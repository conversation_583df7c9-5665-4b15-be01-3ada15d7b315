#!/usr/bin/env python3
"""
测试手机号查询功能的脚本
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:5000"
LOGIN_URL = f"{BASE_URL}/login"
SEARCH_URL = f"{BASE_URL}/customer_history"

def login():
    """登录获取会话"""
    session = requests.Session()
    
    # 先获取登录页面
    response = session.get(LOGIN_URL)
    if response.status_code != 200:
        print(f"获取登录页面失败: {response.status_code}")
        return None
    
    # 尝试登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post(LOGIN_URL, data=login_data)
    if response.status_code == 200 and 'login' not in response.url:
        print("登录成功")
        return session
    else:
        print(f"登录失败: {response.status_code}, URL: {response.url}")
        return None

def test_phone_search(session, phone, description):
    """测试手机号查询"""
    print(f"\n测试 {description}: {phone}")
    
    params = {
        'phone': phone,
        'per_page': 5,
        'page': 1
    }
    
    headers = {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    }
    
    response = session.get(SEARCH_URL, params=params, headers=headers)
    
    if response.status_code == 200:
        try:
            data = response.json()
            if data.get('found'):
                print(f"  ✓ 查询成功")
                print(f"  - 找到订单数: {data.get('pagination', {}).get('total', 0)}")
                print(f"  - 主要客户: {data.get('customer', {}).get('name')} ({data.get('customer', {}).get('phone')})")
                
                matched_customers = data.get('matched_customers', [])
                if len(matched_customers) > 1:
                    print(f"  - 匹配到 {len(matched_customers)} 个客户:")
                    for customer in matched_customers:
                        print(f"    * {customer['name']} ({customer['phone']})")
                elif len(matched_customers) == 1:
                    print(f"  - 精确匹配客户: {matched_customers[0]['name']} ({matched_customers[0]['phone']})")
                
            else:
                error_msg = data.get('error', '未找到匹配的客户')
                print(f"  ✗ 查询失败: {error_msg}")
        except json.JSONDecodeError:
            print(f"  ✗ 响应不是有效的JSON: {response.text[:100]}")
    else:
        print(f"  ✗ 请求失败: {response.status_code}")

def main():
    """主测试函数"""
    print("开始测试手机号查询功能...")
    
    # 登录
    session = login()
    if not session:
        print("登录失败，无法进行测试")
        return
    
    # 测试用例
    test_cases = [
        ("1234", "后四位查询 - 1234"),
        ("5678", "后四位查询 - 5678"),
        ("0000", "后四位查询 - 0000"),
        ("13812345678", "完整手机号查询"),
        ("138", "无效长度查询"),
        ("abcd", "非数字后四位查询"),
        ("", "空查询"),
    ]
    
    for phone, description in test_cases:
        test_phone_search(session, phone, description)
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
