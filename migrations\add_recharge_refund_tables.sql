-- 充值退充和小票补打印功能数据库迁移脚本
-- 执行日期: 2025-01-15
-- 版本: 1.0

-- 1. 为员工表添加退充权限字段
ALTER TABLE staff 
ADD COLUMN can_refund_recharge BOOLEAN DEFAULT FALSE COMMENT '退充权限',
ADD COLUMN max_refund_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '最大退充金额限制';

-- 2. 创建充值退充记录表
CREATE TABLE IF NOT EXISTS recharge_refund (
    id INT PRIMARY KEY AUTO_INCREMENT,
    original_recharge_id INT NOT NULL COMMENT '原充值记录ID',
    customer_id INT NOT NULL COMMENT '客户ID',
    refund_amount DECIMAL(10,2) NOT NULL COMMENT '退充金额',
    refund_gift_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '扣除的赠送金额',
    refund_reason VARCHAR(500) COMMENT '退充原因',
    operator VARCHAR(100) NOT NULL COMMENT '操作员',
    status VARCHAR(20) DEFAULT 'completed' COMMENT '状态：completed, cancelled',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    approved_by VARCHAR(100) COMMENT '审批人（大额退充）',
    
    FOREIGN KEY (original_recharge_id) REFERENCES recharge_record(id) ON DELETE RESTRICT,
    FOREIGN KEY (customer_id) REFERENCES customer(id) ON DELETE RESTRICT,
    
    INDEX idx_original_recharge_id (original_recharge_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_operator (operator),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值退充记录表';

-- 3. 创建小票补打印记录表
CREATE TABLE IF NOT EXISTS receipt_reprint (
    id INT PRIMARY KEY AUTO_INCREMENT,
    record_type VARCHAR(20) NOT NULL COMMENT '记录类型：recharge或refund',
    record_id INT NOT NULL COMMENT '对应记录ID',
    customer_id INT NOT NULL COMMENT '客户ID',
    operator VARCHAR(100) NOT NULL COMMENT '操作员',
    reprint_reason VARCHAR(255) COMMENT '补打印原因',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (customer_id) REFERENCES customer(id) ON DELETE RESTRICT,
    
    INDEX idx_record_type_id (record_type, record_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_operator (operator),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小票补打印记录表';

-- 4. 为现有管理员用户设置退充权限（可选）
-- UPDATE staff SET can_refund_recharge = TRUE, max_refund_amount = 10000.00 WHERE role = 'manager';

-- 5. 创建视图：退充统计视图（可选）
CREATE OR REPLACE VIEW v_refund_statistics AS
SELECT 
    DATE(rr.created_at) as refund_date,
    COUNT(*) as refund_count,
    SUM(rr.refund_amount) as total_refund_amount,
    SUM(rr.refund_gift_amount) as total_gift_refund_amount,
    rr.operator
FROM recharge_refund rr
WHERE rr.status = 'completed'
GROUP BY DATE(rr.created_at), rr.operator;

-- 6. 创建触发器：记录退充操作日志（可选）
DELIMITER $$

CREATE TRIGGER tr_recharge_refund_log 
AFTER INSERT ON recharge_refund
FOR EACH ROW
BEGIN
    INSERT INTO operation_log (
        table_name, 
        operation_type, 
        record_id, 
        operator, 
        operation_data, 
        created_at
    ) VALUES (
        'recharge_refund',
        'INSERT',
        NEW.id,
        NEW.operator,
        JSON_OBJECT(
            'refund_amount', NEW.refund_amount,
            'refund_gift_amount', NEW.refund_gift_amount,
            'customer_id', NEW.customer_id,
            'original_recharge_id', NEW.original_recharge_id
        ),
        NOW()
    );
END$$

DELIMITER ;

-- 注意：如果operation_log表不存在，请先创建该表或注释掉触发器部分

-- 验证迁移结果的查询语句
-- SELECT COUNT(*) as staff_with_refund_permission FROM staff WHERE can_refund_recharge = TRUE;
-- SHOW TABLES LIKE '%refund%';
-- SHOW TABLES LIKE '%reprint%';
-- DESCRIBE recharge_refund;
-- DESCRIBE receipt_reprint;