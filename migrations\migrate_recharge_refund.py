#!/usr/bin/env python3
"""
充值退充和小票补打印功能数据库迁移脚本
执行方式: python migrations/migrate_recharge_refund.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models import db, Staff
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_migration():
    """执行数据库迁移"""
    app = create_app('development')
    with app.app_context():
        try:
            logger.info("开始执行充值退充功能数据库迁移...")
            
            # 1. 检查是否已经迁移过
            try:
                result = db.session.execute(text("SHOW COLUMNS FROM staff LIKE 'can_refund_recharge'"))
                if result.fetchone():
                    logger.warning("检测到已存在退充权限字段，跳过员工表迁移")
                else:
                    # 添加员工退充权限字段
                    logger.info("为员工表添加退充权限字段...")
                    db.session.execute(text("""
                        ALTER TABLE staff 
                        ADD COLUMN can_refund_recharge BOOLEAN DEFAULT FALSE,
                        ADD COLUMN max_refund_amount DECIMAL(10,2) DEFAULT 0.00
                    """))
                    logger.info("员工表字段添加完成")
            except Exception as e:
                logger.error(f"员工表迁移失败: {e}")
                raise
            
            # 2. 创建退充记录表
            try:
                logger.info("创建充值退充记录表...")
                db.session.execute(text("""
                    CREATE TABLE IF NOT EXISTS recharge_refund (
                        id INT PRIMARY KEY AUTO_INCREMENT,
                        original_recharge_id INT NOT NULL,
                        customer_id INT NOT NULL,
                        refund_amount DECIMAL(10,2) NOT NULL,
                        refund_gift_amount DECIMAL(10,2) DEFAULT 0.00,
                        refund_reason VARCHAR(500),
                        operator VARCHAR(100) NOT NULL,
                        status VARCHAR(20) DEFAULT 'completed',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        approved_by VARCHAR(100),
                        
                        FOREIGN KEY (original_recharge_id) REFERENCES recharge_record(id),
                        FOREIGN KEY (customer_id) REFERENCES customer(id),
                        
                        INDEX idx_original_recharge_id (original_recharge_id),
                        INDEX idx_customer_id (customer_id),
                        INDEX idx_operator (operator),
                        INDEX idx_created_at (created_at)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """))
                logger.info("充值退充记录表创建完成")
            except Exception as e:
                logger.error(f"退充记录表创建失败: {e}")
                raise
            
            # 3. 创建补打印记录表
            try:
                logger.info("创建小票补打印记录表...")
                db.session.execute(text("""
                    CREATE TABLE IF NOT EXISTS receipt_reprint (
                        id INT PRIMARY KEY AUTO_INCREMENT,
                        record_type VARCHAR(20) NOT NULL,
                        record_id INT NOT NULL,
                        customer_id INT NOT NULL,
                        operator VARCHAR(100) NOT NULL,
                        reprint_reason VARCHAR(255),
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        
                        FOREIGN KEY (customer_id) REFERENCES customer(id),
                        
                        INDEX idx_record_type_id (record_type, record_id),
                        INDEX idx_customer_id (customer_id),
                        INDEX idx_operator (operator),
                        INDEX idx_created_at (created_at)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """))
                logger.info("小票补打印记录表创建完成")
            except Exception as e:
                logger.error(f"补打印记录表创建失败: {e}")
                raise
            
            # 4. 为管理员用户设置默认退充权限
            try:
                logger.info("为管理员用户设置默认退充权限...")
                managers = Staff.query.filter_by(role='manager').all()
                for manager in managers:
                    if not manager.can_refund_recharge:
                        manager.can_refund_recharge = True
                        manager.max_refund_amount = 10000.00  # 默认最大退充金额
                        logger.info(f"为管理员 {manager.name} 设置退充权限")
                
                logger.info("管理员权限设置完成")
            except Exception as e:
                logger.error(f"管理员权限设置失败: {e}")
                raise
            
            # 提交所有更改
            db.session.commit()
            logger.info("数据库迁移完成！")
            
            # 验证迁移结果
            verify_migration()
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"数据库迁移失败: {e}")
            raise

def verify_migration():
    """验证迁移结果"""
    try:
        logger.info("验证迁移结果...")
        
        # 检查表是否存在
        tables_to_check = ['recharge_refund', 'receipt_reprint']
        for table in tables_to_check:
            result = db.session.execute(text(f"SHOW TABLES LIKE '{table}'"))
            if result.fetchone():
                logger.info(f"✓ 表 {table} 创建成功")
            else:
                logger.error(f"✗ 表 {table} 创建失败")
        
        # 检查员工权限字段
        result = db.session.execute(text("SHOW COLUMNS FROM staff LIKE 'can_refund_recharge'"))
        if result.fetchone():
            logger.info("✓ 员工退充权限字段添加成功")
        else:
            logger.error("✗ 员工退充权限字段添加失败")
        
        # 统计有退充权限的员工数量
        staff_count = Staff.query.filter_by(can_refund_recharge=True).count()
        logger.info(f"✓ 当前有 {staff_count} 名员工拥有退充权限")
        
        logger.info("迁移验证完成！")
        
    except Exception as e:
        logger.error(f"迁移验证失败: {e}")

def rollback_migration():
    """回滚迁移（仅用于开发测试）"""
    app = create_app('development')
    with app.app_context():
        try:
            logger.warning("开始回滚数据库迁移...")
            
            # 删除新创建的表
            db.session.execute(text("DROP TABLE IF EXISTS receipt_reprint"))
            db.session.execute(text("DROP TABLE IF EXISTS recharge_refund"))
            
            # 删除员工表新增字段
            try:
                db.session.execute(text("ALTER TABLE staff DROP COLUMN can_refund_recharge"))
                db.session.execute(text("ALTER TABLE staff DROP COLUMN max_refund_amount"))
            except:
                pass  # 字段可能不存在
            
            db.session.commit()
            logger.warning("数据库迁移回滚完成！")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"回滚失败: {e}")
            raise

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='充值退充功能数据库迁移')
    parser.add_argument('--rollback', action='store_true', help='回滚迁移（仅用于开发测试）')
    args = parser.parse_args()
    
    if args.rollback:
        confirm = input("确定要回滚迁移吗？这将删除所有相关数据！(yes/no): ")
        if confirm.lower() == 'yes':
            rollback_migration()
        else:
            logger.info("回滚操作已取消")
    else:
        run_migration()