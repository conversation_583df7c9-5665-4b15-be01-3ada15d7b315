"""
统一响应格式工具模块
提供标准化的API响应格式
"""
from typing import Any, Dict, Optional, List
from flask import jsonify
import logging


class APIResponse:
    """API响应格式化器"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功", status_code: int = 200) -> tuple:
        """
        成功响应格式
        
        Args:
            data: 响应数据
            message: 成功消息
            status_code: HTTP状态码
            
        Returns:
            tuple: (response, status_code)
        """
        response = {
            'success': True,
            'message': message
        }
        
        if data is not None:
            response['data'] = data
            
        return jsonify(response), status_code
    
    @staticmethod
    def error(message: str, status_code: int = 400, error_code: str = None) -> tuple:
        """
        错误响应格式
        
        Args:
            message: 错误消息
            status_code: HTTP状态码
            error_code: 错误代码
            
        Returns:
            tuple: (response, status_code)
        """
        response = {
            'success': False,
            'error': message
        }
        
        if error_code:
            response['error_code'] = error_code
            
        return jsonify(response), status_code
    
    @staticmethod
    def paginated_success(items: List[Any], total: int, page: int, per_page: int, 
                         message: str = "查询成功") -> tuple:
        """
        分页成功响应格式
        
        Args:
            items: 数据项列表
            total: 总数量
            page: 当前页码
            per_page: 每页数量
            message: 成功消息
            
        Returns:
            tuple: (response, status_code)
        """
        data = {
            'items': items,
            'pagination': {
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page if per_page > 0 else 0
            }
        }
        
        return APIResponse.success(data, message)
    
    @staticmethod
    def validation_error(errors: Dict[str, List[str]] = None, message: str = "数据验证失败") -> tuple:
        """
        验证错误响应格式
        
        Args:
            errors: 字段错误字典
            message: 错误消息
            
        Returns:
            tuple: (response, status_code)
        """
        response = {
            'success': False,
            'error': message
        }
        
        if errors:
            response['validation_errors'] = errors
            
        return jsonify(response), 422
    
    @staticmethod
    def not_found(resource: str = "资源") -> tuple:
        """
        资源未找到响应格式
        
        Args:
            resource: 资源类型
            
        Returns:
            tuple: (response, status_code)
        """
        return APIResponse.error(f"{resource}不存在", 404)
    
    @staticmethod
    def unauthorized(message: str = "未授权访问") -> tuple:
        """
        未授权响应格式
        
        Args:
            message: 错误消息
            
        Returns:
            tuple: (response, status_code)
        """
        return APIResponse.error(message, 401)
    
    @staticmethod
    def forbidden(message: str = "权限不足") -> tuple:
        """
        禁止访问响应格式
        
        Args:
            message: 错误消息
            
        Returns:
            tuple: (response, status_code)
        """
        return APIResponse.error(message, 403)
    
    @staticmethod
    def server_error(message: str = "服务器内部错误") -> tuple:
        """
        服务器错误响应格式
        
        Args:
            message: 错误消息
            
        Returns:
            tuple: (response, status_code)
        """
        return APIResponse.error(message, 500)


def handle_api_error(func):
    """
    API错误处理装饰器
    统一处理异常并返回标准格式响应
    """
    from functools import wraps
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValueError as e:
            logging.error(f"数据验证错误: {str(e)}")
            return APIResponse.error(str(e), 400)
        except PermissionError as e:
            logging.error(f"权限错误: {str(e)}")
            return APIResponse.forbidden(str(e))
        except Exception as e:
            logging.error(f"服务器错误: {str(e)}", exc_info=True)
            return APIResponse.server_error("服务器内部错误，请稍后重试")
    
    return wrapper


class LoggingUtils:
    """日志工具"""
    
    @staticmethod
    def log_api_call(endpoint: str, method: str, user_id: str = None):
        """
        记录API调用日志
        
        Args:
            endpoint: 端点名称
            method: HTTP方法
            user_id: 用户ID
        """
        logging.info(f"API调用: {method} {endpoint} - 用户: {user_id}")
    
    @staticmethod
    def log_business_operation(operation: str, details: Dict[str, Any] = None, user_id: str = None):
        """
        记录业务操作日志
        
        Args:
            operation: 操作名称
            details: 操作详情
            user_id: 用户ID
        """
        log_message = f"业务操作: {operation} - 用户: {user_id}"
        if details:
            log_message += f" - 详情: {details}"
        logging.info(log_message)
    
    @staticmethod
    def log_error(error: Exception, context: str = None):
        """
        记录错误日志
        
        Args:
            error: 异常对象
            context: 错误上下文
        """
        error_message = f"错误: {str(error)}"
        if context:
            error_message += f" - 上下文: {context}"
        logging.error(error_message, exc_info=True) 