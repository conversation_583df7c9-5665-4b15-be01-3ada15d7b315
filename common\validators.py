"""
通用数据验证工具模块
提供统一的数据验证功能，避免重复代码
"""
from typing import Dict, List, Any, Optional, Tuple
import datetime
import re


class ValidationError(Exception):
    """自定义验证异常"""
    def __init__(self, message: str, field: str = None):
        self.message = message
        self.field = field
        super().__init__(message)


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
        """
        验证必填字段
        
        Args:
            data: 要验证的数据字典
            required_fields: 必填字段列表
            
        Raises:
            ValidationError: 当缺少必填字段时
        """
        missing_fields = []
        for field in required_fields:
            if field not in data or not data[field]:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValidationError(f"缺少必填字段: {', '.join(missing_fields)}")
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """
        验证手机号格式
        
        Args:
            phone: 手机号字符串
            
        Returns:
            bool: 是否为有效手机号
        """
        if not phone:
            return False
        # 中国手机号正则表达式
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone))
    
    @staticmethod
    def validate_discount_rate(rate: float) -> None:
        """
        验证折扣率
        
        Args:
            rate: 折扣率
            
        Raises:
            ValidationError: 当折扣率不在有效范围内时
        """
        if rate <= 0 or rate > 1:
            raise ValidationError("折扣率必须在0-1之间")
    
    @staticmethod
    def validate_date_range(start_date: str, end_date: str) -> Tuple[datetime.date, datetime.date]:
        """
        验证日期范围
        
        Args:
            start_date: 开始日期字符串 (YYYY-MM-DD)
            end_date: 结束日期字符串 (YYYY-MM-DD)
            
        Returns:
            Tuple[datetime.date, datetime.date]: 解析后的日期对象
            
        Raises:
            ValidationError: 当日期格式错误或范围无效时
        """
        try:
            start = datetime.datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            raise ValidationError("日期格式错误，请使用 YYYY-MM-DD 格式")
        
        if start >= end:
            raise ValidationError("开始日期必须早于结束日期")
        
        return start, end
    
    @staticmethod
    def validate_service_type(service_type: str) -> None:
        """
        验证服务类型
        
        Args:
            service_type: 服务类型
            
        Raises:
            ValidationError: 当服务类型无效时
        """
        valid_types = ['洗衣', '织补', '改衣', '其他']
        if service_type not in valid_types:
            raise ValidationError(f"无效的服务类型，支持的类型: {', '.join(valid_types)}")
    
    @staticmethod
    def validate_amount(amount: float, field_name: str = "金额") -> None:
        """
        验证金额
        
        Args:
            amount: 金额
            field_name: 字段名称用于错误信息
            
        Raises:
            ValidationError: 当金额无效时
        """
        if amount < 0:
            raise ValidationError(f"{field_name}不能为负数")
        if amount > 999999.99:
            raise ValidationError(f"{field_name}超出允许范围")
    
    @staticmethod
    def validate_mall_name(name: str) -> str:
        """
        验证并清理商场名称
        
        Args:
            name: 商场名称
            
        Returns:
            str: 清理后的名称
            
        Raises:
            ValidationError: 当名称无效时
        """
        if not name:
            raise ValidationError("商场品牌名称不能为空")
        
        cleaned_name = name.strip()
        if not cleaned_name:
            raise ValidationError("商场品牌名称不能为空")
        
        if len(cleaned_name) > 100:
            raise ValidationError("商场品牌名称不能超过100个字符")
        
        return cleaned_name
    
    @staticmethod
    def validate_password(password: str, is_new_user: bool = False) -> None:
        """
        验证密码
        
        Args:
            password: 密码
            is_new_user: 是否为新用户
            
        Raises:
            ValidationError: 当密码无效时
        """
        if is_new_user and not password:
            raise ValidationError("新建用户必须设置密码")
        
        if password and len(password) < 6:
            raise ValidationError("密码长度不能少于6位")


def validate_data(validator_func):
    """
    数据验证装饰器
    
    Args:
        validator_func: 验证函数，接收 request.json 参数
    """
    def decorator(f):
        def wrapper(*args, **kwargs):
            try:
                from flask import request
                data = request.json or {}
                validator_func(data)
                return f(*args, **kwargs)
            except ValidationError as e:
                from flask import jsonify
                return jsonify({'error': e.message}), 400
        return wrapper
    return decorator 