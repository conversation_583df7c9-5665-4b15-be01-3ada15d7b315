<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>不干胶打印 - 快洗管理系统</title>
    <!-- 引入Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.2.0/css/all.min.css" rel="stylesheet">
    
    <!-- Lodop打印控件脚本 -->
    <script language="javascript" src="https://www.lodop.net/download/CLodop_Setup_For_Win32NT.exe" id="lodop32"></script>
    <script language="javascript" src="https://www.lodop.net/download/CLodop_Setup_For_Win32NT.exe" id="lodop64"></script>
    <script language="javascript" src="/static/js/LodopFuncs.js"></script>
    <script src="/static/js/lodop-print.js"></script>
    <script src="/static/js/print-functions.js"></script>
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .container-fluid {
            padding: 0 20px;
        }
        .main-content {
            padding: 0 20px;
        }
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }
        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background-color: #343a40;
            color: white;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .site-title {
            font-size: 1.5rem;
            margin: 0;
            color: white;
        }
        .user-info {
            padding: 8px 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            font-size: 0.9em;
            color: white;
        }
        .user-info a {
            color: white;
            text-decoration: none;
            margin-left: 10px;
        }
        .user-info a:hover {
            text-decoration: underline;
        }
        .content-wrapper {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .sticky-label {
            width: 72mm;
            height: 50mm;
            /* border: 1px solid #ccc; */ /* 去掉边框，保持简洁样式 */
            padding: 3mm;
            margin: 10px auto;
            background-color: white;
            font-size: 16px; /* 从12px增大到16px (约33%增长) */
            box-sizing: border-box;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .sticky-label .label-header {
            text-align: center;
            margin-bottom: 2mm;
            flex-shrink: 0;
        }
        .sticky-label .store-name {
            font-size: 21px; /* 从16px增大到21px (约30%增长) */
            font-weight: bold;
            line-height: 1.0;
            margin: 0;
        }
        .sticky-label .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px; /* 从10px增大到13px (约30%增长) */
            font-weight: bold;
            line-height: 1.0;
            margin: 1mm 0;
            width: 100%;
        }
        .sticky-label .info-left {
            text-align: left;
            flex: 1;
        }
        .sticky-label .info-right {
            text-align: right;
            flex: 1;
        }
        .sticky-label .items-section {
            display: none; /* 隐藏衣物详情区域，信息已整合到info-row中 */
        }
        .sticky-label .item-info {
            display: none; /* 隐藏单独的衣物信息 */
        }
        .sticky-label .badge {
            border: 1px solid #000;
            padding: 1px 3px;
            margin-right: 2px;
            font-weight: bold;
            font-size: 10px; /* 从8px增大到10px (约25%增长) */
            line-height: 1.0;
            display: inline-block;
        }
        .sticky-label .badge-accessory {
            background-color: #fff;
            color: #000;
        }
        .sticky-label .badge-urgent {
            background-color: #fff;
            color: #000;
        }
        .sticky-label .barcode-section {
            text-align: center;
            flex-shrink: 0;
            margin-top: 2mm;
        }
        #scanner-container {
            position: relative;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }
        #scanner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }
        #scanner-message {
            color: white;
            font-size: 18px;
            text-align: center;
            padding: 20px;
        }
        #quagga-viewport {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        .info-badge {
            background-color: #e9f5ff;
            border-left: 3px solid #007bff;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .status-badge {
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .btn-submit {
            padding: 10px 20px;
            font-size: 16px;
        }
        .result-container {
            margin-top: 20px;
            display: none;
        }
        #order-number-input {
            font-size: 16px;
            padding: 10px;
        }
        .nav-links {
            display: flex;
            gap: 15px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
        .item-info.highlighted {
            background-color: #fffde7;
            border-left: 3px solid #ffc107;
            padding-left: 5px;
            font-weight: bold;
        }
        .display-option {
            margin-bottom: 15px;
        }
        @media print {
            @page {
                size: 72mm 50mm;
                margin: 0;
            }

            * {
                margin: 0 !important;
                padding: 0 !important;
                box-sizing: border-box !important;
            }

            body {
                margin: 0 !important;
                padding: 0 !important;
                width: 72mm !important;
                height: 50mm !important;
                overflow: hidden !important;
            }

            body * {
                visibility: hidden !important;
            }

            .sticky-label, .sticky-label * {
                visibility: visible !important;
            }

            .sticky-label {
                position: fixed !important;
                left: 0 !important;
                top: 0 !important;
                width: 72mm !important;
                height: 50mm !important;
                margin: 0 !important;
                padding: 2mm !important;
                /* border: 1px solid #000 !important; */ /* 去掉打印时的边框 */
                box-sizing: border-box !important;
                overflow: hidden !important;
                font-size: 13px !important; /* 从10px增大到13px (约30%增长) */
                display: flex !important;
                flex-direction: column !important;
                justify-content: space-between !important;
                background: white !important;
                z-index: 9999 !important;
            }

            .sticky-label .info-row {
                margin: 0.5mm 0 !important;
                line-height: 1.1 !important;
                font-size: 13px !important; /* 确保info-row字体大小 */
            }

            .sticky-label .store-name {
                font-size: 18px !important; /* 从14px增大到18px (约30%增长) */
                margin-bottom: 1mm !important;
            }

            .sticky-label .badge {
                font-size: 10px !important; /* 确保徽章字体大小 */
            }

            .sticky-label .barcode-section {
                margin-top: 1mm !important;
            }

            .no-print {
                display: none !important;
                visibility: hidden !important;
            }

            /* 确保所有容器元素不影响打印布局 */
            .container-fluid, .row, .col-12, .main-content,
            .content-wrapper, .content-body, .card, .card-body {
                all: unset !important;
                display: block !important;
                visibility: hidden !important;
            }

            #label-container {
                all: unset !important;
                display: block !important;
                visibility: hidden !important;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 顶部导航栏 -->
            <div class="col-12 no-print">
                <div class="top-bar">
                    <h2 class="site-title">快洗管理系统</h2>
                    <div class="nav-links">
                        <a href="/"><i class="fas fa-home"></i> 首页</a>
                        <a href="/history"><i class="fas fa-history"></i> 订单查询</a>
                        <a href="/data_summary"><i class="fas fa-chart-bar"></i> 数据汇总</a>
                        <a href="/rack" style="color: #ff6b35;"><i class="fas fa-th"></i> 格架管理</a>
                    </div>
                    <div class="user-info">
                        欢迎，{{ staff_name }}
                        <a href="/logout"><i class="fas fa-sign-out-alt"></i> 退出</a>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-12 main-content">
                <div class="content-wrapper">
                    <div class="content-header no-print">
                        <h1><i class="fas {{ icon }}"></i> {{ title }}</h1>
                    </div>

                    <div class="content-body">
                        <div class="alert alert-info no-print">
                            <i class="fas fa-info-circle"></i> 输入订单号或扫描水洗唛条码获取标签信息
                        </div>

                        <div class="info-badge no-print">
                            <i class="fas fa-tag"></i> 支持扫描格式：<br>
                            - 订单号：如 "100004"（显示订单所有衣物）<br>
                            - 水洗唛条码：如 "100004-01"（订单号-衣物序号，可以单独显示或高亮某件衣物）
                        </div>

                        <!-- 扫码区域 -->
                        <div id="scanner-container" class="no-print">
                            <div id="scanner-overlay">
                                <div id="scanner-message">
                                    <p>正在初始化扫码器...</p>
                                    <button id="start-scanner" class="btn btn-primary" disabled>开始扫码</button>
                                </div>
                            </div>
                            <div id="quagga-viewport"></div>
                        </div>

                        <!-- 订单号手动输入 -->
                        <div class="form-container no-print mt-4">
                            <form id="orderSearchForm">
                                <div class="input-group mb-3">
                                    <input type="text" id="order-number-input" class="form-control" placeholder="请输入订单号或水洗唛条码" autofocus>
                                    <button class="btn btn-primary btn-submit" type="submit" id="search-button">
                                        <i class="fas fa-search"></i> 查找
                                    </button>
                                    <button class="btn btn-outline-secondary" type="button" id="scan-button">
                                        <i class="fas fa-barcode"></i> 扫码
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- 结果提示区域 -->
                        <div class="result-container" id="resultContainer">
                            <div class="alert" id="alertBox" role="alert"></div>
                        </div>

                        <!-- 打印标签预览 -->
                        <div class="card mt-3">
                            <div class="card-header no-print">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5>标签预览</h5>
                                    <div id="displayOptions" class="display-option" style="display:none;">
                                        <div class="btn-group" role="group">
                                            <button id="showAll" class="btn btn-sm btn-outline-primary active">显示全部</button>
                                            <button id="showSingle" class="btn btn-sm btn-outline-primary">只显示当前衣物</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="label-container">
                                    <!-- 标签内容将通过JavaScript动态生成 -->
                                </div>
                                <div class="text-center mt-3 no-print">
                                    <button id="print-button" class="btn btn-success" disabled>
                                        <i class="fas fa-print"></i> 打印标签
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 历史记录 -->
                        <div class="card mt-3 no-print">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5>最近打印记录</h5>
                                <button id="refresh-history" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-sync-alt"></i> 刷新
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>订单号</th>
                                                <th>会员姓名</th>
                                                <th>打印时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="history-table">
                                            <!-- 历史记录将通过JavaScript动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/quagga@0.12.1/dist/quagga.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script>
        // 存储打印历史记录
        let printHistory = [];
        // 存储当前标签数据
        let currentLabelData = null;
        // 存储当前选中的衣物索引
        let currentClothingIndex = null;
        // 标记是否只显示当前衣物
        let showSingleItem = false;
        
        // 初始化扫码器
        function initScanner() {
            // 检查浏览器是否支持扫码
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                $('#scanner-message').html('<p>您的浏览器不支持扫码功能，请使用手动输入。</p>');
                return;
            }
            
            $('#scanner-message').html('<p>点击下面的按钮开始扫码</p><button id="start-scanner" class="btn btn-primary">开始扫码</button>');
            
            // 绑定开始扫码按钮点击事件
            $(document).on('click', '#start-scanner', function() {
                startScanner();
            });
        }
        
        // 启动扫码器
        function startScanner() {
            $('#scanner-overlay').hide();
            
            Quagga.init({
                inputStream: {
                    name: "Live",
                    type: "LiveStream",
                    target: document.querySelector('#quagga-viewport'),
                    constraints: {
                        width: 480,
                        height: 320,
                        facingMode: "environment"
                    },
                },
                decoder: {
                    readers: [
                        "code_128_reader",
                        "ean_reader",
                        "ean_8_reader",
                        "code_39_reader",
                        "code_39_vin_reader",
                        "codabar_reader",
                        "upc_reader",
                        "upc_e_reader",
                        "i2of5_reader"
                    ],
                    debug: {
                        showCanvas: true,
                        showPatches: true,
                        showFoundPatches: true,
                        showSkeleton: true,
                        showLabels: true,
                        showPatchLabels: true,
                        showRemainingPatchLabels: true,
                        boxFromPatches: {
                            showTransformed: true,
                            showTransformedBox: true,
                            showBB: true
                        }
                    }
                },
                locator: {
                    patchSize: "medium",
                    halfSample: true
                },
                numOfWorkers: 4,
                frequency: 10,
                debug: false
            }, function(err) {
                if (err) {
                    console.error("Quagga初始化错误:", err);
                    $('#scanner-overlay').show();
                    $('#scanner-message').html('<p>摄像头初始化失败，请检查摄像头权限或使用手动输入。</p>');
                    return;
                }
                
                Quagga.start();
            });
            
            // 处理扫码结果
            Quagga.onDetected(function(result) {
                if (result && result.codeResult) {
                    const barcode = result.codeResult.code;
                    console.log("扫码结果:", barcode);
                    
                    // 停止扫码
                    Quagga.stop();
                    $('#scanner-overlay').show();
                    
                    // 解析条码，可能是水洗唛格式
                    const barcodeInfo = parseBarcode(barcode);
                    
                    // 显示扫码结果
                    if (barcodeInfo.isClothingBarcode) {
                        $('#scanner-message').html(`<p>扫码成功！水洗唛条码: ${barcode}<br>已提取订单号: ${barcodeInfo.orderNumber}, 衣物索引: ${barcodeInfo.clothingIndex}</p><button id="start-scanner" class="btn btn-primary">再次扫码</button>`);
                    } else {
                        $('#scanner-message').html(`<p>扫码成功！订单号: ${barcode}</p><button id="start-scanner" class="btn btn-primary">再次扫码</button>`);
                    }
                    
                    // 获取订单信息 - 同时传递订单号和衣物索引
                    getLabelData(barcodeInfo.orderNumber, barcodeInfo.isClothingBarcode ? barcodeInfo.clothingIndex : null);
                }
            });
        }
        
        // 获取标签数据
        function getLabelData(orderNumber, clothingIndex = null) {
            // 保存当前衣物索引
            currentClothingIndex = clothingIndex;
            
            // 显示加载中提示
            showAlert('正在获取订单数据...', 'info');
            
            // 构建API URL
            let apiUrl = '/api/order_label/' + orderNumber;
            
            // 如果有衣物索引参数，添加到URL查询参数中
            if (clothingIndex !== null) {
                apiUrl += '?clothingIndex=' + clothingIndex;
            }
            
            $.ajax({
                url: apiUrl,
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        // 保存标签数据
                        currentLabelData = response.label_data;
                        
                        // 渲染标签
                        renderLabel(response.label_data, clothingIndex);
                        
                        // 如果是指定衣物，显示切换选项
                        if (clothingIndex !== null) {
                            $('#displayOptions').show();
                            // 设置为显示全部模式（但高亮当前衣物）
                            showSingleItem = false;
                            $('#showAll').addClass('active');
                            $('#showSingle').removeClass('active');
                        } else {
                            $('#displayOptions').hide();
                        }
                        
                        // 启用打印按钮
                        $('#print-button').prop('disabled', false);
                        
                        // 添加到历史记录
                        addToPrintHistory(response.label_data, clothingIndex);
                        
                        // 显示成功消息
                        if (clothingIndex !== null) {
                            showAlert(`标签信息获取成功（衣物索引: ${clothingIndex}），可以打印了`, 'success');
                        } else {
                            showAlert('标签信息获取成功，可以打印了', 'success');
                        }

                        // === 自动触发Lodop不干胶打印 ===
                        if (typeof window.lodopPrintStickyLabel === 'function') {
                            try {
                                // 延迟少许，确保预览已渲染
                                setTimeout(function(){
                                    window.lodopPrintStickyLabel(orderNumber);
                                }, 300);
                            } catch (e) {
                                console.error('自动调用 Lodop 不干胶打印失败:', e);
                            }
                        } else {
                            console.warn('window.lodopPrintStickyLabel 未定义，无法自动打印');
                        }
                    } else {
                        showAlert('获取订单信息失败: ' + response.error, 'danger');
                    }
                },
                error: function(xhr) {
                    const errorMessage = xhr.responseJSON ? xhr.responseJSON.error : '服务器错误';
                    showAlert('获取订单信息失败: ' + errorMessage, 'danger');
                }
            });
        }
        
        // 渲染标签
        function renderLabel(data, clothingIndex = null) {
            const container = $('#label-container');
            container.empty();
            
            // 确定需要显示的衣物
            let clothesToShow = data.clothes;
            
            // 如果指定了衣物索引并且设置为只显示当前衣物，则过滤
            if (clothingIndex !== null && showSingleItem) {
                // 索引是从1开始的，但数组索引是从0开始，所以需要减1
                const adjustedIndex = clothingIndex - 1;
                if (adjustedIndex >= 0 && adjustedIndex < data.clothes.length) {
                    clothesToShow = [data.clothes[adjustedIndex]];
                }
            }
            
            // 从订单号中提取日期信息（假设订单号前6位是日期，格式为YYMMDD）
            let orderDate = '';
            if (data.order_number && data.order_number.length >= 6) {
                const orderPrefix = data.order_number.substring(0, 6);
                // 匹配格式为YYMMDD的订单号
                if (/^\d{6}$/.test(orderPrefix)) {
                    const year = '20' + orderPrefix.substring(0, 2); // 假设年份是20xx年
                    const month = orderPrefix.substring(2, 4);
                    const day = orderPrefix.substring(4, 6);
                    orderDate = `${year}-${month}-${day}`;
                }
            }
            
            // 格式化日期显示（YYMMDD格式）
            let formattedDate = '';
            if (data.date) {
                const date = new Date(data.date);
                const year = date.getFullYear().toString().substr(-2);
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                formattedDate = `${year}${month}${day}`;
            }

            // 创建标签HTML - 按照图片示例布局
            const labelHtml = `
                <div class="sticky-label">
                    <div class="label-header">
                        <div class="store-name">${data.operator || '未知营业员'}</div>
                    </div>

                    <div class="info-row">
                        <div class="info-left">单号: ${data.order_number}</div>
                        <div class="info-right">日期: ${formattedDate}</div>
                    </div>

                    <div class="info-row">
                        <div class="info-left">电话: ${data.customer_phone && String(data.customer_phone).trim() ? String(data.customer_phone) : '未知'}</div>
                        <div class="info-right">
                            ${data.clothes.map((item, index) => {
                                // 只显示要显示的衣物
                                if (showSingleItem && clothingIndex !== null && index !== (clothingIndex - 1)) {
                                    return '';
                                }

                                return `${item.has_accessory === 'true' ? '<span class="badge badge-accessory">配</span>' : ''}${item.is_urgent === 'true' ? '<span class="badge badge-urgent">急</span>' : ''}`;
                            }).join('')}
                        </div>
                    </div>

                    <div class="info-row">
                        <div class="info-left">客户: ${data.customer_name || '未知'}</div>
                        <div class="info-right">总件数: ${showSingleItem && clothingIndex !== null ? `${clothingIndex}/1` : `${data.total_count}`}</div>
                    </div>

                    <div class="info-row">
                        <div class="info-left">衣物: ${data.clothes.map((item, index) => {
                            // 只显示要显示的衣物
                            if (showSingleItem && clothingIndex !== null && index !== (clothingIndex - 1)) {
                                return '';
                            }
                            return `${index + 1}.${item.name}`;
                        }).filter(name => name).join(', ')}</div>
                        <div class="info-right"></div>
                    </div>

                    <div class="barcode-section">
                        <svg id="barcode-${data.order_number}"></svg>
                    </div>
                </div>
            `;
            
            container.html(labelHtml);
            
            // 生成条形码
            JsBarcode(`#barcode-${data.order_number}`, data.order_number, {
                format: "CODE128",
                width: 2,
                height: 30,
                displayValue: true,
                fontSize: 16, /* 从12px增大到16px (约33%增长) */
                margin: 2
            });
        }
        
        // 添加到打印历史
        function addToPrintHistory(data, clothingIndex = null) {
            // 限制历史记录数量为10条
            if (printHistory.length >= 10) {
                printHistory.pop();
            }
            
            // 添加新记录到开头
            const now = new Date();
            const record = {
                orderNumber: data.order_number,
                customerName: data.customer_name,
                timestamp: now.toLocaleString(),
                clothingIndex: clothingIndex
            };
            printHistory.unshift(record);
            
            // 更新历史表格
            updateHistoryTable();
        }
        
        // 更新历史表格
        function updateHistoryTable() {
            const table = $('#history-table');
            table.empty();
            
            if (printHistory.length === 0) {
                table.append('<tr><td colspan="4" class="text-center">暂无记录</td></tr>');
                return;
            }
            
            printHistory.forEach(record => {
                // 准备订单号显示
                let orderDisplay = record.orderNumber;
                if (record.clothingIndex !== null) {
                    orderDisplay += ` <span class="badge bg-info">衣物 ${record.clothingIndex}</span>`;
                }
                
                const row = `
                    <tr>
                        <td>${orderDisplay}</td>
                        <td>${record.customerName}</td>
                        <td>${record.timestamp}</td>
                        <td>
                            <button class="btn btn-sm btn-info reprint-btn" 
                                data-order="${record.orderNumber}" 
                                data-clothing-index="${record.clothingIndex || ''}">
                                <i class="fas fa-print"></i> 重新打印
                            </button>
                        </td>
                    </tr>
                `;
                table.append(row);
            });
            
            // 绑定重新打印按钮事件
            $('.reprint-btn').off('click').on('click', function() {
                const orderNumber = $(this).data('order');
                const clothingIndex = $(this).data('clothing-index') || null;
                getLabelData(orderNumber, clothingIndex);
            });
        }
        
        // 显示提示信息
        function showAlert(message, type) {
            const alertBox = $('#alertBox');
            alertBox.text(message);
            alertBox.removeClass('alert-success alert-danger alert-info');
            alertBox.addClass('alert-' + type);
            
            $('#resultContainer').show();
        }
        
        // 解析条码
        function parseBarcode(barcode) {
            // 检查是否是水洗唛条码格式（如"100004-01"）
            const regex = /^(\d+)-(\d+)$/;
            const match = barcode.match(regex);
            
            if (match) {
                return {
                    isClothingBarcode: true,
                    orderNumber: match[1],
                    clothingIndex: parseInt(match[2])
                };
            } else {
                return {
                    isClothingBarcode: false,
                    orderNumber: barcode
                };
            }
        }
        
        // 文档加载完成
        $(document).ready(function() {
            // 初始化扫码器
            initScanner();
            
            // 绑定表单提交事件
            $('#orderSearchForm').on('submit', function(e) {
                e.preventDefault();
                const barcode = $('#order-number-input').val().trim();
                
                if (!barcode) {
                    showAlert('请输入订单号或水洗唛条码', 'danger');
                    return;
                }
                
                // 解析可能的水洗唛条码
                const barcodeInfo = parseBarcode(barcode);
                const orderNumber = barcodeInfo.orderNumber;
                
                // 提示用户，如果是水洗唛条码
                if (barcodeInfo.isClothingBarcode) {
                    showAlert(`已从水洗唛条码 ${barcode} 提取订单号: ${orderNumber}, 衣物索引: ${barcodeInfo.clothingIndex}`, 'info');
                }
                
                // 获取订单信息 - 传递订单号和衣物索引
                getLabelData(
                    orderNumber, 
                    barcodeInfo.isClothingBarcode ? barcodeInfo.clothingIndex : null
                );
                
                // 清空输入框
                $('#order-number-input').val('');
            });
            
            // 绑定扫码按钮事件
            $('#scan-button').on('click', function() {
                $('#order-number-input').focus();
                if ($('#scanner-container').is(':visible')) {
                    startScanner();
                } else {
                    alert('请点击"开始扫码"按钮');
                }
            });
            
            // 绑定订单号输入框回车事件
            $('#order-number-input').on('keypress', function(e) {
                if (e.which === 13) { // Enter键
                    e.preventDefault();
                    $('#orderSearchForm').submit();
                }
            });
            
            // 绑定打印按钮点击事件
            $('#print-button').on('click', function() {
                printLabelWithModeSelection();
            });

            // 打印标签函数 - 支持模式选择
            function printLabelWithModeSelection() {
                if (!currentLabelData) {
                    showAlert('没有可打印的标签数据', 'warning');
                    return;
                }

                // 调用统一的打印模式选择功能
                if (typeof window.printStickyLabelWithModeSelection === 'function') {
                    window.printStickyLabelWithModeSelection(currentLabelData.order_number);
                } else {
                    // 降级到浏览器打印
                    printLabelBrowser();
                }
            }

            // 浏览器打印标签函数（原有功能保持）
            function printLabelBrowser() {
                if (!currentLabelData) {
                    showAlert('没有可打印的标签数据', 'warning');
                    return;
                }

                // 确保标签在打印前正确显示
                const stickyLabel = document.querySelector('.sticky-label');
                if (stickyLabel) {
                    // 临时设置打印样式
                    stickyLabel.style.position = 'fixed';
                    stickyLabel.style.left = '0';
                    stickyLabel.style.top = '0';
                    stickyLabel.style.width = '72mm';
                    stickyLabel.style.height = '50mm';
                    stickyLabel.style.padding = '3mm';
                    stickyLabel.style.fontSize = '12px';
                    stickyLabel.style.zIndex = '9999';
                    stickyLabel.style.background = 'white';
                }

                // 延迟一下确保样式生效
                setTimeout(() => {
                    window.print();
                }, 100);
            }

            // 原有的printLabel函数保留，供重新打印按钮使用
            function printLabel() {
                printLabelBrowser();
            }
            
            // 绑定刷新历史记录按钮
            $('#refresh-history').on('click', function() {
                updateHistoryTable();
            });
            
            // 绑定显示选项切换事件
            $('#showAll').on('click', function() {
                showSingleItem = false;
                $(this).addClass('active');
                $('#showSingle').removeClass('active');
                // 重新渲染标签
                if (currentLabelData) {
                    renderLabel(currentLabelData, currentClothingIndex);
                }
            });
            
            $('#showSingle').on('click', function() {
                showSingleItem = true;
                $(this).addClass('active');
                $('#showAll').removeClass('active');
                // 重新渲染标签
                if (currentLabelData) {
                    renderLabel(currentLabelData, currentClothingIndex);
                }
            });
        });
    </script>
</body>
</html> 