/**
 * Lodop打印集成模块
 * 统一处理水洗唛、不干胶标签、小票的Lodop打印功能
 * 版本: 1.0.0
 * 创建日期: 2024-03-15
 */

// Lodop打印集成模块
console.log('Lodop打印集成模块已加载');

// 全局变量
let LODOP_INSTANCE = null;

// 获取Lodop实例
function getLodopInstance() {
    if (window.LODOP && window.LODOP.VERSION) {
        LODOP_INSTANCE = window.LODOP;
        // === Lodop 授权注册 ===
        try {
            LODOP_INSTANCE.SET_LICENSES("","B9D8783FE186D9D21B7E0DAC276BF5CB","","");
        } catch (e) {
            console.warn("LODOP.SET_LICENSES 调用失败：", e);
        }
        return LODOP_INSTANCE;
    }
    
    if (typeof CLodop !== 'undefined' && CLodop) {
        LODOP_INSTANCE = CLodop();
        // === Lodop 授权注册 ===
        try {
            LODOP_INSTANCE.SET_LICENSES("","B9D8783FE186D9D21B7E0DAC276BF5CB","","");
        } catch (e) {
            console.warn("LODOP.SET_LICENSES 调用失败：", e);
        }
        return LODOP_INSTANCE;
    }
    
    return null;
}

// 检查Lodop是否可用
function checkLodopAvailable() {
    const lodop = getLodopInstance();
    if (!lodop) {
        // alert('打印控件未安装或未正确加载，请先安装Lodop打印控件');
        console.error('打印控件未安装或未正确加载，请先安装Lodop打印控件');
        return false;
    }
    return true;
}

/**
 * 水洗唛标签打印 (101mm × 16mm) - 根据图片示例优化布局
 * @param {Object} orderData 订单数据
 * @param {Array} selectedItems 选中的衣物项目，如果为空则打印全部
 */
function printWashLabels(orderData, selectedItems = null) {
    if (!checkLodopAvailable()) return;

    try {
        // 添加调试信息
        console.log("printWashLabels被调用，参数:", {
            orderData: orderData,
            selectedItems: selectedItems,
            orderDataClothes: orderData?.clothes,
            clothesLength: orderData?.clothes?.length
        });
        
        // 确定要打印的衣物
        const itemsToPrint = selectedItems || orderData.clothes || [];
        
        console.log("计算后的itemsToPrint:", {
            itemsToPrint: itemsToPrint,
            length: itemsToPrint.length,
            isArray: Array.isArray(itemsToPrint)
        });
        
        if (!itemsToPrint || itemsToPrint.length === 0) {
            console.error('没有要打印的衣物项目');
            // alert('没有要打印的衣物项目');
            return;
        }

        // 初始化打印任务
        const LODOP = getLodopInstance();
        LODOP.PRINT_INIT("水洗唛标签打印");
        
        // 设置纸张大小 - 确保使用正确的101mm × 16mm尺寸
        LODOP.SET_PRINT_PAGESIZE(1, "101mm", "16mm", "");
        
        // 设置打印模式
        LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
        LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREIEW", true);

        // 在 printWashLabels 函数开头（itemsToPrint.forEach 之前）插入总件数计算
        const totalCount = orderData.clothes ? orderData.clothes.reduce((acc, itm) => acc + (itm.quantity || 1), 0) : itemsToPrint.length;

        // 为每个衣物项目生成水洗唛
        itemsToPrint.forEach((item, index) => {
            if (index > 0) {
                LODOP.NEWPAGE(); // 新建页面
            }
            
            // 取消外边框，保持留白 0.5mm 但不绘制线框
            
            // 左侧条形码区域 (约40mm宽)
            // 条形码
            const barcodeContent = `${orderData.order_number}-${String(index + 1).padStart(2, '0')}`;
            LODOP.ADD_PRINT_BARCODE(
                "2mm",   // Top - 距顶部1.5mm
                "3mm",     // Left - 距左边1mm  
                "38mm",    // Width - 条形码宽度38mm
                "12mm",    // Height - 条形码高度12mm
                "128A",    // 条码类型
                barcodeContent
            );
            
            // 设置条码样式
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 6);
            LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 1); // 显示条码文字
            
            // 右侧信息区域 (从40mm开始到99mm)
            // 绘制信息区域分隔线
            LODOP.ADD_PRINT_LINE("0.5mm", "40mm", "15.5mm", "40mm", 0, 1);
            
            // 第一行：衣物名称和数量、价格
            const itemName = `${item.name || '未知'}${item.color ? `(${item.color})` : ''}`;
            const quantity = item.quantity || 1;
            const priceText = `¥${(item.price || 0).toFixed(2)} ${index + 1}/${totalCount}`;
            
            // 衣物名称 (左侧)
            LODOP.ADD_PRINT_TEXT("1mm", "41mm", "35mm", "3.5mm", itemName);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            
            // 价格信息 (右侧)
            LODOP.ADD_PRINT_TEXT("1mm", "76mm", "23mm", "3.5mm", priceText);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 3); // 右对齐
            
            // 第二行：客户信息
            const phoneDisplay = orderData.customer_phone ? 
                `电: ${orderData.customer_phone.substr(0, 3)}****${orderData.customer_phone.substr(-4)}` : 
                '电: 未知';
            const customerName = orderData.customer_name || '未知';
            const customerInfo = `${phoneDisplay} ${customerName}`;
            
            LODOP.ADD_PRINT_TEXT("4.5mm", "41mm", "58mm", "3mm", customerInfo);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 7);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            
            // 第三行：订单号和日期
            const dateFormatted = orderData.date ? 
                orderData.date.split(' ')[0].replace(/-/g, '').substr(2) : 
                '未知';
            const orderInfo = `营: ${orderData.operator || '未知'} 下单: ${dateFormatted}`;
            
            LODOP.ADD_PRINT_TEXT("7.5mm", "41mm", "58mm", "3mm", orderInfo);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 7);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            
            // ======= 服务与备注 =======
            // 主服务字符串（去掉"加急"）
            let serviceStr = '洗衣';
            if (item.services && Array.isArray(item.services)) {
                const mainServices = item.services.filter(s => s !== '加急');
                if (mainServices.length > 0) {
                    serviceStr = mainServices.join('/');
                }
            }

            // 基础字符串（含服务）
            let serviceAndRemarks = serviceStr;

            // 加急标记
            if (item.services && item.services.includes('加急')) {
                serviceAndRemarks += ' [急]';
            }

            // 配饰标记
            if (item.name && (item.name.includes('配饰') || item.name.includes('饰品'))) {
                serviceAndRemarks += ' [配]';
            }

            // 汇总备注信息
            const remarks = [];
            if (item.flaw && item.flaw.trim()) remarks.push(`瑕疵:${item.flaw.trim()}`);
            if (item.remarks && item.remarks.trim()) remarks.push(item.remarks.trim());
            
            if (remarks.length > 0) {
                serviceAndRemarks += ` 备注: ${remarks.join(' ')}`;
            }

            // 文本截断
            if (serviceAndRemarks.length > 60) {
                serviceAndRemarks = serviceAndRemarks.substring(0,57)+'...';
            }
            
            LODOP.ADD_PRINT_TEXT("10.5mm", "41mm", "58mm", "4mm", serviceAndRemarks);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 6);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            
            // 急件标志 (在右上角显示)
            if (item.services && item.services.some(s => s.includes('急件') || s.includes('加急'))) {
                LODOP.ADD_PRINT_TEXT("1mm", "95mm", "5mm", "3mm", "[急]");
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                LODOP.SET_PRINT_STYLEA(0, "FontColor", "#FF0000");
            }
        });
        
        // === 静默打印到指定打印机 ===
        const printerNames = ["sxm", "Gprinter GP-1824T"];
        let printerSet = false;
        
        for (const printerName of printerNames) {
            try {
                const result = LODOP.SET_PRINTER_INDEXA(printerName);
                if (result) {
                    console.log(`成功设置水洗唛打印机: ${printerName}`);
                    printerSet = true;
                    break;
                } else {
                    console.warn(`打印机 ${printerName} 不存在或无法使用`);
                }
            } catch (e) {
                console.warn(`尝试设置打印机 ${printerName} 失败:`, e);
            }
        }
        
        if (!printerSet) {
            console.error("未找到指定的水洗唛打印机，取消打印操作");
            return;
        }

        // 直接打印（静默，不弹出预览窗口）
        LODOP.PRINT();
        
    } catch (error) {
        console.error('水洗唛打印失败:', error);
        // alert('水洗唛打印失败: ' + error.message);
    }
}

/**
 * 不干胶标签打印 (72mm × 50mm)
 * @param {Object} labelData 标签数据
 */
function printStickyLabel(labelData) {
    if (!checkLodopAvailable()) return;

    try {
        const LODOP = getLodopInstance();
        LODOP.PRINT_INIT("不干胶标签打印");
        LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
        LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREVIEW", true);

        // 纸张尺寸 72mm×50mm
        LODOP.SET_PRINT_PAGESIZE(1, "72mm", "50mm", "");

        // 如果没有衣物列表，则按 1 处理
        const totalItems = (labelData.clothes && labelData.clothes.length) ? labelData.clothes.length : 1;

        // 统一计算衣物名称串、徽章等（网页布局使用整单信息）
        const clothesNames = (labelData.clothes || []).map((c, i) => `${i + 1}.${c.name || '未知'}`).join(' ');
        const totalCount = (labelData.clothes || []).length;
        const badgeArr = [];
        if ((labelData.clothes || []).some(c => c.has_accessory === 'true' || (/配饰|饰品/.test(c.name || '')))) badgeArr.push('[配]');
        if ((labelData.clothes || []).some(c => c.is_urgent === 'true' || ((c.services || []).includes && (c.services || []).includes('加急')))) badgeArr.push('[急]');
        const badgeTextGlobal = badgeArr.join(' ');

        // 辅助函数：绘制单张标签
        const drawSingleLabel = (idx) => {
            console.log(`开始绘制标签 ${idx + 1}，标签数据:`, labelData);
            // 不绘制外边框，保持简洁的标签样式
            // LODOP.ADD_PRINT_RECT("1mm", "1mm", "70mm", "48mm", 2, 1);

            // 支付状态映射 - 处理中文状态值，直接显示
            const getPaymentStatusText = (status) => {
                // 如果已经是中文状态，直接返回
                if (['已付款', '未付款', '已退款', '部分退款'].includes(status)) {
                    return status;
                }
                
                // 处理英文状态值的映射（兼容性）
                const statusMap = {
                    'paid': '已付款',
                    'unpaid': '未付款', 
                    'refunded': '已退款',
                    'partial_refund': '部分退款'
                };
                return statusMap[status] || status || '未知';
            };
            
            const paymentStatusText = getPaymentStatusText(labelData.payment_status);

            // 最顶部：支付状态 (页面最左上角)
            LODOP.ADD_PRINT_TEXT("2mm", "3mm", "30mm", "3mm", paymentStatusText);
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "FontColor", "#FF0000"); // 红色突出显示

            // 头部：营业员信息，居中加粗
            LODOP.ADD_PRINT_TEXT("6mm", "1mm", "70mm", "6mm", `${labelData.operator || '未知'}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 14);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "FontColor", "#000000"); // 重置为黑色字体
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);

            // 日期格式 yyMMdd
            const dateFormatted = labelData.date ? labelData.date.split(' ')[0].replace(/-/g, '').substr(2) : '未知';

            // 行 1：单号 / 日期
            LODOP.ADD_PRINT_TEXT("14mm", "3mm", "32mm", "4mm", `单号: ${labelData.order_number}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "FontColor", "#000000"); // 重置为黑色字体
            
            LODOP.ADD_PRINT_TEXT("14mm", "36mm", "33mm", "4mm", `日期: ${dateFormatted}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "FontColor", "#000000"); // 重置为黑色字体
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 3); // 右对齐

            // 行 2：电话（去掉徽章，给电话更多空间）
            console.log('调试电话号码:', labelData.customer_phone, '类型:', typeof labelData.customer_phone);
            const phoneDisplay = (labelData.customer_phone && String(labelData.customer_phone).trim()) ? String(labelData.customer_phone) : '未知';
            console.log('处理后的电话显示:', phoneDisplay);
            console.log('即将打印电话文本:', `电话: ${phoneDisplay}`);
            
            // 给电话号码整行的空间，不再与徽章共享
            LODOP.ADD_PRINT_TEXT("18mm", "3mm", "66mm", "5mm", `电话: ${phoneDisplay}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10); // 与其他文本保持一致的字体大小
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "FontColor", "#000000");
            console.log('电话号码打印元素已添加（整行显示）');
            
            // 徽章移到其他位置或暂时去掉
            // if (badgeTextGlobal) {
            //     // 徽章暂时注释掉，专注解决电话号码显示问题
            // }

            // 行 3：客户 / 总件数
            LODOP.ADD_PRINT_TEXT("22mm", "3mm", "32mm", "4mm", `客户: ${labelData.customer_name || '未知'}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "FontColor", "#000000"); // 重置为黑色字体
            LODOP.ADD_PRINT_TEXT("22mm", "36mm", "33mm", "4mm", `总件数: ${totalCount}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "FontColor", "#000000"); // 重置为黑色字体
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 3);

            // 行 4：衣物列表
            LODOP.ADD_PRINT_TEXT("28mm", "3mm", "66mm", "6mm", `衣物: ${clothesNames}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "FontColor", "#000000"); // 重置为黑色字体

            // 条形码
            const barcodeContent = `${labelData.order_number}-${String(idx + 1).padStart(2, '0')}`;
            LODOP.ADD_PRINT_BARCODE("37mm", "16mm", "40mm", "10mm", "128A", barcodeContent);
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 1);
        };

        console.log(`准备绘制 ${totalItems} 张标签`);
        for (let i = 0; i < totalItems; i++) {
            console.log(`绘制第 ${i + 1} 张标签`);
            if (i > 0) {
                LODOP.NEWPAGE();
            }
            drawSingleLabel(i);
        }
        console.log('所有标签绘制完成');

        // === 静默打印到指定打印机 ===
        try {
            const result = LODOP.SET_PRINTER_INDEXA("bgj");
            if (result) {
                console.log("成功设置不干胶标签打印机: bgj");
            } else {
                console.error("未找到指定的不干胶标签打印机，取消打印操作");
                // alert("未找到指定的不干胶标签打印机（bgj），请检查打印机连接状态");
                return;
            }
        } catch (e) {
            console.error("设置不干胶标签打印机时发生错误，取消打印操作");
            // alert("设置不干胶标签打印机时发生错误，请检查打印机连接状态");
            return;
        }
        LODOP.PRINT();

    } catch (error) {
        console.error('不干胶标签打印失败:', error);
        // alert('不干胶标签打印失败: ' + error.message);
    }
}

/**
 * 小票打印 (80mm 热敏纸)
 * @param {Object} orderData 订单数据
 */
function printReceiptLodop(orderData) {
    if (!checkLodopAvailable()) return;

    try {
        // 初始化打印任务
        const LODOP = getLodopInstance();
        LODOP.PRINT_INIT("收银小票打印");
        
        // 设置纸张大小 - 80mm宽度，高度自适应
        LODOP.SET_PRINT_PAGESIZE(1, "80mm", "200mm", "");
        
        // 设置打印模式
        LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
        LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREIEW", true);
        
        let currentTop = 5; // 当前打印位置 (mm)
        
        // 小票头部
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "8mm", "Soulweave改衣坊");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 18);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
        currentTop += 10;
        
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", "收银小票");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 14);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
        currentTop += 8;
        
        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;
        
        // 订单信息
        const orderInfo = [
            `订单号: ${orderData.order_number}`,
            `客户: ${orderData.customer_name}`,
            `电话: ${orderData.customer_phone}`,
            `日期: ${orderData.date}`,
            `收银员: ${orderData.operator}`
        ];
        
        orderInfo.forEach(info => {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", info);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;
        });
        
        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;
        
        // 表格头部
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "25mm", "5mm", "品名");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "30mm", "10mm", "5mm", "数量");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
        
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "40mm", "20mm", "5mm", "服务");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "60mm", "15mm", "5mm", "单价");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 3); // 右对齐
        currentTop += 6;
        
        // 表格分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 2;
        
        // 商品列表
        if (orderData.clothes && Array.isArray(orderData.clothes)) {
            orderData.clothes.forEach(item => {
                // 商品名称
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "25mm", "5mm", item.name || '未知');
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                
                // 数量
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "30mm", "10mm", "5mm", (item.quantity || 1).toString());
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
                
                // 服务信息
                let serviceText = '洗衣';
                if (item.services && Array.isArray(item.services)) {
                    const mainServices = item.services.filter(s => s !== '加急');
                    if (mainServices.length > 0) {
                        serviceText = mainServices.join('/');
                    }
                    if (item.services.includes('加急')) {
                        serviceText += '(急)';
                    }
                }
                
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "40mm", "20mm", "5mm", serviceText);
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                
                // 单价
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "60mm", "15mm", "5mm", `¥${(item.price || 0).toFixed(2)}`);
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 3); // 右对齐
                
                currentTop += 6;
            });
        }
        
        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;
        
        // 合计信息
        if (orderData.discount_amount && orderData.discount_amount > 0) {
            // 有折扣的情况
            const originalAmount = (parseFloat(orderData.total_amount) || 0) + (parseFloat(orderData.discount_amount) || 0);
            
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `原价: ¥${(originalAmount).toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;
            
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `折扣: -¥${(parseFloat(orderData.discount_amount)||0).toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;
            
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `实付金额: ¥${(parseFloat(orderData.total_amount)||0).toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 7;
        } else {
            // 无折扣的情况
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `总金额: ¥${(parseFloat(orderData.total_amount)||0).toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 7;
        }
        
        // 支付方式
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `支付方式: ${orderData.payment_method || '现金'}`);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        currentTop += 8;
        
        // 客户余额信息（如果有）
        if (orderData.customer_balance_info && orderData.customer_balance_info.has_balance_account) {
            const balanceInfo = orderData.customer_balance_info;
            
            // 虚线分隔
            LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 2, 1);
            currentTop += 3;
            
            const beforeBal = parseFloat(balanceInfo.balance_before || balanceInfo.total_balance || 0) || 0;
            const consumed = parseFloat(balanceInfo.consumed_amount || balanceInfo.balance_used || 0) || 0;
            const afterBal = parseFloat(balanceInfo.balance_after || balanceInfo.balance_after_order || 0) || 0;

            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "4mm", `消费前余额: ¥${beforeBal.toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 5;
            
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "4mm", `本次消费: ¥${consumed.toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 5;
            
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "4mm", `剩余余额: ¥${afterBal.toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 8;
        }
        
        // ======= 新增: 备注信息 =======
        if (orderData.remarks && orderData.remarks.trim()) {
            // 分隔线（细线）
            LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 1, 1);
            currentTop += 3;

            // 打印备注内容，超长自动省略
            let remarkText = `备注: ${orderData.remarks}`;
            if (remarkText.length > 100) {
                remarkText = remarkText.substring(0, 97) + '...';
            }

            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", remarkText);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 7;
        }
        
        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 2, 1);
        currentTop += 3;
        
        // 小票底部
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", "感谢您的惠顾，欢迎再次光临！");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
        currentTop += 8;
        
        // 设置实际页面高度
        LODOP.SET_PRINT_PAGESIZE(1, "80mm", `${currentTop + 5}mm`, "");
        
        // === 静默打印到指定打印机 ===
        try {
            const result = LODOP.SET_PRINTER_INDEXA("xp");
            if (result) {
                console.log("成功设置小票打印机: xp");
            } else {
                console.error("未找到指定的小票打印机，取消打印操作");
                // alert("未找到指定的小票打印机（xp），请检查打印机连接状态");
                return;
            }
        } catch (e) {
            console.error("设置小票打印机时发生错误，取消打印操作");
            // alert("设置小票打印机时发生错误，请检查打印机连接状态");
            return;
        }
        LODOP.PRINT();
        
    } catch (error) {
        console.error('小票打印失败:', error);
        // alert('小票打印失败: ' + error.message);
    }
}

/**
 * 统一的Lodop水洗唛打印接口
 * @param {string|number|object} orderId 订单ID或订单对象
 * @param {string} selectedOption 选择选项，'all'打印全部，数字打印特定索引
 */
async function lodopPrintWashLabels(orderId, selectedOption = 'all') {
    try {
        console.log("Lodop水洗唛打印开始，原始参数:", orderId, "类型:", typeof orderId, "选项:", selectedOption);
        
        // 正确提取订单ID - 处理可能传入对象的情况
        let actualOrderId = orderId;
        if (typeof orderId === 'object' && orderId !== null) {
            // 如果传入的是对象，尝试提取ID
            actualOrderId = orderId.id || orderId.order_id || orderId.orderId || orderId.order_number;
            console.log("lodopPrintWashLabels: 检测到对象类型的orderId，提取实际ID:", actualOrderId, "原始对象:", orderId);
        }

        // 确保actualOrderId是有效值
        if (!actualOrderId) {
            console.error("lodopPrintWashLabels: 无效的订单ID:", orderId);
            throw new Error('无效的订单ID');
        }

        console.log("lodopPrintWashLabels: 使用的订单ID:", actualOrderId);
        
        // 修正API路径 - 使用正确的order_details路由
        const response = await fetch(`/order_details?id=${actualOrderId}`);
        if (!response.ok) {
            throw new Error('获取订单数据失败');
        }
        
        const result = await response.json();
        console.log("获取到查询结果:", result);
        
        // 从查询结果中提取订单数据 - order_details返回的是直接的订单数据
        if (result.error) {
            throw new Error(result.error);
        }
        
        const orderData = result; // order_details直接返回订单数据
        console.log("提取的订单数据:", orderData);
        
        // 检查衣物数据
        console.log("检查衣物数据:", {
            clothes: orderData.clothes,
            clothesType: typeof orderData.clothes,
            isArray: Array.isArray(orderData.clothes),
            length: orderData.clothes?.length
        });
        
        if (!orderData.clothes || !Array.isArray(orderData.clothes) || orderData.clothes.length === 0) {
            console.error('订单中没有衣物数据，订单数据:', orderData);
            throw new Error('订单中没有衣物数据');
        }
        
        // 确定要打印的衣物
        let selectedItems = null;
        if (selectedOption !== 'all') {
            const index = parseInt(selectedOption);
            if (!isNaN(index) && index >= 0 && index < orderData.clothes.length) {
                selectedItems = [orderData.clothes[index]];
            }
        }
        
        // 执行Lodop打印
        printWashLabels(orderData, selectedItems);
        
    } catch (error) {
        console.error('Lodop水洗唛打印失败:', error);
        // alert('Lodop水洗唛打印失败: ' + error.message);
    }
}

/**
 * 统一的Lodop小票打印接口
 * @param {string|number|object} orderId 订单ID或订单对象
 */
async function lodopPrintReceipt(orderId) {
    try {
        console.log("Lodop小票打印开始，原始参数:", orderId, "类型:", typeof orderId);
        
        // 正确提取订单ID - 处理可能传入对象的情况
        let actualOrderId = orderId;
        if (typeof orderId === 'object' && orderId !== null) {
            // 如果传入的是对象，尝试提取ID
            actualOrderId = orderId.id || orderId.order_id || orderId.orderId || orderId.order_number;
            console.log("lodopPrintReceipt: 检测到对象类型的orderId，提取实际ID:", actualOrderId, "原始对象:", orderId);
        }

        // 确保actualOrderId是有效值
        if (!actualOrderId) {
            console.error("lodopPrintReceipt: 无效的订单ID:", orderId);
            throw new Error('无效的订单ID');
        }

        console.log("lodopPrintReceipt: 使用的订单ID:", actualOrderId);
        
        // 修正API路径 - 使用正确的order_details路由
        const response = await fetch(`/order_details?id=${actualOrderId}`);
        if (!response.ok) {
            throw new Error('获取订单数据失败');
        }
        
        const result = await response.json();
        console.log("获取到查询结果:", result);
        
        // 从查询结果中提取订单数据 - order_details返回的是直接的订单数据
        if (result.error) {
            throw new Error(result.error);
        }
        
        const orderData = result; // order_details直接返回订单数据
        console.log("提取的订单数据:", orderData);
        
        // 调用专用的小票打印函数，避免与网页函数重名
        printReceiptLodop(orderData);
        
    } catch (error) {
        console.error('Lodop小票打印失败:', error);
        // alert('Lodop小票打印失败: ' + error.message);
    }
}

/**
 * 通用的Lodop不干胶标签打印接口
 * @param {string} orderNumber 订单号
 */
async function lodopPrintStickyLabel(orderNumber) {
    try {
        console.log("Lodop不干胶标签打印开始，订单号:", orderNumber);
        
        // 获取标签数据
        const response = await fetch(`/api/order_label/${orderNumber}`);
        if (!response.ok) {
            throw new Error('获取标签数据失败');
        }
        
        const result = await response.json();
        if (!result.success) {
            throw new Error(result.message || '获取标签数据失败');
        }
        
        const labelData = result.label_data;
        console.log("获取到标签数据:", labelData);
        
        // 执行Lodop打印
        printStickyLabel(labelData);
        
    } catch (error) {
        console.error('Lodop不干胶标签打印失败:', error);
        // alert('Lodop不干胶标签打印失败: ' + error.message);
    }
}

// 导出函数供全局使用
window.checkLodopAvailable = checkLodopAvailable;
window.getLodopInstance = getLodopInstance;
window.lodopPrintWashLabels = lodopPrintWashLabels;
window.lodopPrintReceipt = lodopPrintReceipt;
window.lodopPrintStickyLabel = lodopPrintStickyLabel;

console.log('Lodop打印集成模块初始化完成');

// 水洗标签打印函数 (101mm × 16mm) - 与printWashLabels保持一致的布局
function printWashLabelLodop(orderData, selectedItems = null) {
    // 直接调用统一实现，保持单一代码源
    printWashLabels(orderData, selectedItems);
}



// 获取订单数据的函数 - 修正API路径
async function fetchOrderData(orderId) {
    try {
        // 修正API路径：使用order_details而不是customer_history
        const response = await fetch(`/order_details?id=${orderId}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        return data;
    } catch (error) {
        console.error('获取订单数据失败:', error);
        throw error;
    }
}

// 统一打印接口 - 带模式选择
function printWithModeSelection(orderId, printType) {
    if (!orderId) {
        console.error('订单ID不能为空');
        // alert('订单ID不能为空');
        return;
    }

    // 如果检测到Lodop可用，则直接使用Lodop静默打印，不再弹窗询问
    if (checkLodopAvailable()) {
        if (printType === 'sticky_label') {
            // 不干胶标签使用第一版函数，需要通过API获取标签数据
            lodopPrintStickyLabel(orderId);
        } else {
            fetchOrderData(orderId).then(orderData => {
                switch (printType) {
                    case 'receipt':
                        printReceiptLodop(orderData);
                        break;
                    case 'wash_label':
                        printWashLabelLodop(orderData);
                        break;
                    default:
                        console.error('未知的打印类型:', printType);
                        // alert('未知的打印类型');
                }
            }).catch(error => {
                console.error('获取订单数据失败:', error);
                // alert(`获取订单数据失败: ${error.message}`);
            });
        }
        return;
    }

    // --- Lodop 不可用情况下 ---
    const mode = confirm('未检测到 Lodop 打印组件\n确定 = 使用网页打印 (备选方案)\n取消 = 安装 Lodop 后再试');

    if (mode) {
        // 网页打印
        switch (printType) {
            case 'receipt':
                window.open(`/receipt/${orderId}`, '_blank');
                break;
            case 'wash_label':
                window.open(`/labels/${orderId}`, '_blank');
                break;
            case 'sticky_label':
                window.open(`/sticky_label_print?order_id=${orderId}`, '_blank');
                break;
            default:
                console.error('未知的打印类型:', printType);
                // alert('未知的打印类型');
        }
    } else {
        console.warn('已取消打印，请安装 Lodop 后重试');
        // alert('已取消打印，请安装 Lodop 后重试');
    }
}

// === 自动充值小票打印：默认使用 Lodop，不可用时提供网页打印 ===
function autoPrintRechargeReceipt(rechargeData) {
    if (!rechargeData) {
        console.error('autoPrintRechargeReceipt: rechargeData 不能为空');
        return;
    }

    if (checkLodopAvailable()) {
        lodopPrintRechargeReceipt(rechargeData);
        return;
    }

    // Lodop 不可用，提示用户选择
    const useWeb = confirm('未检测到 Lodop 打印组件，点击"确定"改用网页打印，或"取消"安装后再试');
    if (useWeb) {
        if (typeof printRechargeReceipt === 'function') {
            printRechargeReceipt(rechargeData);
        } else {
            console.error('网页打印功能不可用，请联系管理员');
            // alert('网页打印功能不可用，请联系管理员');
        }
    } else {
        console.warn('已取消打印，请安装 Lodop 后重试');
        // alert('已取消打印，请安装 Lodop 后重试');
    }
}

// 向全局暴露新函数
window.autoPrintRechargeReceipt = autoPrintRechargeReceipt;
window.lodopPrintRechargeReceipt = lodopPrintRechargeReceipt;
window.printRechargeReceiptLodop = printRechargeReceiptLodop;

// 导出函数供全局使用
window.printReceiptWithModeSelection = function(orderId) {
    printWithModeSelection(orderId, 'receipt');
};

window.printWashLabelWithModeSelection = function(orderId) {
    printWithModeSelection(orderId, 'wash_label');
};

window.printStickyLabelWithModeSelection = function(orderId) {
    printWithModeSelection(orderId, 'sticky_label');
};

// 批量打印功能
window.batchPrintWithLodop = function(orderIds, printType) {
    if (!checkLodopAvailable()) return;
    
    if (!orderIds || orderIds.length === 0) {
        console.error('请选择要打印的订单');
        // alert('请选择要打印的订单');
        return;
    }
    
    const confirmMsg = `确定要批量打印 ${orderIds.length} 个订单的${printType === 'receipt' ? '收据' : (printType === 'wash_label' ? '水洗标签' : '不干胶标签')}吗？`;
    if (!confirm(confirmMsg)) {
        return;
    }
    
    // 批量获取订单数据并打印
    Promise.all(orderIds.map(orderId => fetchOrderData(orderId)))
        .then(orderDataList => {
            const LODOP = getLodopInstance();
            LODOP.PRINT_INIT(`批量${printType === 'receipt' ? '收据' : (printType === 'wash_label' ? '水洗标签' : '不干胶标签')}打印`);
            
            orderDataList.forEach((orderData, index) => {
                if (index > 0) {
                    LODOP.NewPage();
                }
                
                // 根据打印类型调用相应的打印函数
                switch(printType) {
                    case 'receipt':
                        printReceiptLodop(orderData);
                        break;
                    case 'wash_label':
                        printWashLabelLodop(orderData);
                        break;
                    case 'sticky_label':
                        // 注意：不干胶标签批量打印暂不支持，因为第一版函数需要标签数据而不是订单数据
                        console.warn('不干胶标签暂不支持批量打印');
                        break;
                }
            });
            
            LODOP.PREVIEW();
        })
        .catch(error => {
            console.error('批量获取订单数据失败:', error);
            // alert(`批量获取订单数据失败: ${error.message}`);
        });
};

// === 新增：充值小票打印 (80mm 热敏纸) ===
/**
 * 充值小票打印 (80mm 热敏纸)
 * @param {Object} rechargeData 充值数据
 *   - customer_name: string 客户姓名
 *   - phone: string 手机号码
 *   - amount: number 充值金额
 *   - giftAmount: number 赠送金额
 *   - paymentMethod: string 支付方式
 *   - newBalance: number 充值后余额
 *   - operator: string 操作员
 *   - isNewCustomer: boolean 是否新客户
 *   - rechargeTime?: string 充值时间（可选，若无则使用当前时间）
 */
function printRechargeReceiptLodop(rechargeData) {
    if (!checkLodopAvailable()) return;

    try {
        const LODOP = getLodopInstance();
        LODOP.PRINT_INIT("充值小票打印");
        // 80mm 宽热敏纸，高度初始 200mm，自适应后再调整
        LODOP.SET_PRINT_PAGESIZE(1, "80mm", "200mm", "");
        LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
        LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREVIEW", true);

        // 统一的顶部位置(mm)
        let currentTop = 5;

        // ======== 标题 ========
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "8mm", "Soulweave改衣坊");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 18);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
        currentTop += 10;

        // 小票标题（支持补打印标识）
        let receiptTitle = "账户充值小票";
        if (rechargeData.isReprint) {
            receiptTitle += " [补打印]";
        }
        
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", receiptTitle);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 14);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
        currentTop += 8;

        // 补打印信息（如果是补打印）
        if (rechargeData.isReprint) {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `补打印时间: ${rechargeData.reprintTime || new Date().toLocaleString('zh-CN')}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
            currentTop += 5;
            
            if (rechargeData.reprintReason) {
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `补打印原因: ${rechargeData.reprintReason}`);
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
                currentTop += 5;
            }
            
            // 补打印后的分隔线
            LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
            currentTop += 3;
        }

        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;

        // ======== 基本信息 ========
        const currentTime = rechargeData.rechargeTime || new Date().toLocaleString('zh-CN', {
            year: 'numeric', month: '2-digit', day: '2-digit',
            hour: '2-digit', minute: '2-digit', second: '2-digit'
        });

        const baseInfo = [
            `客户姓名: ${rechargeData.customer_name || '未知'}`,
            `手机号码: ${rechargeData.phone || '未知'}`,
            `充值时间: ${currentTime}`,
            `操作员: ${rechargeData.operator || '系统'}`
        ];
        baseInfo.forEach(info => {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", info);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;
        });

        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;

        // ======== 金额信息 ========
        // 充值金额
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `充值金额: ¥${(rechargeData.amount || 0).toFixed(2)}`);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        currentTop += 6;

        // 赠送金额（可选）
        if (rechargeData.giftAmount && rechargeData.giftAmount > 0) {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `赠送金额: ¥${rechargeData.giftAmount.toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;

            const actualAmount = (rechargeData.amount || 0) + (rechargeData.giftAmount || 0);
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `实际到账: ¥${actualAmount.toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;
        }

        // 支付方式
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `支付方式: ${rechargeData.paymentMethod || '未知'}`);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        currentTop += 6;

        // 分隔线（虚线，表示余额变动）
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;

        // 余额信息
        const prevBalance = (rechargeData.newBalance || 0) - (rechargeData.amount || 0) - (rechargeData.giftAmount || 0);
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `充值前余额: ¥${prevBalance.toFixed(2)}`);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        currentTop += 6;

        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", `充值后余额: ¥${(rechargeData.newBalance || 0).toFixed(2)}`);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 13);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        currentTop += 7;

        // 新客户欢迎语
        if (rechargeData.isNewCustomer) {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", "🎉 欢迎新客户 🎉");
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
            currentTop += 8;
        }

        // ======== 尾部致谢 ========
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", "感谢您的信任，欢迎再次光临！");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
        currentTop += 8;

        // 重新设置页面高度，避免留白
        LODOP.SET_PRINT_PAGESIZE(1, "80mm", `${currentTop + 5}mm`, "");

        // 指定打印机（假设与订单小票相同）
        try {
            const result = LODOP.SET_PRINTER_INDEXA("xp");
            if (result) {
                console.log("成功设置充值小票打印机: xp");
            } else {
                console.error("未找到指定的充值小票打印机，取消打印操作");
                // alert("未找到指定的充值小票打印机（xp），请检查打印机连接状态");
                return;
            }
        } catch (e) {
            console.error("设置充值小票打印机时发生错误，取消打印操作");
            // alert("设置充值小票打印机时发生错误，请检查打印机连接状态");
            return;
        }

        // 静默打印
        LODOP.PRINT();

    } catch (error) {
        console.error('充值小票打印失败:', error);
        // alert('充值小票打印失败: ' + error.message);
    }
}

/**
 * Lodop充值小票打印入口
 * @param {Object} rechargeData 充值数据对象
 */
function lodopPrintRechargeReceipt(rechargeData) {
    // 直接调用实现函数
    printRechargeReceiptLodop(rechargeData);
} 