# 充值退充和小票补打印功能说明

## 功能概述

本系统新增了充值退充和小票补打印功能，包括：

1. **充值退充功能**：支持对客户的充值记录进行退充操作
2. **小票补打印功能**：支持重新打印历史充值记录和退充记录的小票
3. **权限管理**：基于员工角色的退充权限控制
4. **审计追踪**：完整的操作日志记录

## 快速开始

### 1. 启动应用服务器

```bash
python app.py
```

或者使用测试启动脚本（提供友好提示）：
```bash
python start_test_server.py
```

### 2. 访问测试页面

打开浏览器访问：`http://localhost:5000/test/refund`

### 3. 测试功能

在测试页面中可以测试以下功能：
- 查询可退充信息
- 执行退充操作
- 补打印充值小票
- 补打印退充小票
- 查询退充记录

## 功能详细说明

### 充值退充功能

#### 权限要求
- 员工必须具有 `can_refund_recharge = True` 权限
- 退充金额不能超过员工的 `max_refund_amount` 限制
- 大额退充需要管理员审批

#### 退充规则
- 只能退充客户当前余额范围内的金额
- 如果原充值包含赠送金额，会按比例扣除赠送余额
- 退充后会自动更新客户的充值余额和赠送余额

#### 使用步骤
1. 查询充值记录的可退充信息
2. 填写退充金额和退充原因
3. 系统验证权限和金额
4. 执行退充操作
5. 自动打印退充小票

### 小票补打印功能

#### 支持类型
- **充值小票补打印**：重新打印历史充值记录的小票
- **退充小票补打印**：重新打印历史退充记录的小票

#### 补打印标识
- 补打印的小票会标注 `[补打印]` 字样
- 显示补打印时间和原因
- 显示原小票的时间

#### 使用步骤
1. 输入要补打印的记录ID
2. 填写补打印原因（可选）
3. 系统创建补打印记录
4. 自动调用打印功能

## API接口说明

### 1. 查询可退充信息
```
GET /api/recharge/{recharge_id}/refundable
```

**响应示例：**
```json
{
  "success": true,
  "refundable_amount": 300.00,
  "gift_amount_to_deduct": 50.00,
  "current_balance": 300.00,
  "current_gift_balance": 50.00,
  "can_refund": true,
  "reason": "可以退充"
}
```

### 2. 执行退充操作
```
POST /api/recharge/{recharge_id}/refund
```

**请求体：**
```json
{
  "refund_amount": 100.00,
  "refund_reason": "客户要求退款",
  "print_receipt": true
}
```

**响应示例：**
```json
{
  "success": true,
  "refund_id": 123,
  "message": "退充操作成功",
  "new_balance": 200.00,
  "receipt_printed": true
}
```

### 3. 查询退充记录
```
GET /api/recharge/refunds?page=1&per_page=20
```

**可选参数：**
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `customer_phone`: 客户手机号
- `operator`: 操作员姓名

### 4. 补打印充值小票
```
POST /api/recharge/{recharge_id}/reprint
```

**请求体：**
```json
{
  "reprint_reason": "客户要求补打印"
}
```

### 5. 补打印退充小票
```
POST /api/recharge/refund/{refund_id}/reprint
```

**请求体：**
```json
{
  "reprint_reason": "客户要求补打印"
}
```

## 数据库结构

### 退充记录表 (recharge_refund)
- `id`: 主键
- `recharge_record_id`: 原充值记录ID
- `customer_id`: 客户ID
- `refund_amount`: 退充金额
- `original_amount`: 原充值金额
- `refund_reason`: 退充原因
- `refund_method`: 退充方式
- `status`: 状态
- `operator`: 操作员
- `approved_by`: 审批人
- `created_at`: 创建时间

### 补打印记录表 (receipt_reprint)
- `id`: 主键
- `record_type`: 记录类型 (recharge/refund)
- `record_id`: 对应记录ID
- `customer_id`: 客户ID
- `operator`: 操作员
- `reprint_reason`: 补打印原因
- `created_at`: 创建时间

### 员工权限扩展 (staff)
- `can_refund_recharge`: 是否有退充权限
- `max_refund_amount`: 最大退充金额限制

## 权限说明

### 员工权限级别
1. **普通员工**：只能操作自己服务的客户
2. **区域管理员**：只能操作本区域的客户
3. **总部管理员**：可以操作所有客户

### 退充权限设置
- 默认情况下，所有管理员都有退充权限
- 最大退充金额默认为 ¥10,000
- 可以通过员工管理界面调整权限设置

## 打印功能

### 支持的打印方式
1. **Lodop打印**（推荐）：使用专业打印控件
2. **网页打印**：浏览器打印功能（备用）

### 打印机配置
- 退充小票使用与充值小票相同的打印机（`xp`）
- 纸张规格：80mm热敏纸
- 支持静默打印

### 小票内容
- **退充小票**：包含客户信息、原充值信息、退充详情、余额变动等
- **补打印标识**：明确标注补打印时间和原因

## 安全考虑

### 数据完整性
- 使用数据库事务确保操作原子性
- 操作失败时自动回滚所有变更

### 权限验证
- 多层权限验证机制
- 操作前验证员工权限和金额限制

### 审计追踪
- 记录所有退充和补打印操作
- 包含操作时间、操作员、IP地址等信息

## 故障排除

### 常见问题

1. **导入错误**
   - 确保没有冲突的 `utils/` 目录
   - 使用根目录的 `utils.py` 文件

2. **数据库错误**
   - 运行数据库迁移脚本：`python migrations/migrate_recharge_refund.py`
   - 检查表结构是否正确

3. **权限问题**
   - 确保员工有 `can_refund_recharge` 权限
   - 检查 `max_refund_amount` 设置

4. **打印问题**
   - 确保安装了Lodop打印控件
   - 检查打印机连接状态
   - 验证打印机名称配置

### 日志查看
- 退充操作日志：`logs/recharge_refund_operations.log`
- 应用日志：控制台输出

## 开发说明

### 文件结构
```
├── models.py                          # 数据模型定义
├── utils.py                           # 业务逻辑函数
├── blueprints/recharge.py             # API接口
├── static/js/refund-print.js          # 前端打印功能
├── templates/refund_receipt.html      # 退充小票模板
├── templates/refund_test.html         # 测试页面
├── migrations/                        # 数据库迁移脚本
├── test_refund_api.py                 # API测试脚本
└── start_test_server.py               # 测试服务器启动脚本
```

### 扩展开发
- 可以基于现有API开发更丰富的前端界面
- 支持添加更多的退充规则和验证逻辑
- 可以扩展打印模板和格式

## 联系支持

如有问题或建议，请联系开发团队。