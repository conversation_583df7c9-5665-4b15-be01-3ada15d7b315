from flask import Blueprint, jsonify, request, session

from blueprints.auth import login_required
from models import db, Customer, RechargeRecord, RechargeGiftRule
from utils import update_customer_balance, calculate_gift_amount

bp = Blueprint('customer', __name__)


@bp.route('/search_customer', endpoint='search_customer')
@login_required
def search_customer():
    """按手机号搜索客户信息"""
    phone = request.args.get('phone', '').strip()
    customer = Customer.query.filter_by(phone=phone).first()
    if customer:
        # 检查折扣是否有效
        import datetime
        discount_valid = True
        if customer.discount_expiry:
            discount_valid = customer.discount_expiry >= datetime.date.today()
        
        return jsonify({
            'found': True,
            'name': customer.name,
            'id': customer.id,
            'balance': customer.balance,
            'gift_balance': customer.gift_balance or 0.0,
            'total_balance': customer.total_balance,
            'discount_rate': customer.discount_rate or 1.0,
            'discount_expiry': customer.discount_expiry.isoformat() if customer.discount_expiry else None,
            'discount_valid': discount_valid,
        })
    return jsonify({'found': False})


@bp.route('/recharge_account', methods=['POST'], endpoint='recharge_account')
@login_required
def recharge_account():
    """客户账户充值（后台）"""
    try:
        data = request.json or {}
        phone = data.get('phone')
        amount = float(data.get('amount', 0))
        payment_method = data.get('paymentMethod')
        customer_name = data.get('customer_name', '')

        if not phone or amount <= 0 or not payment_method:
            return jsonify({'error': '参数错误'}), 400

        customer = Customer.query.filter_by(phone=phone).first()
        if not customer:
            customer = Customer(name=customer_name or phone, phone=phone, balance=0.0)
            db.session.add(customer)
            db.session.flush()
            is_new_customer = True
        else:
            is_new_customer = False

        selected_rule_id = data.get('selected_rule_id')
        no_gift_rule = data.get('no_gift_rule', False)

        if no_gift_rule:
            gift_amount = 0.0
        elif selected_rule_id:
            rule = RechargeGiftRule.query.filter_by(id=selected_rule_id, is_active=True).first()
            if rule and rule.min_amount <= amount:
                gift_amount = amount * (rule.gift_value / 100.0) if rule.gift_type == 'percentage' else rule.gift_value
            else:
                gift_amount = 0.0
        else:
            gift_amount = calculate_gift_amount(amount)

        new_balance = update_customer_balance(customer, amount, gift_amount=gift_amount)

        recharge = RechargeRecord(
            customer_id=customer.id,
            amount=amount,
            gift_amount=gift_amount,
            payment_method=payment_method,
            operator=session.get('staff_name', '未知'),
        )
        db.session.add(recharge)
        db.session.commit()

        return jsonify({
            'success': True,
            'newBalance': new_balance,
            'balance': customer.balance,
            'gift_balance': customer.gift_balance,
            'giftAmount': gift_amount,
            'isNewCustomer': is_new_customer,
            'customer_name': customer.name,
            'customer_id': customer.id,
            'operator': session.get('staff_name', '未知'),
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500 