from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for
from models import *
from blueprints.auth import login_required
import datetime

print_bp = Blueprint('print', __name__)
bp = print_bp  # 为了与自动注册系统兼容

@print_bp.route('/receipt/<order_id>')
@login_required
def receipt(order_id):
    """订单收据打印页面"""
    try:
        order = Order.query.get_or_404(order_id)
        
        # 验证权限：检查用户是否有权限查看此订单
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        
        if staff_role not in ['admin'] and staff_area != '总部':
            if order.area and order.area != staff_area:
                return "您没有权限查看此区域的订单", 403
        
        # 获取订单相关信息
        customer = order.customer
        clothes = order.clothes
        
        # 计算折扣信息
        total_original_amount = 0
        total_discount_amount = 0
        
        for clothing in clothes:
            quantity = clothing.quantity or 1
            if clothing.original_price and clothing.original_price > 0:
                original_price = clothing.original_price
            else:
                original_price = clothing.price
            
            item_original_amount = original_price * quantity
            item_actual_amount = clothing.price * quantity
            
            total_original_amount += item_original_amount
            total_discount_amount += (item_original_amount - item_actual_amount)
        
        # -------- 新增: 汇总衣物备注 --------
        clothing_remarks = []
        for c in clothes:
            if c.remarks and c.remarks.strip():
                clothing_remarks.append(f"{c.name}: {c.remarks}")
        order_remarks = '; '.join(clothing_remarks) if clothing_remarks else ''

        # -------- 新增: 计算客户余额信息（用于小票打印）--------
        customer_balance_info = None
        if customer:
            # 计算总余额（充值余额 + 赠送余额）
            total_balance = (customer.balance or 0) + (customer.gift_balance or 0)

            # 计算订单前后余额
            if order.payment_method == '余额' and order.payment_status == '已付款':
                # 如果是余额支付，订单前余额 = 当前余额 + 订单金额
                balance_before_order = total_balance + order.total_amount
                balance_after_order = total_balance
                balance_used = order.total_amount
            else:
                # 如果不是余额支付，余额没有变化
                balance_before_order = total_balance
                balance_after_order = total_balance
                balance_used = 0

            customer_balance_info = {
                'has_balance_account': True,
                'total_balance': total_balance,
                'balance': customer.balance or 0,
                'gift_balance': customer.gift_balance or 0,
                'balance_before_order': balance_before_order,
                'balance_after_order': balance_after_order,
                'balance_used': balance_used,
                'is_balance_payment': order.payment_method == '余额'
            }

        # 准备打印数据
        receipt_data = {
            'order': order,
            'customer': customer,
            'clothes': clothes,
            'total_original_amount': total_original_amount,
            'total_discount_amount': total_discount_amount,
            'staff_name': session.get('staff_name', ''),
            'print_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'remarks': order_remarks,  # 新增备注字段
            'customer_balance_info': customer_balance_info  # 新增客户余额信息
        }
        
        # 将模板名称调整为现有的 receipt1.html
        return render_template('receipt1.html', **receipt_data)
        
    except Exception as e:
        return f"获取订单信息失败: {str(e)}", 500

@print_bp.route('/labels/<order_id>')
@login_required  
def labels(order_id):
    """订单标签打印页面"""
    try:
        order = Order.query.get_or_404(order_id)
        
        # 验证权限
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        
        if staff_role not in ['admin'] and staff_area != '总部':
            if order.area and order.area != staff_area:
                return "您没有权限查看此区域的订单", 403
        
        # 获取订单相关信息
        customer = order.customer
        clothes = order.clothes
        
        # 准备标签数据
        label_data = {
            'order': order,
            'customer': customer,
            'clothes': clothes,
            'staff_name': session.get('staff_name', ''),
            'print_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return render_template('labels.html', **label_data)
        
    except Exception as e:
        return f"获取订单信息失败: {str(e)}", 500

@print_bp.route('/sticky_label_print')
@login_required
def sticky_label_print():
    """不干胶标签打印页面"""
    staff_name = session.get('staff_name', '未登录')
    staff_area = session.get('staff_area', '')
    
    # 获取订单ID参数（如果有的话）
    order_id = request.args.get('order_id')
    order_data = None
    
    if order_id:
        try:
            order = Order.query.get(order_id)
            if order:
                # 验证权限
                staff_role = session.get('staff_role')
                if staff_role not in ['admin'] and staff_area != '总部':
                    if order.area and order.area != staff_area:
                        order = None  # 权限不足，不显示订单数据
                
                if order:
                    order_data = {
                        'id': order.id,
                        'order_number': order.order_number,
                        'customer_name': order.customer.name if order.customer else '',
                        'customer_phone': order.customer.phone if order.customer else '',
                        'clothes': [{
                            'name': c.name,
                            'color': c.color,
                            'quantity': c.quantity or 1
                        } for c in order.clothes]
                    }
        except Exception as e:
            print(f"获取订单数据失败: {str(e)}")
    
    return render_template('sticky_label_print.html',
                         staff_name=staff_name,
                         staff_area=staff_area,
                         order_data=order_data)

@print_bp.route('/api/order_label/<order_number>', methods=['GET'])
@login_required
def get_order_label_info(order_number):
    """获取订单标签信息API"""
    try:
        order = Order.query.filter_by(order_number=order_number).first()
        if not order:
            return jsonify({'error': '订单不存在'}), 404
        
        # 验证权限
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        
        if staff_role not in ['admin'] and staff_area != '总部':
            if order.area and order.area != staff_area:
                return jsonify({'error': '您没有权限查看此区域的订单'}), 403
        
        # ---------- 构建前端所需的 label_data ----------
        customer = order.customer
        clothes_list = []

        total_count = 0
        for c in order.clothes:
            qty = c.quantity or 1
            total_count += qty
            clothes_list.append({
                'name': c.name,
                'color': c.color,
                'quantity': qty,
                # 默认标记字段，后续可根据业务调整
                'has_accessory': 'false',
                'is_urgent': 'false'
            })

        label_data = {
            'order_number': order.order_number,
            'operator': order.operator,
            'customer_name': customer.name if customer else '',
            'customer_phone': customer.phone if customer else '',
            'total_count': total_count,
            'date': order.created_at.strftime('%Y-%m-%d'),
            'payment_status': order.payment_status,  # 新增支付状态字段
            'clothes': clothes_list
        }

        return jsonify({
            'success': True,
            'label_data': label_data
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500 