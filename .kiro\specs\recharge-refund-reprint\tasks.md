# 充值退充和小票补打印功能实施计划

## 任务列表

- [x] 1. 数据库模型和迁移


  - 创建退充记录表和补打印记录表的数据模型
  - 扩展员工权限模型添加退充权限字段
  - 编写数据库迁移脚本
  - _需求: 1.3, 2.1, 3.1, 7.1_

- [x] 2. 退充核心业务逻辑实现

  - [x] 2.1 实现退充金额计算逻辑



    - 编写退充金额验证和计算函数
    - 实现赠送金额按比例扣除逻辑
    - 创建余额充足性检查功能
    - _需求: 1.5, 1.6, 6.1, 6.2, 6.3_

  - [x] 2.2 实现退充操作核心功能


    - 编写退充记录创建功能
    - 实现客户余额更新逻辑
    - 添加数据库事务处理确保操作原子性
    - _需求: 1.3, 6.4, 8.1, 8.2_

  - [x] 2.3 实现退充权限验证


    - 编写权限检查中间件
    - 实现基于角色的退充权限控制
    - 添加金额限制验证逻辑
    - _需求: 1.7, 2.2, 2.3, 2.4_

- [x] 3. 退充API接口开发

  - [x] 3.1 实现获取可退充信息接口


    - 编写GET /api/recharge/{id}/refundable接口
    - 实现可退充金额计算和返回
    - 添加使用情况详情查询功能
    - _需求: 1.1, 1.5, 6.5_

  - [x] 3.2 实现执行退充操作接口

    - 编写POST /api/recharge/{id}/refund接口
    - 集成退充业务逻辑和权限验证
    - 添加操作结果返回和错误处理
    - _需求: 1.3, 1.4, 8.3_

  - [x] 3.3 实现退充记录查询接口

    - 编写GET /api/recharge/refunds接口
    - 实现分页、筛选和权限控制
    - 添加导出功能支持
    - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [x] 4. 小票补打印功能实现

  - [x] 4.1 实现补打印API接口


    - 编写POST /api/recharge/{id}/reprint接口用于充值小票补打印
    - 编写POST /api/recharge/refund/{id}/reprint接口用于退充小票补打印
    - 实现补打印记录创建和日志记录
    - _需求: 4.1, 4.2, 4.4, 4.6_

  - [x] 4.2 扩展打印服务支持补打印



    - 修改Lodop打印函数支持补打印标识
    - 更新充值小票打印函数添加补打印标记
    - 实现退充小票打印功能
    - _需求: 4.3, 4.5, 5.1, 5.2, 5.3_

  - [x] 4.3 实现补打印记录管理

    - 编写GET /api/receipts/reprints接口
    - 实现补打印历史查询和筛选
    - 添加补打印统计功能
    - _需求: 4.4, 7.2_

- [x] 5. 退充小票打印功能


  - [x] 5.1 设计退充小票模板


    - 创建退充小票HTML模板
    - 设计退充小票Lodop打印格式
    - 实现退充详情显示逻辑
    - _需求: 5.1, 5.2, 5.4_

  - [x] 5.2 实现退充小票打印函数

    - 编写printRefundReceiptLodop函数
    - 实现退充小票数据格式化
    - 添加网页打印备选方案
    - _需求: 5.1, 5.3, 5.5_

  - [x] 5.3 集成退充小票自动打印

    - 在退充操作完成后自动触发打印
    - 实现打印失败时的重试机制
    - 添加打印状态反馈
    - _需求: 1.4, 5.5_

- [x] 6. 前端界面开发


  - [x] 6.1 实现退充操作界面


    - 在充值记录列表添加退充按钮
    - 创建退充确认对话框组件
    - 实现退充金额输入和验证
    - _需求: 1.1, 1.2, 1.8_

  - [x] 6.2 实现补打印操作界面

    - 在充值记录列表添加补打印按钮
    - 创建补打印原因输入对话框
    - 实现一键补打印功能
    - _需求: 4.1, 4.2_

  - [x] 6.3 实现退充记录管理界面


    - 创建退充记录查询页面
    - 实现退充记录筛选和分页
    - 添加退充记录导出功能
    - _需求: 3.1, 3.2, 3.3, 3.4_

  - [x] 6.4 实现权限控制界面元素

    - 根据用户权限显示/隐藏退充按钮
    - 实现权限不足时的提示信息
    - 添加大额退充的审批流程界面
    - _需求: 1.7, 2.3, 2.4_

- [ ] 7. 员工权限管理功能
  - [ ] 7.1 扩展员工管理界面
    - 在员工编辑页面添加退充权限设置
    - 实现退充金额限制配置
    - 添加权限变更日志记录
    - _需求: 2.1, 2.2_

  - [ ] 7.2 实现权限验证中间件
    - 编写退充权限检查装饰器
    - 实现基于会话的权限验证
    - 添加权限缓存机制提高性能
    - _需求: 2.3, 2.4_

- [ ] 8. 操作日志和审计功能
  - [ ] 8.1 实现操作日志记录
    - 创建统一的日志记录服务
    - 实现退充操作日志记录
    - 添加补打印操作日志记录
    - _需求: 7.1, 7.2, 7.3_

  - [ ] 8.2 实现日志查询和分析
    - 编写操作日志查询接口
    - 实现日志筛选和导出功能
    - 添加异常操作检测和告警
    - _需求: 7.4, 7.5, 8.3, 8.4, 8.5_

- [ ] 9. 错误处理和异常管理
  - [ ] 9.1 实现自定义异常类
    - 创建退充相关异常类定义
    - 实现异常消息国际化
    - 添加异常日志记录功能
    - _需求: 8.1, 8.2_

  - [ ] 9.2 实现全局错误处理
    - 编写API错误处理中间件
    - 实现用户友好的错误提示
    - 添加错误重试机制
    - _需求: 8.2, 5.5_

- [ ] 10. 数据完整性和安全性
  - [ ] 10.1 实现数据验证和约束
    - 添加输入数据验证规则
    - 实现业务逻辑约束检查
    - 创建数据完整性测试用例
    - _需求: 8.1, 8.2_

  - [ ] 10.2 实现安全防护机制
    - 添加SQL注入防护
    - 实现操作频率限制
    - 创建异常操作检测规则
    - _需求: 8.3, 8.4, 8.5_

- [ ] 11. 测试用例编写
  - [ ] 11.1 编写单元测试
    - 创建退充金额计算逻辑测试
    - 编写权限验证功能测试
    - 实现余额更新逻辑测试
    - _需求: 所有核心业务逻辑_

  - [ ] 11.2 编写集成测试
    - 创建退充完整流程测试
    - 编写补打印功能集成测试
    - 实现权限控制集成测试
    - _需求: 完整业务流程_

  - [ ] 11.3 编写边界和压力测试
    - 创建边界条件测试用例
    - 编写并发操作测试
    - 实现性能压力测试
    - _需求: 系统稳定性和性能_

- [ ] 12. 配置和部署
  - [ ] 12.1 创建配置管理
    - 编写退充功能配置文件
    - 实现配置热更新机制
    - 添加配置验证功能
    - _需求: 系统可配置性_

  - [ ] 12.2 编写部署脚本
    - 创建数据库迁移脚本
    - 编写功能部署检查脚本
    - 实现回滚机制
    - _需求: 系统部署和维护_

- [ ] 13. 文档和培训材料
  - [ ] 13.1 编写用户操作手册
    - 创建退充操作指南
    - 编写补打印功能说明
    - 制作权限管理文档
    - _需求: 用户培训和支持_

  - [ ] 13.2 编写技术文档
    - 创建API接口文档
    - 编写数据库设计文档
    - 制作故障排除指南
    - _需求: 技术维护和支持_

- [ ] 14. 系统集成和联调测试
  - [ ] 14.1 集成现有充值系统
    - 确保与现有充值功能兼容
    - 测试数据迁移和升级
    - 验证业务流程完整性
    - _需求: 系统兼容性_

  - [ ] 14.2 进行端到端测试
    - 执行完整业务流程测试
    - 验证用户体验和界面交互
    - 测试打印功能和硬件兼容性
    - _需求: 整体功能验证_

- [ ] 15. 性能优化和监控
  - [ ] 15.1 实现性能监控
    - 添加关键操作性能监控
    - 实现数据库查询优化
    - 创建性能报告和告警
    - _需求: 系统性能_

  - [ ] 15.2 优化用户体验
    - 实现前端加载优化
    - 添加操作进度提示
    - 优化界面响应速度
    - _需求: 用户体验_