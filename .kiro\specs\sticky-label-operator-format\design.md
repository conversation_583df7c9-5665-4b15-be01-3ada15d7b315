# Design Document

## Overview

本设计文档描述如何修改不干胶标签打印功能中营业员信息的显示格式，将"营业员: [营业员姓名]"简化为只显示"[营业员姓名]"。

## Architecture

修改涉及以下组件：
- **Lodop打印模块** (`static/js/lodop-print.js`)：负责实际的不干胶标签打印
- **网页预览模块** (`templates/sticky_label_print.html`)：负责标签的网页预览显示
- **打印函数模块** (`static/js/print-functions.js`)：可能包含相关的打印逻辑

## Components and Interfaces

### 1. Lodop打印组件 (lodop-print.js)

**位置**: `static/js/lodop-print.js` 中的 `printStickyLabel` 函数

**当前实现**:
```javascript
// 头部：营业员信息，居中加粗
LODOP.ADD_PRINT_TEXT("6mm", "1mm", "70mm", "6mm", `营业员: ${labelData.operator || '未知'}`);
```

**修改后实现**:
```javascript
// 头部：营业员信息，居中加粗
LODOP.ADD_PRINT_TEXT("6mm", "1mm", "70mm", "6mm", `${labelData.operator || '未知'}`);
```

### 2. 网页预览组件 (sticky_label_print.html)

**位置**: `templates/sticky_label_print.html` 中的 `renderLabel` 函数

**当前实现**:
```javascript
<div class="store-name">营业员：${data.operator || '未知营业员'}</div>
```

**修改后实现**:
```javascript
<div class="store-name">${data.operator || '未知营业员'}</div>
```

## Data Models

### 标签数据结构
```javascript
{
    operator: string,        // 营业员姓名
    order_number: string,    // 订单号
    customer_name: string,   // 客户姓名
    customer_phone: string,  // 客户电话
    // ... 其他字段
}
```

**数据处理逻辑**:
- 当 `operator` 为空、null或undefined时，显示"未知"或"未知营业员"
- 直接使用营业员姓名，不添加"营业员:"前缀

## Error Handling

### 异常情况处理
1. **营业员信息为空**: 显示"未知"作为默认值
2. **数据获取失败**: 保持现有的错误处理机制不变
3. **打印失败**: 不影响现有的错误处理和日志记录

### 兼容性考虑
- 确保修改不影响其他打印功能（水洗唛、小票）
- 保持与现有API接口的兼容性

## Testing Strategy

### 测试用例

#### 1. 功能测试
- **正常营业员姓名**: 验证显示格式为"[营业员姓名]"
- **空营业员信息**: 验证显示"未知"或"未知营业员"
- **特殊字符营业员名**: 验证特殊字符正确显示

#### 2. 集成测试
- **Lodop打印测试**: 验证实际打印输出格式正确
- **网页预览测试**: 验证预览显示与打印一致
- **跨浏览器测试**: 确保在不同浏览器中显示一致

#### 3. 回归测试
- **水洗唛打印**: 确保不影响水洗唛中的营业员信息显示
- **小票打印**: 确保不影响小票中的营业员信息显示
- **其他功能**: 验证其他相关功能正常工作

## Implementation Notes

### 修改范围
1. **主要修改**: 移除"营业员:"和"营业员："前缀
2. **样式保持**: 保持字体、大小、位置等样式不变
3. **布局调整**: 可能需要微调文本对齐以保持美观

### 代码变更影响
- 变更范围小，风险较低
- 不涉及数据结构变更
- 不影响API接口

### 部署考虑
- 可以直接部署，无需数据库迁移
- 建议在测试环境验证后再部署到生产环境