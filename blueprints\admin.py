from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for, flash
from models import *
from blueprints.auth import login_required
import datetime
from werkzeug.security import generate_password_hash, check_password_hash

admin_bp = Blueprint('admin', __name__)
bp = admin_bp  # 为了与自动注册系统兼容

@admin_bp.route('/user_management')
@login_required
def user_management():
    """用户管理页面"""
    # 只有管理员或系统经理可以访问用户管理
    if session.get('staff_role') not in ['admin', 'manager']:
        flash('您没有权限访问用户管理功能', 'error')
        return redirect(url_for('index'))
    
    staff_name = session.get('staff_name', '未登录')
    staff_area = session.get('staff_area', '')
    
    return render_template('user_management.html', 
                         staff_name=staff_name, 
                         staff_area=staff_area)

# 用户管理API
@admin_bp.route('/api/users')
@login_required
def get_users():
    """获取用户列表"""
    try:
        # 只有管理员或系统经理可以查看用户列表
        if session.get('staff_role') not in ['admin', 'manager']:
            return jsonify({'error': '权限不足'}), 403
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '')
        status = request.args.get('status', '')
        role = request.args.get('role', '')
        area = request.args.get('area', '')
        
        # 构建查询
        query = Staff.query
        
        # 应用搜索过滤
        if search:
            query = query.filter(
                db.or_(
                    Staff.username.like(f'%{search}%'),
                    Staff.name.like(f'%{search}%'),
                    Staff.phone.like(f'%{search}%')
                )
            )
        
        # 应用状态过滤
        if status:
            if status == 'active':
                query = query.filter(Staff.is_active == True)
            elif status == 'inactive':
                query = query.filter(Staff.is_active == False)
        
        # 应用角色过滤
        if role:
            query = query.filter(Staff.role == role)
        
        # 应用区域过滤
        if area:
            query = query.filter(Staff.area == area)
        
        # 分页
        paginated_users = query.order_by(Staff.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        result = []
        for user in paginated_users.items:
            result.append({
                'id': user.id,
                'username': user.username,
                'name': user.name,
                'phone': user.phone,
                'role': user.role,
                'area': user.area,
                'is_active': user.is_active,
                'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'last_login': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else ''
            })
        
        return jsonify({
            'success': True,
            'users': result,
            'pagination': {
                'page': page,
                'pages': paginated_users.pages,
                'total': paginated_users.total,
                'per_page': per_page
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/users/<int:user_id>', methods=['GET'])
@login_required
def get_user(user_id):
    """获取单个用户信息"""
    try:
        # 只有管理员或系统经理可以查看用户详情
        if session.get('staff_role') not in ['admin', 'manager']:
            return jsonify({'error': '权限不足'}), 403
        
        user = Staff.query.get_or_404(user_id)
        
        user_data = {
            'id': user.id,
            'username': user.username,
            'name': user.name,
            'phone': user.phone,
            'role': user.role,
            'area': user.area,
            'is_active': user.is_active,
            'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'last_login': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else ''
        }
        
        return jsonify({
            'success': True,
            'user': user_data
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/users', methods=['POST'])
@login_required
def create_user():
    """创建新用户"""
    try:
        # 只有管理员或系统经理可以创建用户
        if session.get('staff_role') not in ['admin', 'manager']:
            return jsonify({'error': '权限不足'}), 403
        
        data = request.json
        username = data.get('username', '').strip()
        name = data.get('name', '').strip()
        phone = data.get('phone', '').strip()
        password = data.get('password', '').strip()
        role = data.get('role', 'staff')
        area = data.get('area', '').strip()
        is_active = data.get('is_active', True)
        
        # 验证必填字段
        if not all([username, name, password]):
            return jsonify({'error': '用户名、姓名和密码不能为空'}), 400
        
        # 检查用户名是否已存在
        if Staff.query.filter_by(username=username).first():
            return jsonify({'error': '用户名已存在'}), 400
        
        # 创建新用户
        new_user = Staff(
            username=username,
            name=name,
            phone=phone,
            password=generate_password_hash(password),
            role=role,
            area=area,
            is_active=is_active,
            created_at=datetime.datetime.now()
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '用户创建成功',
            'user_id': new_user.id
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/users/<int:user_id>', methods=['PUT'])
@login_required
def update_user(user_id):
    """更新用户信息"""
    try:
        # 只有管理员或系统经理可以更新用户
        if session.get('staff_role') not in ['admin', 'manager']:
            return jsonify({'error': '权限不足'}), 403
        
        user = Staff.query.get_or_404(user_id)
        data = request.json
        
        # 更新基本信息
        if 'name' in data:
            user.name = data['name'].strip()
        if 'phone' in data:
            user.phone = data['phone'].strip()
        if 'role' in data:
            user.role = data['role']
        if 'area' in data:
            user.area = data['area'].strip()
        if 'is_active' in data:
            user.is_active = data['is_active']
        
        # 更新密码（如果提供）
        if 'password' in data and data['password'].strip():
            user.password = generate_password_hash(data['password'].strip())
        
        # 检查用户名是否重复（如果要更新用户名）
        if 'username' in data:
            new_username = data['username'].strip()
            if new_username != user.username:
                if Staff.query.filter_by(username=new_username).first():
                    return jsonify({'error': '用户名已存在'}), 400
                user.username = new_username
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '用户信息更新成功'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/users/<int:user_id>', methods=['DELETE'])
@login_required
def delete_user(user_id):
    """删除用户"""
    try:
        # 只有管理员或系统经理可以删除用户
        if session.get('staff_role') not in ['admin', 'manager']:
            return jsonify({'error': '权限不足'}), 403
        
        user = Staff.query.get_or_404(user_id)
        
        # 不允许删除自己
        current_user_id = session.get('staff_id')
        if user.id == current_user_id:
            return jsonify({'error': '不能删除自己的账号'}), 400
        
        # 不允许删除admin用户
        if user.username == 'admin':
            return jsonify({'error': '不能删除admin账号'}), 400
        
        db.session.delete(user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '用户删除成功'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 系统管理API
@admin_bp.route('/api/areas', methods=['GET'])
@login_required
def get_areas():
    """获取所有区域列表"""
    try:
        # 从Staff表中获取所有不重复的区域
        areas_query = db.session.query(Staff.area).filter(
            Staff.area.isnot(None),
            Staff.area != ''
        ).distinct().all()
        
        areas = [area[0] for area in areas_query if area[0]]
        
        # 添加一些默认区域（如果不存在）
        default_areas = ['总部', '分店1', '分店2']
        for area in default_areas:
            if area not in areas:
                areas.append(area)
        
        areas.sort()
        
        return jsonify({
            'success': True,
            'areas': areas
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/staff_list', methods=['GET'])
@login_required
def get_staff_list():
    """获取营业员列表（用于下拉选择等）"""
    try:
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        
        # 构建查询
        query = Staff.query.filter(Staff.is_active == True, Staff.role == 'staff')
        
        # 如果不是管理员且不在总部，只显示同区域的员工
        if staff_role != 'admin' and staff_area != '总部' and staff_area:
            query = query.filter(Staff.area == staff_area)
        
        staff_list = query.order_by(Staff.name).all()
        
        result = []
        for staff in staff_list:
            result.append({
                'id': staff.id,
                'name': staff.name,
                'username': staff.username,
                'area': staff.area,
                'role': staff.role
            })
        
        return jsonify({
            'staff': result
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 门店配置管理API
@admin_bp.route('/api/admin/stores', methods=['GET'])
@login_required
def get_admin_stores():
    """管理员获取所有门店列表（营业员代表门店）"""
    try:
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '需要管理员权限'}), 403
            
        # 获取所有角色为'staff'的用户，他们代表门店
        staff_stores = Staff.query.filter_by(role='staff', is_active=True).all()
        
        store_list = []
        for staff in staff_stores:
            store_list.append({
                'id': staff.id,
                'store_name': staff.name,  # 使用营业员姓名作为门店名
                'staff_name': staff.name,
                'staff_username': staff.username,
                'area': staff.area,
                'phone': staff.phone
            })
        
        return jsonify({
            'success': True,
            'stores': store_list
        })
        
    except Exception as e:
        print(f"获取门店列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/admin/rack-summary', methods=['GET'])
@login_required
def get_admin_rack_summary():
    """管理员获取格架使用摘要"""
    try:
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '需要管理员权限'}), 403
            
        store_name = request.args.get('store')
        
        # 基础查询：所有已分配格架的衣物
        base_query = db.session.query(Clothing).filter(Clothing.slot_no.isnot(None))
        
        # 如果指定了门店（营业员姓名），通过订单表关联过滤
        if store_name:
            # 根据营业员姓名过滤订单
            base_query = base_query.join(Order).filter(Order.operator == store_name)
        
        occupied_slots = base_query.all()
        
        # 总格架数（固定为1400：700*2侧）
        total_slots = 1400
        occupied_count = len(occupied_slots)
        free_count = total_slots - occupied_count
        usage_rate = round(occupied_count / total_slots * 100, 2) if total_slots > 0 else 0
        
        # 按侧别统计
        lane_stats = {}
        for lane in ['A', 'B']:
            lane_count = len([slot for slot in occupied_slots if slot.lane == lane])
            lane_stats[lane] = {
                'occupied': lane_count,
                'free': 700 - lane_count,
                'usage_rate': round(lane_count / 700 * 100, 2)
            }
        
        # 按时间段统计
        from datetime import datetime, timedelta
        now = datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today_start - timedelta(days=now.weekday())
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        time_stats = {
            'today': len([slot for slot in occupied_slots if slot.slot_time and slot.slot_time >= today_start]),
            'this_week': len([slot for slot in occupied_slots if slot.slot_time and slot.slot_time >= week_start]),
            'this_month': len([slot for slot in occupied_slots if slot.slot_time and slot.slot_time >= month_start])
        }
        
        return jsonify({
            'success': True,
            'summary': {
                'total_slots': total_slots,
                'occupied_count': occupied_count,
                'free_count': free_count,
                'usage_rate': usage_rate,
                'lane_stats': lane_stats,
                'time_stats': time_stats
            }
        })
        
    except Exception as e:
        print(f"获取格架摘要失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/admin/store-configs', methods=['GET'])
@login_required
def get_admin_store_configs():
    """管理员获取门店配置列表（营业员信息）"""
    try:
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '需要管理员权限'}), 403
            
        # 获取所有营业员的信息作为门店配置
        staff_configs = Staff.query.filter_by(role='staff').all()
        
        config_list = []
        for staff in staff_configs:
            config_list.append({
                'id': staff.id,
                'store_name': staff.name,
                'staff_username': staff.username,
                'staff_name': staff.name,
                'area': staff.area,
                'phone': staff.phone,
                'is_active': staff.is_active,
                'created_at': staff.created_at.isoformat() if staff.created_at else '',
                'last_login': staff.last_login.isoformat() if staff.last_login else ''
            })
        
        return jsonify({
            'success': True,
            'configs': config_list
        })
        
    except Exception as e:
        print(f"获取门店配置失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/admin/store-configs', methods=['POST'])
@login_required
def add_admin_store_config():
    """管理员添加门店配置（创建营业员）"""
    try:
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '需要管理员权限'}), 403
            
        data = request.json
        staff_name = data.get('staff_name', '').strip()
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        area = data.get('area', '').strip()
        phone = data.get('phone', '').strip()
        
        if not staff_name or not username or not password:
            return jsonify({'error': '请填写营业员姓名、用户名和密码'}), 400
        
        # 检查用户名是否已存在
        existing = Staff.query.filter_by(username=username).first()
        if existing:
            return jsonify({'error': '用户名已存在'}), 400
        
        # 检查姓名是否已存在（避免门店名重复）
        existing_name = Staff.query.filter_by(name=staff_name, role='staff').first()
        if existing_name:
            return jsonify({'error': '该营业员姓名已存在'}), 400
        
        # 创建新营业员
        new_staff = Staff(
            username=username,
            name=staff_name,
            password=generate_password_hash(password),
            role='staff',
            area=area,
            phone=phone,
            is_active=True,
            created_at=datetime.datetime.now()
        )
        
        db.session.add(new_staff)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '营业员（门店）添加成功',
            'staff_id': new_staff.id
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"添加营业员失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/admin/store-configs/<int:staff_id>', methods=['PUT'])
@login_required
def update_admin_store_config(staff_id):
    """管理员更新门店配置（更新营业员）"""
    try:
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '需要管理员权限'}), 403
            
        staff = Staff.query.get(staff_id)
        if not staff or staff.role != 'staff':
            return jsonify({'error': '营业员不存在'}), 404
        
        data = request.json
        
        # 更新字段
        if 'staff_name' in data:
            # 检查新姓名是否与其他营业员冲突
            existing = Staff.query.filter(
                Staff.name == data['staff_name'],
                Staff.role == 'staff',
                Staff.id != staff_id
            ).first()
            if existing:
                return jsonify({'error': '营业员姓名已存在'}), 400
            staff.name = data['staff_name']
        
        if 'username' in data:
            # 检查新用户名是否与其他用户冲突
            existing = Staff.query.filter(
                Staff.username == data['username'],
                Staff.id != staff_id
            ).first()
            if existing:
                return jsonify({'error': '用户名已存在'}), 400
            staff.username = data['username']
        
        if 'area' in data:
            staff.area = data['area']
        
        if 'phone' in data:
            staff.phone = data['phone']
        
        if 'is_active' in data:
            staff.is_active = data['is_active']
        
        if 'password' in data and data['password'].strip():
            staff.password = generate_password_hash(data['password'].strip())
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '营业员信息更新成功'
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"更新营业员信息失败: {str(e)}")
        return jsonify({'error': str(e)}), 500 