from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for, flash
from models import *
from blueprints.auth import login_required
import datetime
from sqlalchemy.orm import joinedload, selectinload

mall_customer_bp = Blueprint('mall_customer', __name__)
bp = mall_customer_bp  # 为了与自动注册系统兼容

@mall_customer_bp.route('/mall_customer_management')
@login_required
def mall_customer_management():
    """商场客户管理页面"""
    # 检查权限：只有manager才能访问
    if session.get('staff_role') != 'manager':
        flash('您没有权限访问商场客户管理功能', 'error')
        return redirect(url_for('index'))
    
    staff_name = session.get('staff_name', '未登录')
    staff_area = session.get('staff_area', '')
    
    return render_template('mall_customer_management.html', 
                         staff_name=staff_name, 
                         staff_area=staff_area)

@mall_customer_bp.route('/api/mall_customers', methods=['GET'])
@login_required
def get_mall_customers():
    """获取商场客户列表"""
    try:
        # 获取查询参数
        search_term = request.args.get('search', '')
        status = request.args.get('status', '')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # 构建查询，使用 selectinload 预加载关联数据避免 N+1 查询
        customer_query = MallCustomer.query.options(
            selectinload(MallCustomer.product_discounts),
            selectinload(MallCustomer.monthly_bills)
        )

        # 根据权限过滤查询
        staff_role = session.get('staff_role', '')
        staff_area = session.get('staff_area', '')
        
        # admin和总部用户可以查看所有数据
        if staff_role != 'admin' and staff_area != '总部':
            # 其他用户只能查看自己区域的数据
            if staff_area:
                customer_query = customer_query.filter(MallCustomer.area == staff_area)

        # 应用搜索过滤器
        if search_term:
            customer_query = customer_query.filter(MallCustomer.mall_name.like(f'%{search_term}%'))

        # 应用状态过滤器
        if status:
            customer_query = customer_query.filter(MallCustomer.status == status)

        # 按名称排序
        customer_query = customer_query.order_by(MallCustomer.mall_name)

        # 分页
        paginated_customers = customer_query.paginate(page=page, per_page=per_page, error_out=False)

        # 构建结果数据
        result = []
        for customer in paginated_customers.items:
            # 从预加载的关联数据中获取最新账单状态
            billing_status = '无账单'
            if customer.monthly_bills:
                latest_bill = max(customer.monthly_bills, key=lambda x: x.bill_year_month)
                billing_status = latest_bill.payment_status

            # 从预加载的关联数据中获取折扣数量
            discount_count = len(customer.product_discounts)

            result.append({
                'id': customer.id,
                'mall_name': customer.mall_name,
                'contact_name': customer.contact_name,
                'phone': customer.phone,
                'billing_cycle': customer.billing_cycle,
                'status': customer.status,
                'contract_end_date': customer.contract_end_date.strftime('%Y-%m-%d') if customer.contract_end_date else '',
                'billing_status': billing_status,
                'discount_count': discount_count,
                'overall_discount_rate': customer.overall_discount_rate,
                'created_at': customer.created_at.strftime('%Y-%m-%d')
            })

        return jsonify({
            'customers': result,
            'total': paginated_customers.total,
            'pages': paginated_customers.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/mall_customers', methods=['POST'])
@login_required
def create_mall_customer():
    """创建新的商场客户"""
    # 检查权限：只有manager才能创建
    if session.get('staff_role') != 'manager':
        return jsonify({'error': '您没有权限执行此操作'}), 403
    
    try:
        data = request.json or {}
        staff_area = session.get('staff_area', '')
        
        # 验证必填字段
        mall_name = data.get('mall_name', '').strip()
        if not mall_name:
            return jsonify({'error': '商场品牌名称不能为空'}), 400
        
        area = data.get('area') or staff_area
        
        # 检查商场名称是否已存在
        existing_customer = MallCustomer.query.filter_by(
            mall_name=mall_name,
            area=area
        ).first()
        
        if existing_customer:
            return jsonify({'error': f'商场品牌名称"{mall_name}"在区域"{area}"已存在，请使用不同的名称'}), 400
        
        # 处理日期字段
        contract_start_date = None
        contract_end_date = None
        
        if data.get('contract_start_date'):
            contract_start_date = datetime.datetime.strptime(data['contract_start_date'], '%Y-%m-%d').date()
        
        if data.get('contract_end_date'):
            contract_end_date = datetime.datetime.strptime(data['contract_end_date'], '%Y-%m-%d').date()
        
        # 创建新客户
        new_customer = MallCustomer(
            mall_name=mall_name,
            address=data.get('address', ''),
            phone=data.get('phone', ''),
            contract_start_date=contract_start_date,
            contract_end_date=contract_end_date,
            billing_cycle=data.get('billing_cycle', '月结'),
            contact_name=data.get('contact_name', ''),
            contact_phone=data.get('contact_phone', ''),
            contact_position=data.get('contact_position', ''),
            status=data.get('status', '正常'),
            remarks=data.get('remarks', ''),
            overall_discount_rate=float(data.get('overall_discount_rate', 1.0)),
            area=area
        )
        
        db.session.add(new_customer)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '商场客户创建成功',
            'customer_id': new_customer.id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/mall_customers/<int:customer_id>', methods=['GET'])
@login_required
def get_mall_customer(customer_id):
    """获取商场客户详情"""
    try:
        customer = MallCustomer.query.get_or_404(customer_id)

        # 验证区域权限
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
            return jsonify({'error': '您没有权限查看此商场客户'}), 403

        return jsonify({
            'id': customer.id,
            'mall_name': customer.mall_name,
            'address': customer.address,
            'phone': customer.phone,
            'contract_start_date': customer.contract_start_date.strftime('%Y-%m-%d') if customer.contract_start_date else '',
            'contract_end_date': customer.contract_end_date.strftime('%Y-%m-%d') if customer.contract_end_date else '',
            'billing_cycle': customer.billing_cycle,
            'contact_name': customer.contact_name,
            'contact_phone': customer.contact_phone,
            'contact_position': customer.contact_position,
            'status': customer.status,
            'remarks': customer.remarks,
            'overall_discount_rate': customer.overall_discount_rate,
            'area': customer.area,
            'created_at': customer.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/mall_customers/<int:customer_id>', methods=['PUT'])
@login_required
def update_mall_customer(customer_id):
    """更新商场客户信息"""
    if session.get('staff_role') != 'manager':
        return jsonify({'error': '您没有权限执行此操作'}), 403

    try:
        customer = MallCustomer.query.get_or_404(customer_id)

        # 验证区域权限
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
            return jsonify({'error': '您没有权限修改此商场客户'}), 403

        data = request.json

        # 验证区域变更权限
        if 'area' in data and data['area'] != customer.area:
            if staff_role != 'admin' and staff_area != '总部':
                return jsonify({'error': '您没有权限修改客户所属区域'}), 403

        # 更新基础信息
        if 'mall_name' in data:
            new_mall_name = data['mall_name'].strip()
            if not new_mall_name:
                return jsonify({'error': '商场品牌名称不能为空'}), 400

            # 检查新名称是否与其他客户重复
            if new_mall_name != customer.mall_name:
                existing_customer = MallCustomer.query.filter(
                    MallCustomer.mall_name == new_mall_name,
                    MallCustomer.area == customer.area,
                    MallCustomer.id != customer.id
                ).first()

                if existing_customer:
                    return jsonify({'error': f'商场品牌名称"{new_mall_name}"在区域"{customer.area}"已存在，请使用不同的名称'}), 400

            customer.mall_name = new_mall_name
        
        if 'address' in data:
            customer.address = data['address']
        if 'phone' in data:
            customer.phone = data['phone']
        if 'contract_start_date' in data and data['contract_start_date']:
            customer.contract_start_date = datetime.datetime.strptime(data['contract_start_date'], '%Y-%m-%d').date()
        if 'contract_end_date' in data and data['contract_end_date']:
            customer.contract_end_date = datetime.datetime.strptime(data['contract_end_date'], '%Y-%m-%d').date()
        if 'billing_cycle' in data:
            customer.billing_cycle = data['billing_cycle']
        if 'contact_name' in data:
            customer.contact_name = data['contact_name']
        if 'contact_phone' in data:
            customer.contact_phone = data['contact_phone']
        if 'contact_position' in data:
            customer.contact_position = data['contact_position']
        if 'status' in data:
            customer.status = data['status']
        if 'remarks' in data:
            customer.remarks = data['remarks']
        if 'overall_discount_rate' in data:
            customer.overall_discount_rate = float(data['overall_discount_rate'])
        if 'area' in data:
            customer.area = data['area']

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '商场客户信息更新成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/mall_customers/<int:customer_id>', methods=['DELETE'])
@login_required
def delete_mall_customer(customer_id):
    """删除商场客户"""
    if session.get('staff_role') != 'manager':
        return jsonify({'error': '您没有权限执行此操作'}), 403

    try:
        customer = MallCustomer.query.get_or_404(customer_id)

        # 验证区域权限
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
            return jsonify({'error': '您没有权限删除此商场客户'}), 403

        # 检查是否有关联的订单
        related_orders = Order.query.filter_by(mall_customer_id=customer_id).count()
        related_customers = Customer.query.filter_by(mall_customer_id=customer_id).count()

        force_delete = request.args.get('force', 'false').lower() == 'true'

        if (related_orders > 0 or related_customers > 0) and not force_delete:
            error_msg = f'无法删除商场客户"{customer.mall_name}"，存在以下关联数据：\n'
            if related_orders > 0:
                error_msg += f'• {related_orders} 个关联订单\n'
            if related_customers > 0:
                error_msg += f'• {related_customers} 个关联的普通客户记录\n'
            error_msg += '\n请确认是否要强制删除（这将清除所有关联数据）？'

            return jsonify({
                'error': error_msg,
                'has_relations': True,
                'related_orders': related_orders,
                'related_customers': related_customers
            }), 400

        # 如果强制删除，先处理关联数据
        if force_delete:
            if related_orders > 0:
                Order.query.filter_by(mall_customer_id=customer_id).update({
                    'mall_customer_id': None,
                    'is_mall_order': False
                })

            if related_customers > 0:
                Customer.query.filter_by(mall_customer_id=customer_id).update({
                    'mall_customer_id': None,
                    'is_mall_customer': False
                })

        # 级联删除相关数据
        MallProductDiscount.query.filter_by(mall_customer_id=customer_id).delete()
        MallMonthlyBill.query.filter_by(mall_customer_id=customer_id).delete()
        MallDiscountHistory.query.filter_by(mall_customer_id=customer_id).delete()

        db.session.delete(customer)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '商场客户已删除'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 产品折扣管理路由
@mall_customer_bp.route('/api/mall_customers/<int:customer_id>/discounts', methods=['GET'])
@login_required
def get_customer_discounts(customer_id):
    """获取商场客户的产品折扣列表"""
    try:
        customer = MallCustomer.query.get_or_404(customer_id)
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
            return jsonify({'error': '您没有权限查看此商场客户的折扣信息'}), 403

        discounts = MallProductDiscount.query.filter_by(mall_customer_id=customer_id).all()
        result = []

        for discount in discounts:
            result.append({
                'id': discount.id,
                'product_name': discount.product_name,
                'product_type': discount.product_type,
                'discount_rate': discount.discount_rate,
                'effective_date': discount.effective_date.strftime('%Y-%m-%d'),
                'expiry_date': discount.expiry_date.strftime('%Y-%m-%d') if discount.expiry_date else '长期有效',
                'created_by': discount.created_by,
                'created_at': discount.created_at.strftime('%Y-%m-%d'),
                'change_reason': ''
            })

        return jsonify({'discounts': result})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/mall_customers/<int:customer_id>/discounts', methods=['POST'])
@login_required
def create_product_discount(customer_id):
    """创建商场客户产品折扣"""
    # 检查权限：只有manager才能创建折扣
    if session.get('staff_role') != 'manager':
        return jsonify({'error': '您没有权限执行此操作'}), 403
    
    try:
        customer = MallCustomer.query.get_or_404(customer_id)
        
        # 检查区域权限
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        staff_name = session.get('staff_name', '未知')
        
        if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
            return jsonify({'error': '您没有权限为此商场客户创建折扣'}), 403
        
        data = request.json or {}
        
        # 验证必填字段
        required_fields = ['product_name', 'product_type', 'discount_rate', 'effective_date']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'字段 {field} 不能为空'}), 400
        
        # 验证折扣率
        try:
            discount_rate = float(data['discount_rate'])
            if not (0 < discount_rate <= 1):
                return jsonify({'error': '折扣率必须在0和1之间'}), 400
        except ValueError:
            return jsonify({'error': '折扣率必须是有效的数字'}), 400
        
        # 处理日期
        effective_date = datetime.datetime.strptime(data['effective_date'], '%Y-%m-%d').date()
        expiry_date = None
        if data.get('expiry_date'):
            expiry_date = datetime.datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
            
            # 验证日期范围
            if expiry_date <= effective_date:
                return jsonify({'error': '失效日期必须晚于生效日期'}), 400
        
        # 检查该产品是否已有折扣
        existing_discount = MallProductDiscount.query.filter_by(
            mall_customer_id=customer_id,
            product_name=data['product_name'],
            product_type=data['product_type']
        ).first()
        
        if existing_discount:
            # 创建折扣变更历史
            discount_history = MallDiscountHistory(
                mall_customer_id=customer_id,
                product_name=data['product_name'],
                product_type=data['product_type'],
                old_discount_rate=existing_discount.discount_rate,
                new_discount_rate=discount_rate,
                change_date=datetime.datetime.now().date(),
                change_reason=data.get('change_reason', '定期调整'),
                operator=staff_name
            )
            db.session.add(discount_history)
            
            # 更新折扣
            existing_discount.discount_rate = discount_rate
            existing_discount.effective_date = effective_date
            existing_discount.expiry_date = expiry_date
            existing_discount.updated_at = datetime.datetime.now()
            
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': '产品折扣已更新',
                'discount_id': existing_discount.id
            })
        
        # 创建新的产品折扣
        new_discount = MallProductDiscount(
            mall_customer_id=customer_id,
            product_name=data['product_name'],
            product_type=data['product_type'],
            discount_rate=discount_rate,
            effective_date=effective_date,
            expiry_date=expiry_date,
            created_by=staff_name
        )
        
        db.session.add(new_discount)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '产品折扣创建成功',
            'discount_id': new_discount.id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/discounts/<int:discount_id>', methods=['PUT'])
@login_required
def update_product_discount(discount_id):
    """更新产品折扣"""
    if session.get('staff_role') != 'manager':
        return jsonify({'error': '您没有权限执行此操作'}), 403

    try:
        discount = MallProductDiscount.query.get_or_404(discount_id)
        data = request.json

        # 如果折扣率有变化，记录历史
        if 'discount_rate' in data and float(data['discount_rate']) != discount.discount_rate:
            discount_history = MallDiscountHistory(
                mall_customer_id=discount.mall_customer_id,
                product_name=discount.product_name,
                product_type=discount.product_type,
                old_discount_rate=discount.discount_rate,
                new_discount_rate=float(data['discount_rate']),
                change_date=datetime.datetime.now().date(),
                change_reason=data.get('change_reason', '定期调整'),
                operator=session.get('staff_name', '未知')
            )
            db.session.add(discount_history)
            discount.discount_rate = float(data['discount_rate'])

        if 'effective_date' in data and data['effective_date']:
            discount.effective_date = datetime.datetime.strptime(data['effective_date'], '%Y-%m-%d').date()

        if 'expiry_date' in data:
            if data['expiry_date']:
                discount.expiry_date = datetime.datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
            else:
                discount.expiry_date = None

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '产品折扣更新成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/discounts/<int:discount_id>', methods=['DELETE'])
@login_required
def delete_product_discount(discount_id):
    """删除产品折扣"""
    if session.get('staff_role') != 'manager':
        return jsonify({'error': '您没有权限执行此操作'}), 403

    try:
        discount = MallProductDiscount.query.get_or_404(discount_id)

        # 记录折扣删除历史
        discount_history = MallDiscountHistory(
            mall_customer_id=discount.mall_customer_id,
            product_name=discount.product_name,
            product_type=discount.product_type,
            old_discount_rate=discount.discount_rate,
            new_discount_rate=1.0,
            change_date=datetime.datetime.now().date(),
            change_reason='折扣删除',
            operator=session.get('staff_name', '未知')
        )
        db.session.add(discount_history)

        db.session.delete(discount)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '产品折扣已删除'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/discounts/<int:discount_id>', methods=['GET'])
@login_required
def get_discount_details(discount_id):
    """获取单个产品折扣详情"""
    try:
        discount = MallProductDiscount.query.get_or_404(discount_id)

        result = {
            'id': discount.id,
            'mall_customer_id': discount.mall_customer_id,
            'product_name': discount.product_name,
            'product_type': discount.product_type,
            'discount_rate': discount.discount_rate,
            'effective_date': discount.effective_date.strftime('%Y-%m-%d'),
            'expiry_date': discount.expiry_date.strftime('%Y-%m-%d') if discount.expiry_date else '长期有效',
            'created_by': discount.created_by,
            'created_at': discount.created_at.strftime('%Y-%m-%d'),
            'change_reason': ''
        }

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 月度账单管理路由
@mall_customer_bp.route('/api/mall_customers/<int:customer_id>/bills', methods=['GET'])
@login_required
def get_customer_bills(customer_id):
    """获取商场客户的月度账单列表"""
    try:
        customer = MallCustomer.query.get_or_404(customer_id)
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
            return jsonify({'error': '您没有权限查看此商场客户的账单信息'}), 403

        bills = MallMonthlyBill.query.filter_by(mall_customer_id=customer_id).order_by(MallMonthlyBill.bill_year_month.desc()).all()
        result = []

        for bill in bills:
            result.append({
                'id': bill.id,
                'bill_year_month': bill.bill_year_month,
                'bill_start_date': bill.bill_start_date.strftime('%Y-%m-%d'),
                'bill_end_date': bill.bill_end_date.strftime('%Y-%m-%d'),
                'order_count': bill.order_count,
                'original_amount': bill.original_amount or 0.0,
                'total_amount': bill.total_amount,
                'discount_amount': bill.discount_amount,
                'actual_amount': bill.actual_amount,
                'payment_status': bill.payment_status,
                'payment_date': bill.payment_date.strftime('%Y-%m-%d') if bill.payment_date else '',
                'payment_method': bill.payment_method or '',
                'remarks': bill.remarks or ''
            })

        return jsonify({'bills': result})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/mall_customers/<int:customer_id>/bills', methods=['POST'])
@login_required
def create_monthly_bill(customer_id):
    """创建或更新商场客户月度账单"""
    try:
        mall_customer = MallCustomer.query.get(customer_id)
        if not mall_customer:
            return jsonify({'error': '商场客户不存在'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400

        # 修改已存在的账单
        if 'bill_id' in data:
            bill = MallMonthlyBill.query.get(data['bill_id'])
            if not bill:
                return jsonify({'error': '账单不存在'}), 404

            if bill.mall_customer_id != customer_id:
                return jsonify({'error': '账单与客户不匹配'}), 400

            previous_payment_status = bill.payment_status
            updated_orders_count = 0

            if 'payment_status' in data:
                bill.payment_status = data['payment_status']

                # 当账单状态变为"已付款"时，同步更新关联订单的支付状态
                if data['payment_status'] == "已付款" and previous_payment_status != "已付款":
                    payment_time = datetime.datetime.now()
                    # 批量更新订单支付状态，避免循环中的单独更新
                    updated_orders_count = Order.query.filter(
                        Order.mall_customer_id == bill.mall_customer_id,
                        Order.is_mall_order == True,
                        Order.created_at >= bill.bill_start_date,
                        Order.created_at <= bill.bill_end_date + datetime.timedelta(days=1),
                        Order.payment_status == "未付款"
                    ).update({
                        'payment_status': "已付款",
                        'payment_time': payment_time
                    }, synchronize_session=False)

                    print(f"已更新 {updated_orders_count} 个关联订单的支付状态为'已付款'")

            if 'payment_date' in data:
                if data['payment_date']:
                    bill.payment_date = datetime.datetime.strptime(data['payment_date'], '%Y-%m-%d').date()
                else:
                    bill.payment_date = None

            if 'payment_method' in data:
                bill.payment_method = data['payment_method']

            if 'remarks' in data:
                bill.remarks = data['remarks']

            db.session.commit()
            return jsonify({'success': True, 'message': '账单状态更新成功'})

        # 创建新的月度账单
        new_bill = MallMonthlyBill(
            mall_customer_id=customer_id,
            bill_year_month=data['bill_year_month'],
            bill_start_date=datetime.datetime.strptime(data['bill_start_date'], '%Y-%m-%d').date(),
            bill_end_date=datetime.datetime.strptime(data['bill_end_date'], '%Y-%m-%d').date(),
            order_count=data.get('order_count', 0),
            total_amount=data.get('total_amount', 0.0),
            discount_amount=data.get('discount_amount', 0.0),
            actual_amount=data.get('actual_amount', 0.0),
            payment_status=data.get('payment_status', '未付款'),
            payment_date=datetime.datetime.strptime(data['payment_date'], '%Y-%m-%d').date() if data.get('payment_date') else None,
            payment_method=data.get('payment_method'),
            remarks=data.get('remarks')
        )

        db.session.add(new_bill)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '月度账单创建成功',
            'bill_id': new_bill.id
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/bills/<int:bill_id>', methods=['PUT'])
@login_required
def update_monthly_bill(bill_id):
    """更新月度账单"""
    if session.get('staff_role') != 'manager':
        return jsonify({'error': '您没有权限执行此操作'}), 403

    try:
        bill = MallMonthlyBill.query.get_or_404(bill_id)
        data = request.json

        previous_payment_status = bill.payment_status
        updated_orders_count = 0

        # 检查是否需要更新账单日期范围
        date_range_updated = False
        if 'bill_start_date' in data or 'bill_end_date' in data:
            date_range_updated = True
            if 'bill_start_date' in data and data['bill_start_date']:
                bill.bill_start_date = datetime.datetime.strptime(data['bill_start_date'], '%Y-%m-%d').date()
            if 'bill_end_date' in data and data['bill_end_date']:
                bill.bill_end_date = datetime.datetime.strptime(data['bill_end_date'], '%Y-%m-%d').date()

        # 如果日期范围更新了，重新计算账单统计数据
        if date_range_updated:
            # 优化：使用 selectinload 预加载衣物数据，避免 N+1 查询
            orders = Order.query.options(
                selectinload(Order.clothes)
            ).filter(
                Order.mall_customer_id == bill.mall_customer_id,
                Order.is_mall_order == True,
                Order.created_at >= bill.bill_start_date,
                Order.created_at <= bill.bill_end_date + datetime.timedelta(days=1)
            ).all()

            bill.order_count = len(orders)
            bill.original_amount = 0
            bill.total_amount = 0
            bill.discount_amount = 0

            for order in orders:
                # 使用预加载的衣物数据，避免重复查询
                clothes = order.clothes
                order_original_amount = 0
                for clothing in clothes:
                    original_price = clothing.original_price or clothing.price
                    quantity = clothing.quantity or 1
                    order_original_amount += original_price * quantity

                if order_original_amount == 0:
                    order_original_amount = order.total_amount

                bill.original_amount += order_original_amount
                bill.total_amount += order.total_amount
                bill.discount_amount += order.discount_amount or 0

            bill.actual_amount = bill.total_amount
            print(f"重新计算账单统计: 订单数={bill.order_count}, 原始金额={bill.original_amount}, 实际金额={bill.actual_amount}")

        # 更新账单字段
        if 'payment_status' in data:
            bill.payment_status = data['payment_status']

            if data['payment_status'] == "已付款" and previous_payment_status != "已付款":
                # 批量更新订单支付状态，避免循环中的单独更新
                payment_time = datetime.datetime.now()
                updated_orders_count = Order.query.filter(
                    Order.mall_customer_id == bill.mall_customer_id,
                    Order.is_mall_order == True,
                    Order.created_at >= bill.bill_start_date,
                    Order.created_at <= bill.bill_end_date + datetime.timedelta(days=1),
                    Order.payment_status == "未付款"
                ).update({
                    'payment_status': "已付款",
                    'payment_time': payment_time
                }, synchronize_session=False)

                print(f"已更新 {updated_orders_count} 个关联订单的支付状态为'已付款'")

        if 'payment_date' in data:
            if data['payment_date']:
                bill.payment_date = datetime.datetime.strptime(data['payment_date'], '%Y-%m-%d').date()
            else:
                bill.payment_date = None

        if 'payment_method' in data:
            bill.payment_method = data['payment_method']

        if 'remarks' in data:
            bill.remarks = data['remarks']

        db.session.commit()

        response_data = {
            'success': True,
            'message': '账单更新成功'
        }

        if updated_orders_count > 0:
            response_data['updated_orders_count'] = updated_orders_count
            response_data['message'] = f'账单更新成功，同时更新了 {updated_orders_count} 个关联订单的支付状态'

        if date_range_updated:
            response_data['recalculated'] = True
            response_data['message'] += '，已重新计算账单统计数据'

        return jsonify(response_data)
    except Exception as e:
        db.session.rollback()
        print(f"更新月度账单出错: {str(e)}")
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/bills/<int:bill_id>', methods=['DELETE'])
@login_required
def delete_monthly_bill(bill_id):
    """删除月度账单"""
    if session.get('staff_role') != 'manager':
        return jsonify({'error': '您没有权限执行此操作'}), 403

    try:
        bill = MallMonthlyBill.query.get_or_404(bill_id)
        db.session.delete(bill)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '月度账单已删除'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/mall_customers/<int:customer_id>/discount_history', methods=['GET'])
@login_required
def get_discount_history(customer_id):
    """获取商场客户的折扣变更历史"""
    try:
        customer = MallCustomer.query.get_or_404(customer_id)
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
            return jsonify({'error': '您没有权限查看此商场客户的折扣历史'}), 403

        history = MallDiscountHistory.query.filter_by(mall_customer_id=customer_id).order_by(MallDiscountHistory.change_date.desc()).all()
        result = []

        for h in history:
            result.append({
                'id': h.id,
                'product_name': h.product_name,
                'product_type': h.product_type,
                'old_discount_rate': h.old_discount_rate,
                'new_discount_rate': h.new_discount_rate,
                'change_date': h.change_date.strftime('%Y-%m-%d'),
                'change_reason': h.change_reason,
                'operator': h.operator
            })

        return jsonify({'history': result})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 账单详情管理
@mall_customer_bp.route('/api/bills/<int:bill_id>', methods=['GET'])
@login_required
def get_bill_details(bill_id):
    """获取单个月度账单详情"""
    try:
        bill = MallMonthlyBill.query.get_or_404(bill_id)

        result = {
            'id': bill.id,
            'mall_customer_id': bill.mall_customer_id,
            'bill_year_month': bill.bill_year_month,
            'bill_start_date': bill.bill_start_date.strftime('%Y-%m-%d'),
            'bill_end_date': bill.bill_end_date.strftime('%Y-%m-%d'),
            'order_count': bill.order_count,
            'total_amount': bill.total_amount,
            'discount_amount': bill.discount_amount,
            'actual_amount': bill.actual_amount,
            'payment_status': bill.payment_status,
            'payment_date': bill.payment_date.strftime('%Y-%m-%d') if bill.payment_date else '',
            'payment_method': bill.payment_method or '',
            'remarks': bill.remarks or ''
        }

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/bills/<int:bill_id>/orders', methods=['GET'])
@login_required
def get_bill_orders(bill_id):
    """获取账单关联的订单列表"""
    try:
        bill = MallMonthlyBill.query.get_or_404(bill_id)
        mall_customer_id = bill.mall_customer_id

        # 优化：使用 joinedload 预加载关联数据，避免 N+1 查询
        orders = Order.query.options(
            joinedload(Order.customer),
            selectinload(Order.clothes)
        ).filter(
            Order.mall_customer_id == mall_customer_id,
            Order.is_mall_order == True,
            Order.created_at >= bill.bill_start_date,
            Order.created_at <= bill.bill_end_date + datetime.timedelta(days=1)
        ).order_by(Order.created_at.desc()).all()

        result = []
        for order in orders:
            # 获取订单中的衣物信息
            clothing_items = []
            order_original_amount = 0
            order_actual_amount = 0

            # 直接使用预加载的 clothes 关联数据
            for clothing in order.clothes:
                if clothing.original_price and clothing.original_price > 0:
                    original_price = clothing.original_price
                else:
                    original_price = clothing.price

                quantity = clothing.quantity or 1
                actual_price = clothing.price

                item_original_amount = original_price * quantity
                item_actual_amount = actual_price * quantity

                order_original_amount += item_original_amount
                order_actual_amount += item_actual_amount

                clothing_info = {
                    'name': clothing.name,
                    'color': clothing.color,
                    'quantity': quantity,
                    'original_price': original_price,
                    'price': actual_price,
                    'discount_rate': clothing.discount_rate or 1.0,
                    'item_original_amount': item_original_amount,
                    'item_actual_amount': item_actual_amount
                }
                clothing_items.append(clothing_info)

            if order_original_amount == 0:
                order_original_amount = order.total_amount
                order_actual_amount = order.total_amount

            order_discount_amount = order_original_amount - order_actual_amount

            order_info = {
                'id': order.id,
                'order_number': order.order_number,
                'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'original_amount': order_original_amount,
                'total_amount': order_actual_amount,
                'discount_amount': order_discount_amount,
                'status': order.status,
                'payment_status': order.payment_status,
                'operator': order.operator,
                'clothing_count': sum(clothing.quantity or 1 for clothing in order.clothes),
                'clothing_items': clothing_items
            }
            result.append(order_info)

        return jsonify({
            'orders': result,
            'total_count': len(result)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@mall_customer_bp.route('/api/bills/<int:bill_id>/export', methods=['GET'])
@login_required
def export_bill_orders(bill_id):
    """导出账单订单明细到Excel"""
    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, Border, Side
        from flask import make_response
        import io

        bill = MallMonthlyBill.query.get_or_404(bill_id)
        mall_customer = MallCustomer.query.get_or_404(bill.mall_customer_id)

        # 优化：使用 joinedload 预加载关联数据，避免 N+1 查询
        orders = Order.query.options(
            joinedload(Order.customer),
            selectinload(Order.clothes)
        ).filter(
            Order.mall_customer_id == bill.mall_customer_id,
            Order.is_mall_order == True,
            Order.created_at >= bill.bill_start_date,
            Order.created_at <= bill.bill_end_date + datetime.timedelta(days=1)
        ).order_by(Order.created_at.desc()).all()

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "订单明细"

        header_font = Font(bold=True, size=12)
        header_alignment = Alignment(horizontal='center', vertical='center')
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        ws['A1'] = f"商场客户：{mall_customer.mall_name}"
        ws['A2'] = f"账单期间：{bill.bill_start_date} 至 {bill.bill_end_date}"
        ws['A3'] = f"导出时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ws['A4'] = ""

        headers = [
            '订单号', '创建时间', '客户姓名', '客户电话', '衣物名称', '颜色',
            '数量', '原始单价', '折扣后单价', '折扣率', '小计原始金额',
            '小计实际金额', '订单原始总额', '订单实际总额', '订单折扣金额',
            '支付状态', '支付方式', '备注'
        ]

        row = 5
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.alignment = header_alignment
            cell.border = border

        row = 6
        for order in orders:
            # 使用预加载的客户数据，避免重复查询
            customer = order.customer
            # 使用预加载的衣物数据，避免重复查询
            clothes = order.clothes

            order_original_amount = 0
            order_actual_amount = 0
            for item in clothes:
                if item.original_price and item.original_price > 0:
                    original_price = item.original_price
                else:
                    original_price = item.price

                quantity = item.quantity or 1
                actual_price = item.price

                order_original_amount += original_price * quantity
                order_actual_amount += actual_price * quantity

            if order_original_amount == 0:
                order_original_amount = order.total_amount
                order_actual_amount = order.total_amount

            order_discount_amount = order_original_amount - order_actual_amount

            for clothing in clothes:
                if clothing.original_price and clothing.original_price > 0:
                    original_price = clothing.original_price
                else:
                    original_price = clothing.price

                quantity = clothing.quantity or 1
                actual_price = clothing.price
                discount_rate = clothing.discount_rate or 1.0
                item_original_amount = original_price * quantity
                item_actual_amount = actual_price * quantity

                data = [
                    order.order_number,
                    order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    customer.name if customer else '',
                    customer.phone if customer else '',
                    clothing.name,
                    clothing.color,
                    quantity,
                    original_price,
                    actual_price,
                    discount_rate,
                    item_original_amount,
                    item_actual_amount,
                    order_original_amount,
                    order_actual_amount,
                    order_discount_amount,
                    order.payment_status,
                    order.payment_method or '',
                    order.remarks or ''
                ]

                for col, value in enumerate(data, 1):
                    cell = ws.cell(row=row, column=col, value=value)
                    cell.border = border
                row += 1

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename=bill_{bill_id}_orders_{datetime.datetime.now().strftime("%Y%m%d")}.xlsx'

        return response

    except Exception as e:
        return jsonify({'error': str(e)}), 500 