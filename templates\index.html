<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soulweave改衣坊</title>
    <link rel="stylesheet" href="/static/css/print-styles.css">
    
    <!-- Lodop打印控件脚本 -->
    <script language="javascript" src="https://www.lodop.net/download/CLodop_Setup_For_Win32NT.exe" id="lodop32"></script>
    <script language="javascript" src="https://www.lodop.net/download/CLodop_Setup_For_Win32NT.exe" id="lodop64"></script>
    <script language="javascript" src="/static/js/LodopFuncs.js"></script>
    <script src="/static/js/lodop-print.js"></script>
    
    <!-- 原有脚本 -->
    <script src="/static/js/print-functions.js"></script>
    <script src="/static/js/image-compressor.js"></script>
    <script src="/static/js/discount-functions.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, select, textarea { width: 100%; padding: 8px; box-sizing: border-box; }
        button { padding: 10px 15px; background: #007BFF; color: white; border: none; cursor: pointer; margin-right: 5px; }
        button:hover { background: #0056b3; }
        .section {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: #333;
            flex-direction: row;
            flex-wrap: nowrap;
        }
        .section-number {
            background: #007BFF;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            flex-shrink: 0;
        }
        .section-title h2 {
            display: flex;
            align-items: center;
            margin: 0;
            flex-wrap: nowrap;
            white-space: nowrap;
        }
        .customer-info { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .clothing-item {
            background: #e9f7fe;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            position: relative;
            transition: all 0.3s ease;
        }
        .clothing-item.collapsed {
            padding-top: 10px;
            padding-bottom: 10px;
            background: #d9edf8;
        }
        .clothing-item.collapsed .clothing-content {
            display: none;
        }
        .clothing-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            margin-right: 40px; /* 为删除按钮留出空间 */
        }
        .clothing-item-title {
            font-weight: bold;
            margin: 0;
        }
        .remove-item {
            position: absolute;
            right: 10px;
            top: 10px;
            background: #ff4d4d;
            z-index: 10;
            width: 30px;
            height: 30px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        .toggle-collapse {
            background: #007BFF;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        .service-types { display: flex; gap: 10px; margin-top: 10px; }
        .service-types label { display: inline-flex; align-items: center; }
        .service-types input[type="checkbox"] { width: auto; margin-right: 5px; }
        .service-requirements {
            display: none;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        #clothingItems { margin-top: 20px; }
        .hidden { display: none; }
        .header { display: flex; justify-content: space-between; align-items: center; }
        .header a { text-decoration: none; color: #007BFF; }
        .pagination-controls {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .pagination-controls button {
            margin: 0 5px;
        }
        .total-price {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
            margin-top: 15px;
        }
        .price-input {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .price-input input {
            flex: 1;
        }
        .price-input button {
            width: auto;
            flex-shrink: 0;
            padding: 8px 12px;
            background: #28a745;
        }
        .tab-navigation {
            display: flex;
            margin-bottom: 20px;
            flex-wrap: nowrap;
            width: 100%;
            overflow-x: auto;
        }
        .tab-button {
            padding: 10px 15px;
            background: #f5f5f5;
            border: none;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
            cursor: pointer;
            white-space: nowrap;
            flex: 1;
            text-align: center;
        }
        .tab-button.active {
            background: #007BFF;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .next-step {
            display: block;
            margin: 20px auto 0;
            padding: 12px 20px;
            background: #28a745;
            width: 200px;
        }
        .camera-container {
            margin: 15px 0;
        }
        #cameraFeed {
            display: block;
            width: 100%;
            max-width: 320px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        .camera-buttons {
            display: flex;
            gap: 10px;
        }
        .cloth-image-preview {
            display: block;
            max-width: 100%;
            max-height: 200px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        /* 添加服务价格相关样式 */
        .service-price-container {
            display: flex;
            gap: 15px;
            margin-top: 10px;
            flex-wrap: wrap;
        }
        .service-price-item {
            flex: 1;
            min-width: 200px;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        .service-price-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 8px;
        }
        .service-price-row label {
            margin-bottom: 0;
            width: 70px;
        }
        .service-price-row input {
            flex: 1;
        }
        .service-total-section {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
        }
        #newCustomerForm, #customerDetails {
            margin-top: 15px;
        }
        .form-input-row {
            display: flex;
            flex-direction: row;
            gap: 15px;
            align-items: flex-start;
            width: 100%;
            margin-bottom: 15px;
        }
        .form-input-row .form-group {
            margin-bottom: 0;
        }
        .form-input-row input {
            flex: 1;
        }
        .form-input-row button {
            flex-shrink: 0;
        }
        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
        }
        /* 照片库样式 */
        .photo-gallery {
            margin: 10px 0;
        }
        .photo-thumbnails {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
        }
        .photo-thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 4px;
            object-fit: cover;
            border: 1px solid #ddd;
            position: relative;
            cursor: pointer;
        }
        .photo-thumbnail-container {
            position: relative;
        }
        .remove-photo {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background-color: #ff4d4d;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            z-index: 10;
        }
        /* 数量控件样式 */
        .quantity-control {
            display: flex;
            align-items: center;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        .quantity-btn {
            width: 30px;
            height: 32px;
            border: none;
            background: #f8f9fa;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #495057;
            transition: background-color 0.2s;
        }
        .quantity-btn:hover {
            background: #e9ecef;
        }
        .quantity-btn:active {
            background: #dee2e6;
        }
        .clothing-quantity {
            width: 60px;
            height: 32px;
            border: none;
            text-align: center;
            font-weight: bold;
            background: white;
            outline: none;
        }
        .clothing-quantity:focus {
            background: #f8f9fa;
        }
        /* 响应式布局调整 */
        @media (max-width: 768px) {
            .form-input-row {
                flex-direction: column;
                gap: 10px;
            }
            .form-input-row .form-group {
                width: 100%;
            }

            /* 确保织补和改衣价格区域在小屏幕上正确显示 */
            .service-requirements .form-input-row {
                flex-direction: column;
            }
            .service-requirements .form-input-row .form-group {
                width: 100%;
            }
            .service-requirements .form-group div {
                width: 100%;
                justify-content: space-between;
            }
        }
        /* 摄像头弹窗样式 */
        .camera-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
            z-index: 1000;
        }

        .camera-modal-content {
            position: relative;
            margin: 10% auto;
            padding: 20px;
            width: 80%;
            max-width: 500px;
            background-color: white;
            border-radius: 8px;
        }

        .close-modal {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            font-weight: bold;
            color: #333;
            cursor: pointer;
        }

        .camera-feed {
            width: 100%;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .camera-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }

        .preview-image {
            max-width: 100%;
            max-height: 300px;
            margin: 10px auto;
            display: block;
            border-radius: 5px;
        }

        /* 加载指示器样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            color: white;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        /* 优化订单摘要样式 */
        #clothingSummary {
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 15px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        .summary-item {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            background: #f9f9f9;
        }
        .summary-item:nth-child(even) {
            background: #f5f5f5;
        }
        .summary-item:last-child {
            border-bottom: none;
        }
        .summary-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
            line-height: 1.4;
        }
        .summary-item-details {
            font-size: 0.85em;
            color: #555;
            margin-top: 3px;
            line-height: 1.3;
        }
        .summary-item-details p {
            margin: 2px 0;
        }
        .order-summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .order-summary-header h3 {
            margin: 0;
        }
        .total-items {
            background: #e9f7fe;
            padding: 4px 10px;
            border-radius: 15px;
            font-weight: 500;
            color: #007BFF;
            font-size: 0.9em;
        }
        /* 用户余额相关样式 */
        .balance-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #f5fff5;
            border: 1px solid #ddd;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .balance-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .balance-value {
            font-weight: bold;
            color: #28a745;
            font-size: 1.2em;
        }
        .recharge-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .recharge-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
            z-index: 1000;
            overflow-y: auto;
        }
        .recharge-modal-content {
            position: relative;
            margin: 10% auto;
            padding: 20px;
            width: 80%;
            max-width: 400px;
            background-color: white;
            border-radius: 8px;
        }
        .recharge-amount-options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .recharge-amount-option {
            flex: 1;
            min-width: 80px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .recharge-amount-option:hover, .recharge-amount-option.selected {
            background: #e9f7fe;
            border-color: #007BFF;
            color: #007BFF;
        }
        .recharge-amount-option.selected {
            background-color: #007BFF;
            color: white;
            font-weight: bold;
        }
        .recharge-payment-methods {
            display: flex;
            gap: 15px;
            margin: 15px 0;
        }
        .recharge-payment-method {
            flex: 1;
            padding: 10px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
        }
        .recharge-payment-method:hover, .recharge-payment-method.selected {
            background: #e9f7fe;
            border-color: #007BFF;
        }
        .recharge-payment-method.selected {
            background-color: #007BFF;
            color: white;
            font-weight: bold;
        }
        .form-input-row { display: flex; gap: 5px; }
        .form-input-row input { flex: 1; }
        .form-input-row button { white-space: nowrap; }

        /* 头部控件样式 */
        .header-controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .staff-info {
            display: flex;
            align-items: center;
            gap: 15px;
            background: #f5f5f5;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .logout-btn {
            color: #dc3545;
            text-decoration: none;
            font-weight: 500;
        }
        .logout-btn:hover {
            text-decoration: underline;
        }

        /* 打印弹窗样式 */
        .print-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .print-modal-content {
            position: relative;
            background-color: #fff;
            margin: 5% auto;
            padding: 20px;
            width: 80%;
            max-width: 600px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .print-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .print-modal-header h3 {
            margin: 0;
        }

        .close-print-modal {
            font-size: 24px;
            font-weight: bold;
            background: none;
            border: none;
            cursor: pointer;
        }

        .print-modal-body {
            max-height: 70vh;
            overflow-y: auto;
            margin-bottom: 15px;
        }

        .print-modal-footer {
            display: flex;
            justify-content: flex-end;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }

        .print-action-btn {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .print-action-btn:hover {
            background-color: #0056b3;
        }







        .customer-type-selector {
            display: flex;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }

        .customer-type-option {
            flex: 1;
            text-align: center;
            padding: 10px;
            background: #f5f5f5;
            cursor: pointer;
            border-right: 1px solid #ddd;
            transition: all 0.3s ease;
        }

        .customer-type-option:last-child {
            border-right: none;
        }

        .customer-type-option.active {
            background: #007BFF;
            color: white;
        }

        .mall-customer-selector {
            margin-bottom: 15px;
            display: none;
        }

        .mall-customer-selector.active {
            display: block;
        }

        .discount-badge {
            display: inline-block;
            padding: 3px 8px;
            background-color: #e6f7ff;
            color: #1890ff;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-left: 10px;
        }


    </style>
</head>
<body>
    <div class="header">
        <h1>Soulweave改衣坊</h1>
        <div class="header-controls">
            {% if session.staff_name %}
            <div class="staff-info">
                <span>营业员: {{ session.staff_name }}</span>
                <a href="/logout" class="logout-btn">登出</a>
            </div>
            {% endif %}
            {% if session.staff_role in ['admin', 'manager'] %}
            <a href="/user_management" style="margin-right: 15px;">用户管理</a>
            {% endif %}
            {% if session.staff_role == 'manager' or session.staff_role == 'admin' %}
            <a href="/mall_customer_management" style="margin-right: 15px;">商场客户管理</a>
            <a href="/product_management" style="margin-right: 15px; color: #28a745;">商品管理</a>
            {% endif %}
            {% if session.staff_role in ['manager', 'admin', 'staff'] %}
            <a href="/member_management" style="margin-right: 15px; color: #1890ff;">会员管理</a>
            {% endif %}
            <!-- 添加状态处理页面链接 -->
            <div class="dropdown" style="display: inline-block; position: relative; margin-right: 15px;">
                <a href="#" style="color: #007bff; text-decoration: none;"
                   onclick="document.getElementById('statusDropdown').style.display = document.getElementById('statusDropdown').style.display === 'none' ? 'block' : 'none'; return false;">
                   状态处理 <i class="fa fa-angle-down"></i>
                </a>
                <div id="statusDropdown" style="display: none; position: absolute; background-color: white; min-width: 160px; box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2); z-index: 1; border-radius: 4px; padding: 8px 0;">
                    <a href="/status/to_factory" style="color: black; padding: 8px 12px; text-decoration: none; display: block;">送洗处理</a>
                    <a href="/status/factory_in" style="color: black; padding: 8px 12px; text-decoration: none; display: block;">入厂处理</a>
                    <a href="/status/factory_out" style="color: black; padding: 8px 12px; text-decoration: none; display: block;">出厂处理</a>
                    <a href="/status/on_shelf" style="color: black; padding: 8px 12px; text-decoration: none; display: block;">上架处理</a>
                    <a href="/status/self_pickup" style="color: black; padding: 8px 12px; text-decoration: none; display: block;">自取处理</a>
                    <a href="/sticky_label_print" style="color: black; padding: 8px 12px; text-decoration: none; display: block;">不干胶打印</a>
                </div>
            </div>
            <a href="/data_summary" style="margin-right: 15px;">数据汇总</a>
            <a href="/rack" style="margin-right: 15px; color: #ff6b35;">
                📦 格架管理
            </a>
            <a href="/history">查询历史订单</a>
        </div>
    </div>

    <!-- 分页导航 -->
    <div class="tab-navigation">
        <button class="tab-button active" data-tab="customer-tab">1. 客户信息</button>
        <button class="tab-button" data-tab="clothing-tab">2. 衣物信息</button>
        <button class="tab-button" data-tab="payment-tab">3. 订单确认</button>
    </div>

    <!-- 客户信息页面 -->
    <div id="customer-tab" class="tab-content active">
        <div class="section">
            <div class="section-title">
                <h2><span class="section-number">1</span> 客户信息</h2>
            </div>

            <div class="form-group">
                <label for="customerPhone">客户手机号</label>
                <div class="form-input-row">
                    <input type="tel" id="customerPhone" name="customerPhone" required>
                    <button type="button" id="searchCustomer">查询</button>
                    <button type="button" id="directRecharge" style="background: #28a745;">充值</button>
                </div>
            </div>

            <div id="customerDetails" class="hidden">
                <div class="form-group">
                    <label>客户姓名</label>
                    <div id="customerNameDisplay" style="padding: 8px; background: #eee; border-radius: 4px;"></div>
                </div>
                <div class="form-group">
                    <label>账户余额</label>
                    <div class="balance-container">
                        <div class="balance-info">
                            <span>总余额:</span>
                            <span class="balance-value">¥<span id="customerBalance">0.00</span></span>
                            <div id="balanceDetails" style="font-size: 0.8em; color: #666; margin-top: 2px;"></div>
                        </div>
                        <button type="button" class="recharge-button" id="openRecharge">充值</button>
                    </div>
                </div>
            </div>

            <div id="newCustomerForm">
                <div class="form-group">
                    <label for="customerName">客户姓名</label>
                    <input type="text" id="customerName" name="customerName" required>
                </div>
                <div class="form-group">
                    <label for="address">客户地址</label>
                    <input type="text" id="address" name="address" placeholder="可选">
                </div>
            </div>

            <div class="customer-type-selector">
                <div class="customer-type-option active" data-type="normal">普通客户</div>
                <div class="customer-type-option" data-type="mall">商场客户</div>
            </div>

            <div class="mall-customer-selector">
                <div class="form-group">
                    <label for="mallCustomerName">商场品牌名称</label>
                    <input type="text" id="mallCustomerName" name="mallCustomerName" required>
                </div>
                <div class="form-group">
                    <label for="mallCustomerPhone">商场客户手机号</label>
                    <input type="tel" id="mallCustomerPhone" name="mallCustomerPhone" required>
                </div>
            </div>

            <button type="button" class="next-step" id="nextToClothing">下一步: 衣物信息</button>
        </div>
    </div>

    <!-- 衣物信息页面 -->
    <div id="clothing-tab" class="tab-content">
        <div class="section">
            <div class="section-title">
                <h2><span class="section-number">2</span> 衣物信息</h2>
            </div>

            <div id="clothingItems">
                <!-- 衣物项模板将通过JavaScript动态生成 -->
            </div>

            <button type="button" id="addClothing" style="margin-top: 10px;">添加更多衣物</button>
            <button type="button" class="next-step" id="nextToPayment">下一步: 订单确认</button>
        </div>
    </div>

    <!-- 订单确认页面 -->
    <div id="payment-tab" class="tab-content">
        <div class="section">
            <div class="section-title">
                <h2><span class="section-number">3</span> 订单确认</h2>
            </div>

            <div id="orderSummary">
                <h3>客户信息</h3>
                <p><strong>姓名：</strong><span id="summaryName"></span></p>
                <p><strong>电话：</strong><span id="summaryPhone"></span></p>

                <div class="order-summary-header">
                    <h3>衣物清单</h3>
                    <span class="total-items">共 <span id="totalItems">0</span> 件衣物</span>
                </div>
                <div id="clothingSummary"></div>

                <div class="total-price">总计: ¥<span id="totalAmount">0.00</span></div>

                <div class="form-group" style="margin-top: 20px;">
                    <label for="paymentMethod">支付方式</label>
                    <select id="paymentMethod" name="paymentMethod">
                        <option value="未付款">未付款</option>
                        <option value="扫银联码">扫银联码</option>
                        <option value="商场POS">商场POS</option>
                        <option value="余额">余额支付</option>
                    </select>
                    <div id="balancePaymentInfo" class="balance-container" style="margin-top: 10px; display: none;">
                        <div class="balance-info">
                            <span>账户余额:</span>
                            <span class="balance-value">¥<span id="summaryBalance">0.00</span></span>
                        </div>
                        <div id="balanceStatus" style="font-size: 0.9em;"></div>
                    </div>
                </div>
            </div>

            <button type="button" id="submitOrder" style="margin-top: 20px; background: #28a745; width: 100%;">确认提交订单</button>

            <!-- 订单提交结果将显示在这里 -->
            <div id="orderResults" style="margin-top: 20px;"></div>
        </div>
    </div>

    <!-- 衣物项模板 (隐藏) -->
    <template id="clothing-item-template">
        <div class="clothing-item">
            <button type="button" class="remove-item">×</button>

            <div class="clothing-item-header">
                <h3 class="clothing-item-title">新衣物</h3>
                <button type="button" class="toggle-collapse">展开/折叠</button>
            </div>

            <div class="clothing-content">
                <!-- 修改：将衣物名称、颜色和数量放在同一行 -->
                <div class="form-input-row">
                    <div class="form-group" style="flex: 2;">
                        <label>衣物名称</label>
                        <select class="clothing-name" name="clothingName[]" required>
                            <option value="">请选择衣物类型</option>
                            <!-- 选项将通过JavaScript动态填充 -->
                        </select>
                    </div>

                    <div class="form-group" style="flex: 1;">
                        <label>衣物颜色</label>
                        <select class="clothing-color" name="clothingColor[]">
                            <option value="白色">白色</option>
                            <option value="黑色">黑色</option>
                            <option value="灰色">灰色</option>
                            <option value="红色">红色</option>
                            <option value="蓝色">蓝色</option>
                            <option value="绿色">绿色</option>
                            <option value="黄色">黄色</option>
                            <option value="紫色">紫色</option>
                            <option value="粉色">粉色</option>
                            <option value="棕色">棕色</option>
                            <option value="米色">米色</option>
                            <option value="花色">花色</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>

                    <div class="form-group" style="flex: 0 0 120px;">
                        <label>数量</label>
                        <div class="quantity-control">
                            <button type="button" class="quantity-btn quantity-minus">-</button>
                            <input type="number" class="clothing-quantity" name="clothingQuantity[]" value="1" min="1" max="99" readonly>
                            <button type="button" class="quantity-btn quantity-plus">+</button>
                        </div>
                    </div>
                </div>

                <!-- 修改：将瑕疵描述和衣物照片放在同一行 -->
                <div class="form-input-row" style="align-items: flex-start;">
                    <div class="form-group" style="flex: 1;">
                        <label>瑕疵描述（可选）</label>
                        <textarea class="clothing-flaw" name="clothingFlaw[]" rows="2"></textarea>
                    </div>

                    <div class="form-group" style="flex: 1;">
                        <label>衣物照片</label>
                        <div class="photo-gallery">
                            <div class="photo-thumbnails"></div>
                            <button type="button" class="add-photo-btn">添加照片</button>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>服务类型</label>
                    <div class="service-types">
                        <label><input type="checkbox" class="service-type" name="serviceType[]" value="精洗" checked> 精洗</label>
                        <label><input type="checkbox" class="service-type" name="serviceType[]" value="奢洗"> 奢洗</label>
                        <label><input type="checkbox" class="service-type" name="serviceType[]" value="织补"> 织补</label>
                        <label><input type="checkbox" class="service-type" name="serviceType[]" value="改衣"> 改衣</label>
                        <label><input type="checkbox" class="service-type" name="serviceType[]" value="其他"> 其他</label>
                    </div>

                    <div class="service-requirements wash-requirements" style="display:block;">
                        <div class="form-input-row" style="margin-top: 8px;">
                            <div class="form-group" style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <label style="white-space: nowrap; margin-bottom: 8px;">精洗价格：</label>
                                    <input type="number" class="wash-price" name="fineWashPrice[]" min="0" step="1" value="25" style="width: 80px;">
                                    <span>元</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 奢洗价格输入块 -->
                    <div class="service-requirements luxury-wash-requirements" style="display:none;">
                        <div class="form-input-row" style="margin-top: 8px;">
                            <div class="form-group" style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <label style="white-space: nowrap; margin-bottom: 8px;">奢洗价格：</label>
                                    <input type="number" class="luxury-wash-price" name="luxuryWashPrice[]" min="0" step="1" value="40" style="width: 80px;">
                                    <span>元</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="service-requirements darn-requirements" style="display:none;">
                        <label>织补要求</label>
                        <div class="form-input-row" style="margin-top: 8px;">
                            <div class="form-group" style="flex: 2;">
                                <textarea class="darn-requirement" name="darnRequirement[]" rows="2"></textarea>
                            </div>
                            <div class="form-group" style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <label style="white-space: nowrap; margin-bottom: 8px;">织补价格：</label>
                                    <input type="number" class="darn-price" name="darnPrice[]" min="0" step="1" value="20" style="width: 80px;">
                                    <span>元</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="service-requirements alter-requirements" style="display:none;">
                        <label>改衣要求</label>
                        <div class="form-input-row" style="margin-top: 8px;">
                            <div class="form-group" style="flex: 2;">
                                <textarea class="alter-requirement" name="alterRequirement[]" rows="2"></textarea>
                            </div>
                            <div class="form-group" style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <label style="white-space: nowrap; margin-bottom: 8px;">改衣价格：</label>
                                    <input type="number" class="alter-price" name="alterPrice[]" min="0" step="1" value="30" style="width: 80px;">
                                    <span>元</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="service-requirements other-requirements" style="display:none;">
                        <label>其他要求</label>
                        <div class="form-input-row" style="margin-top: 8px;">
                            <div class="form-group" style="flex: 2;">
                                <textarea class="other-requirement" name="otherRequirement[]" rows="2"></textarea>
                            </div>
                            <div class="form-group" style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <label style="white-space: nowrap; margin-bottom: 8px;">其他价格：</label>
                                    <input type="number" class="other-price" name="otherPrice[]" min="0" step="1" value="20" style="width: 80px;">
                                    <span>元</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group price-input">
                    <label>总价（元）</label>
                    <input type="number" class="clothing-price" name="clothingPrice[]" value="0" min="0" step="0.01">
                </div>

                <div class="form-group">
                    <label>备注</label>
                    <textarea class="clothing-note" name="clothingNote[]" rows="2" placeholder="其他需要记录的信息"></textarea>
                </div>
            </div>
        </div>
    </template>

    <!-- 小票打印弹窗 -->
    <div id="receiptModal" class="print-modal" style="display: none;">
        <div class="print-modal-content">
            <div class="print-modal-header">
                <h3>小票打印预览</h3>
                <button class="close-print-modal">&times;</button>
            </div>
            <div class="print-modal-body" id="receiptContent">
                <!-- 小票内容将在这里动态生成 -->
            </div>
            <div class="print-modal-footer">
                <button id="printReceiptBtn" class="print-action-btn">打印小票</button>
            </div>
        </div>
    </div>

    <!-- 水洗唛打印弹窗 -->
    <div id="labelModal" class="print-modal" style="display: none;">
        <div class="print-modal-content">
            <div class="print-modal-header">
                <h3>水洗唛打印预览</h3>
                <button class="close-print-modal">&times;</button>
            </div>
            <div class="label-dropdown-container">
                <div class="label-select-container">
                    <span class="label-select-title">选择要打印的水洗唛：</span>
                    <select id="labelSelect" class="label-select">
                        <option value="all">打印所有水洗唛</option>
                        <!-- 动态添加衣物选项 -->
                    </select>
                </div>
                <div>
                    <button id="printAllLabelsBtn" class="print-all-labels">打印全部</button>
                    <button id="printSelectedLabelBtn" class="print-selected-label">打印选中项</button>
                </div>
            </div>
            <div class="print-modal-body" id="labelContent">
                <!-- 水洗唛内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script src="/static/js/clothing-options.js"></script>
    <script>
        // 页面切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有active类
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

                // 为当前按钮和内容添加active类
                this.classList.add('active');
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 点击页面任意位置关闭下拉菜单
        document.addEventListener('click', function(event) {
            var dropdown = document.getElementById('statusDropdown');
            var dropdownToggle = document.querySelector('.dropdown a');

            // 如果点击的不是下拉菜单或其触发按钮，则关闭下拉菜单
            if (dropdown && dropdownToggle &&
                !dropdown.contains(event.target) &&
                event.target !== dropdownToggle) {
                dropdown.style.display = 'none';
            }
        });

        // 全局变量
        let currentPhotoItem = null;
        let cameraStream = null;
        // 存储衣物数据的数组
        let clothingItemsData = [];
        // 商场客户标志
        let isMallCustomer = false;
        // 客户ID和客户详细信息
        let customerId = null;
        let customerDetails = {
            name: '',
            phone: '',
            balance: 0
        };

        // 错误提示函数
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger';
            errorDiv.textContent = message;
            errorDiv.style.padding = '10px 15px';
            errorDiv.style.marginTop = '15px';
            errorDiv.style.backgroundColor = '#f8d7da';
            errorDiv.style.color = '#721c24';
            errorDiv.style.borderRadius = '4px';
            errorDiv.style.border = '1px solid #f5c6cb';

            const results = document.getElementById('orderResults');
            results.innerHTML = '';
            results.appendChild(errorDiv);

            // 5秒后自动消失
            setTimeout(() => {
                errorDiv.style.opacity = '0';
                errorDiv.style.transition = 'opacity 0.5s';
                setTimeout(() => {
                    if (results.contains(errorDiv)) {
                        results.removeChild(errorDiv);
                    }
                }, 500);
            }, 5000);
        }

        // 智能价格分配函数
        function distributePriceToServices(totalPrice, itemIndex, serviceCheckboxes, washPriceInput, darnPriceInput, alterPriceInput, otherPriceInput) {
            // 获取当前选中的服务
            const selectedServices = [];
            serviceCheckboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedServices.push(checkbox.value);
                }
            });

            if (selectedServices.length === 0) {
                // 如果没有选中任何服务，不进行分配
                return;
            }

            if (selectedServices.length === 1) {
                // 如果只选择了一种服务，将总价全部分配给该服务
                const serviceType = selectedServices[0];
                if (serviceType === '洗衣' && washPriceInput) {
                    washPriceInput.value = totalPrice.toFixed(2);
                    clothingItemsData[itemIndex].specialRequirements.washPrice = totalPrice;
                } else if (serviceType === '织补' && darnPriceInput) {
                    darnPriceInput.value = totalPrice.toFixed(2);
                    clothingItemsData[itemIndex].specialRequirements.darnPrice = totalPrice;
                } else if (serviceType === '改衣' && alterPriceInput) {
                    alterPriceInput.value = totalPrice.toFixed(2);
                    clothingItemsData[itemIndex].specialRequirements.alterPrice = totalPrice;
                } else if (serviceType === '其他' && otherPriceInput) {
                    otherPriceInput.value = totalPrice.toFixed(2);
                    clothingItemsData[itemIndex].specialRequirements.otherPrice = totalPrice;
                }
            } else {
                // 如果选择了多种服务，使用智能分配策略
                let remainingPrice = totalPrice;

                // 优先保持织补、改衣和其他的价格不变，将差额分配给洗衣
                if (selectedServices.includes('织补') && darnPriceInput) {
                    const darnPrice = parseFloat(darnPriceInput.value) || defaultServicePrices['织补'];
                    remainingPrice -= darnPrice;
                }

                if (selectedServices.includes('改衣') && alterPriceInput) {
                    const alterPrice = parseFloat(alterPriceInput.value) || defaultServicePrices['改衣'];
                    remainingPrice -= alterPrice;
                }

                if (selectedServices.includes('其他') && otherPriceInput) {
                    const otherPrice = parseFloat(otherPriceInput.value) || defaultServicePrices['其他'];
                    remainingPrice -= otherPrice;
                }

                // 将剩余价格分配给洗衣服务
                if (selectedServices.includes('洗衣') && washPriceInput) {
                    const washPrice = Math.max(0, remainingPrice); // 确保不为负数
                    washPriceInput.value = washPrice.toFixed(2);
                    clothingItemsData[itemIndex].specialRequirements.washPrice = washPrice;
                }

                // 如果没有洗衣服务，则平均分配给其他服务
                if (!selectedServices.includes('洗衣')) {
                    const pricePerService = totalPrice / selectedServices.length;

                    if (selectedServices.includes('织补') && darnPriceInput) {
                        darnPriceInput.value = pricePerService.toFixed(2);
                        clothingItemsData[itemIndex].specialRequirements.darnPrice = pricePerService;
                    }

                    if (selectedServices.includes('改衣') && alterPriceInput) {
                        alterPriceInput.value = pricePerService.toFixed(2);
                        clothingItemsData[itemIndex].specialRequirements.alterPrice = pricePerService;
                    }

                    if (selectedServices.includes('其他') && otherPriceInput) {
                        otherPriceInput.value = pricePerService.toFixed(2);
                        clothingItemsData[itemIndex].specialRequirements.otherPrice = pricePerService;
                    }
                }
            }

            console.log(`价格分配完成 - 总价: ¥${totalPrice}, 选中服务: ${selectedServices.join(', ')}`);
        }

        // 设置衣物项事件
        function setupClothingItemEvents(itemElement) {
            // 获取衣物项索引
            const itemIndex = parseInt(itemElement.querySelector('.clothing-item').dataset.itemIndex);

            // 确保clothingItemsData数组有这个索引的数据
            if (!clothingItemsData[itemIndex]) {
                clothingItemsData[itemIndex] = {
                    name: '',
                    color: '白色',
                    serviceTypes: ['洗衣'],
                    specialRequirements: {
                        wash: '',
                        fineWashPrice: getServicePrice('精洗'),
                        luxuryWashPrice: getServicePrice('奢洗'),
                        darn: '',
                        darnPrice: getServicePrice('织补'),
                        alter: '',
                        alterPrice: getServicePrice('改衣'),
                        other: '',
                        otherPrice: getServicePrice('其他')
                    },
                    price: getServicePrice('洗衣'), // 默认只有洗衣服务的价格
                    remarks: '',
                    flaw: '',  // 添加瑕疵字段
                    photos: []
                };
            }

            // 衣物名称变更时更新价格
            const nameSelect = itemElement.querySelector('.clothing-name');
            const priceInput = itemElement.querySelector('.clothing-price');

            nameSelect.addEventListener('change', async function() {
                // 更新标题
                if (this.value) {
                    const headerTitle = this.closest('.clothing-item').querySelector('.clothing-item-title');
                    headerTitle.textContent = this.value;
                }

                // 更新数据
                if (clothingItemsData[itemIndex]) {
                    clothingItemsData[itemIndex].name = this.value;
                }

                // 根据选择的商品获取具体的服务价格
                if (this.value) {
                    console.log(`选择商品: ${this.value}，获取该商品的服务价格`);

                    try {
                        // 获取该商品的具体服务价格
                        const productPrices = await getProductServicePrices(this.value);

                        // 更新各服务价格输入框
                        const clothingItem = this.closest('.clothing-item');
                        const washPriceInput = clothingItem.querySelector('.wash-price'); // 精洗输入
                        const luxuryPriceInput = clothingItem.querySelector('.luxury-wash-price');
                        const darnPriceInput = clothingItem.querySelector('.darn-price');
                        const alterPriceInput = clothingItem.querySelector('.alter-price');
                        const otherPriceInput = clothingItem.querySelector('.other-price');

                        if (washPriceInput) {
                            washPriceInput.value = productPrices['精洗'].toFixed(2);
                            if (clothingItemsData[itemIndex]) {
                                clothingItemsData[itemIndex].specialRequirements.fineWashPrice = productPrices['精洗'];
                            }
                        }
                        if (luxuryPriceInput) {
                            luxuryPriceInput.value = productPrices['奢洗'].toFixed(2);
                            if (clothingItemsData[itemIndex]) {
                                clothingItemsData[itemIndex].specialRequirements.luxuryWashPrice = productPrices['奢洗'];
                            }
                        }
                        if (darnPriceInput) {
                            darnPriceInput.value = productPrices['织补'].toFixed(2);
                            if (clothingItemsData[itemIndex]) {
                                clothingItemsData[itemIndex].specialRequirements.darnPrice = productPrices['织补'];
                            }
                        }
                        if (alterPriceInput) {
                            alterPriceInput.value = productPrices['改衣'].toFixed(2);
                            if (clothingItemsData[itemIndex]) {
                                clothingItemsData[itemIndex].specialRequirements.alterPrice = productPrices['改衣'];
                            }
                        }
                        if (otherPriceInput) {
                            otherPriceInput.value = productPrices['其他'].toFixed(2);
                            if (clothingItemsData[itemIndex]) {
                                clothingItemsData[itemIndex].specialRequirements.otherPrice = productPrices['其他'];
                            }
                        }

                        console.log(`商品 "${this.value}" 的服务价格已更新:`, productPrices);
                    } catch (error) {
                        console.error('获取商品服务价格失败:', error);
                        // 如果获取失败，使用默认价格
                        const washPriceInput = this.closest('.clothing-item').querySelector('.wash-price');
                        if (washPriceInput && typeof getServicePrice === 'function') {
                            washPriceInput.value = getServicePrice('精洗').toFixed(2);
                            if (clothingItemsData[itemIndex]) {
                                clothingItemsData[itemIndex].specialRequirements.fineWashPrice = getServicePrice('精洗');
                            }
                        }
                        if (luxuryPriceInput && typeof getServicePrice === 'function') {
                            luxuryPriceInput.value = getServicePrice('奢洗').toFixed(2);
                            if (clothingItemsData[itemIndex]) {
                                clothingItemsData[itemIndex].specialRequirements.luxuryWashPrice = getServicePrice('奢洗');
                            }
                        }
                    }
                }

                // 应用商场客户折扣（如果适用）
                applyDiscountToItem(itemIndex);

                // 重新计算价格（基于当前选中的服务）
                updateTotalPrice();
            });

            // 颜色选择变更
            const colorSelect = itemElement.querySelector('.clothing-color');
            colorSelect.addEventListener('change', function() {
                if (clothingItemsData[itemIndex]) {
                    clothingItemsData[itemIndex].color = this.value;
                }
            });

            // 总价变更 - 智能分配价格到各服务项
            priceInput.addEventListener('change', function() {
                if (clothingItemsData[itemIndex]) {
                    const newTotalPrice = parseFloat(this.value) || 0;

                    // 如果是商场客户且有折扣，需要反向计算原价
                    if (window.currentMallCustomer && clothingItemsData[itemIndex].name && clothingItemsData[itemIndex].discountRate && clothingItemsData[itemIndex].discountRate < 1) {
                        // 用户输入的是折扣后价格，需要计算原价
                        const originalPrice = newTotalPrice / clothingItemsData[itemIndex].discountRate;
                        clothingItemsData[itemIndex].originalPrice = originalPrice;
                        console.log(`用户修改折扣后价格为¥${newTotalPrice}，对应原价为¥${originalPrice.toFixed(2)}`);

                        // 智能分配原价到各服务项
                        distributePriceToServices(originalPrice, itemIndex, serviceCheckboxes, washPriceInput, darnPriceInput, alterPriceInput, otherPriceInput);

                        // 重新应用折扣
                        setTimeout(() => updateTotalPrice(), 10);
                    } else {
                        clothingItemsData[itemIndex].price = newTotalPrice;
                        // 智能分配价格到各服务项
                        distributePriceToServices(newTotalPrice, itemIndex, serviceCheckboxes, washPriceInput, darnPriceInput, alterPriceInput, otherPriceInput);
                    }
                }
            });

            // 备注变更
            const noteTextarea = itemElement.querySelector('.clothing-note');
            noteTextarea.addEventListener('change', function() {
                if (clothingItemsData[itemIndex]) {
                    clothingItemsData[itemIndex].remarks = this.value;
                }
            });

            // 瑕疵描述变更
            const flawTextarea = itemElement.querySelector('.clothing-flaw');
            if (flawTextarea) {
                flawTextarea.addEventListener('change', function() {
                    if (clothingItemsData[itemIndex]) {
                        clothingItemsData[itemIndex].flaw = this.value;
                    }
                });
            }

            // 服务类型变更事件
            const serviceCheckboxes = itemElement.querySelectorAll('.service-type');
            const washRequirements = itemElement.querySelector('.wash-requirements');
            const luxuryRequirements = itemElement.querySelector('.luxury-wash-requirements');
            const darnRequirements = itemElement.querySelector('.darn-requirements');
            const alterRequirements = itemElement.querySelector('.alter-requirements');
            const otherRequirements = itemElement.querySelector('.other-requirements');
            const darnTextarea = itemElement.querySelector('.darn-requirement');
            const alterTextarea = itemElement.querySelector('.alter-requirement');
            const otherTextarea = itemElement.querySelector('.other-requirement');
            const washPriceInput = itemElement.querySelector('.wash-price');
            const luxuryPriceInput = itemElement.querySelector('.luxury-wash-price');
            const darnPriceInput = itemElement.querySelector('.darn-price');
            const alterPriceInput = itemElement.querySelector('.alter-price');
            const otherPriceInput = itemElement.querySelector('.other-price');

            // 设置默认价格
            if (washPriceInput) {
                washPriceInput.value = defaultServicePrices['精洗'];
            }

            if (luxuryPriceInput) {
                luxuryPriceInput.value = defaultServicePrices['奢洗'];
            }

            if (darnPriceInput) {
                darnPriceInput.value = defaultServicePrices['织补'];
            }

            if (alterPriceInput) {
                alterPriceInput.value = defaultServicePrices['改衣'];
            }

            if (otherPriceInput) {
                otherPriceInput.value = defaultServicePrices['其他'];
            }

            // 更新价格的辅助函数
            function updateTotalPrice() {
                if (clothingItemsData[itemIndex]) {
                    let unitPrice = 0;

                    // 添加精洗价格
                    if (washRequirements && washRequirements.style.display !== 'none' && washPriceInput) {
                        unitPrice += parseFloat(washPriceInput.value || 0);
                    }

                    // 添加奢洗价格
                    if (luxuryRequirements && luxuryRequirements.style.display !== 'none' && luxuryPriceInput) {
                        unitPrice += parseFloat(luxuryPriceInput.value || 0);
                    }

                    // 添加织补价格
                    if (serviceCheckboxes[2].checked && darnPriceInput) {
                        unitPrice += parseFloat(darnPriceInput.value || 0);
                    }

                    // 添加改衣价格
                    if (serviceCheckboxes[3].checked && alterPriceInput) {
                        unitPrice += parseFloat(alterPriceInput.value || 0);
                    }

                    // 添加其他价格
                    if (serviceCheckboxes[4].checked && otherPriceInput) {
                        unitPrice += parseFloat(otherPriceInput.value || 0);
                    }

                    // 获取数量
                    const quantity = clothingItemsData[itemIndex].quantity || 1;

                    // 应用商场客户折扣（如果适用）
                    if (window.currentMallCustomer && clothingItemsData[itemIndex].name) {
                        const productName = clothingItemsData[itemIndex].name;
                        const productType = getProductTypeFromName(productName);
                        const discountResult = applyMallCustomerDiscount(productName, productType, unitPrice);

                        if (discountResult.discountAmount > 0) {
                            // 使用折扣后的单价
                            unitPrice = discountResult.discountedPrice;

                            // 保存折扣信息到数据
                            clothingItemsData[itemIndex].originalPrice = discountResult.originalPrice;
                            clothingItemsData[itemIndex].discountRate = discountResult.discountRate;

                            console.log(`应用折扣: ${productName} 原价¥${discountResult.originalPrice} → 折后¥${unitPrice.toFixed(2)}`);
                        }
                    }

                    // 计算总价 = 单价 × 数量
                    const totalPrice = unitPrice * quantity;

                    priceInput.value = totalPrice.toFixed(2);
                    clothingItemsData[itemIndex].price = totalPrice;

                    console.log(`价格计算: 单价¥${unitPrice.toFixed(2)} × ${quantity}件 = 总价¥${totalPrice.toFixed(2)}`);

                    // 更新订单总计折扣显示
                    updateOrderTotalDiscountDisplay();
                }
            }

            serviceCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    if (this.value === '精洗') {
                        washRequirements.style.display = this.checked ? 'block' : 'none';
                    }
                    if (this.value === '奢洗') {
                        luxuryRequirements.style.display = this.checked ? 'block' : 'none';
                    }
                    if (this.value === '织补') {
                        darnRequirements.style.display = this.checked ? 'block' : 'none';
                    }
                    if (this.value === '改衣') {
                        alterRequirements.style.display = this.checked ? 'block' : 'none';
                    }
                    if (this.value === '其他') {
                        otherRequirements.style.display = this.checked ? 'block' : 'none';
                    }

                    // 更新衣物数据中的服务类型
                    if (clothingItemsData[itemIndex]) {
                        const serviceTypes = [];
                        serviceCheckboxes.forEach(cb => {
                            if (cb.checked) {
                                serviceTypes.push(cb.value);
                            }
                        });
                        clothingItemsData[itemIndex].serviceTypes = serviceTypes;

                        // 同时也更新services属性，确保数据一致性
                        clothingItemsData[itemIndex].services = serviceTypes;
                    }

                    // 更新价格
                    updateTotalPrice();
                });
            });

            // 洗衣价格变更
            if (washPriceInput) {
                washPriceInput.addEventListener('change', function() {
                    if (clothingItemsData[itemIndex]) {
                        clothingItemsData[itemIndex].specialRequirements.fineWashPrice = parseFloat(this.value) || defaultServicePrices['精洗'];
                        updateTotalPrice();
                    }
                });
            }

            // 奢洗价格变更
            if (luxuryPriceInput) {
                luxuryPriceInput.addEventListener('change', function() {
                    if (clothingItemsData[itemIndex]) {
                        clothingItemsData[itemIndex].specialRequirements.luxuryWashPrice = parseFloat(this.value) || defaultServicePrices['奢洗'];
                        updateTotalPrice();
                    }
                });
            }

            // 织补价格变更
            if (darnPriceInput) {
                darnPriceInput.addEventListener('change', function() {
                    if (clothingItemsData[itemIndex]) {
                        clothingItemsData[itemIndex].specialRequirements.darnPrice = parseFloat(this.value) || defaultServicePrices['织补'];
                        updateTotalPrice();
                    }
                });
            }

            // 改衣价格变更
            if (alterPriceInput) {
                alterPriceInput.addEventListener('change', function() {
                    if (clothingItemsData[itemIndex]) {
                        clothingItemsData[itemIndex].specialRequirements.alterPrice = parseFloat(this.value) || defaultServicePrices['改衣'];
                        updateTotalPrice();
                    }
                });
            }

            // 其他价格变更
            if (otherPriceInput) {
                otherPriceInput.addEventListener('change', function() {
                    if (clothingItemsData[itemIndex]) {
                        clothingItemsData[itemIndex].specialRequirements.otherPrice = parseFloat(this.value) || defaultServicePrices['其他'];
                        updateTotalPrice();
                    }
                });
            }

            // 特殊要求变更
            darnTextarea.addEventListener('change', function() {
                if (clothingItemsData[itemIndex]) {
                    clothingItemsData[itemIndex].specialRequirements.darn = this.value;
                }
            });

            alterTextarea.addEventListener('change', function() {
                if (clothingItemsData[itemIndex]) {
                    clothingItemsData[itemIndex].specialRequirements.alter = this.value;
                }
            });

            otherTextarea.addEventListener('change', function() {
                if (clothingItemsData[itemIndex]) {
                    clothingItemsData[itemIndex].specialRequirements.other = this.value;
                }
            });

            // 展开/折叠按钮
            const toggleBtn = itemElement.querySelector('.toggle-collapse');
            toggleBtn.addEventListener('click', function() {
                const item = this.closest('.clothing-item');
                item.classList.toggle('collapsed');

                // 更改按钮文本
                this.textContent = item.classList.contains('collapsed') ? '展开' : '折叠';
            });

            // 移除按钮
            const removeBtn = itemElement.querySelector('.remove-item');
            removeBtn.addEventListener('click', function() {
                const allItems = document.querySelectorAll('.clothing-item');
                if (allItems.length > 1) {
                    const currentItem = this.closest('.clothing-item');
                    const currentIndex = parseInt(currentItem.dataset.itemIndex);

                    console.log(`移除衣物项，索引: ${currentIndex}`);

                    // 从DOM中移除
                    currentItem.remove();

                    // 从数据数组中移除
                    clothingItemsData.splice(currentIndex, 1);
                    console.log(`移除后的衣物数据:`, clothingItemsData);

                    // 更新所有衣物项的索引
                    document.querySelectorAll('.clothing-item').forEach((item, idx) => {
                        console.log(`更新衣物项索引: ${idx}`);
                        item.dataset.itemIndex = idx;
                    });
                } else {
                    alert('至少需要一件衣物');
                }
            });

            // 添加照片按钮
            const addPhotoBtn = itemElement.querySelector('.add-photo-btn');
            addPhotoBtn.addEventListener('click', function() {
                // 保存当前操作的衣物项
                currentPhotoItem = this.closest('.clothing-item');
                openCameraModal();
            });

            // 添加数量控制事件
            const quantityMinus = itemElement.querySelector('.quantity-minus');
            const quantityPlus = itemElement.querySelector('.quantity-plus');
            const quantityInput = itemElement.querySelector('.clothing-quantity');

            if (quantityMinus && quantityPlus && quantityInput) {
                quantityMinus.addEventListener('click', function() {
                    let currentValue = parseInt(quantityInput.value) || 1;
                    if (currentValue > 1) {
                        quantityInput.value = currentValue - 1;
                        // 更新数据
                        if (clothingItemsData[itemIndex]) {
                            clothingItemsData[itemIndex].quantity = currentValue - 1;
                        }
                        updateTotalPrice();
                    }
                });

                quantityPlus.addEventListener('click', function() {
                    let currentValue = parseInt(quantityInput.value) || 1;
                    if (currentValue < 99) {
                        quantityInput.value = currentValue + 1;
                        // 更新数据
                        if (clothingItemsData[itemIndex]) {
                            clothingItemsData[itemIndex].quantity = currentValue + 1;
                        }
                        updateTotalPrice();
                    }
                });

                quantityInput.addEventListener('change', function() {
                    let value = parseInt(this.value) || 1;
                    if (value < 1) value = 1;
                    if (value > 99) value = 99;
                    this.value = value;
                    // 更新数据
                    if (clothingItemsData[itemIndex]) {
                        clothingItemsData[itemIndex].quantity = value;
                    }
                    updateTotalPrice();
                });
            }
        }

        // 添加衣物项
        function addNewClothingItem() {
            // 获取当前衣物项数量
            const itemCount = document.querySelectorAll('.clothing-item').length;

            // 克隆模板
            const template = document.getElementById('clothing-item-template');
            const clone = document.importNode(template.content, true);

            // 更新索引
            const newItemIndex = itemCount;
            clone.querySelector('.clothing-item').dataset.itemIndex = newItemIndex;

            // 填充衣物选项
            const selectElement = clone.querySelector('.clothing-name');
            fetch('/api/product_types')
                .then(response => response.json())
                .then(data => {
                    if (data.products && data.products.length > 0) {
                        data.products.forEach(product => {
                            const optionElement = document.createElement('option');
                            optionElement.value = product.name;
                            optionElement.textContent = product.name;
                            optionElement.dataset.price = product.price;
                            selectElement.appendChild(optionElement);
                        });
                    } else {
                        console.error('未获取到商品数据');
                    }
                })
                .catch(error => console.error('获取商品列表失败:', error));

            // 添加事件监听器
            setupClothingItemEvents(clone);

            // 添加到容器
            document.getElementById('clothingItems').appendChild(clone);

            // 创建新的衣物数据对象并添加到数组
            clothingItemsData[newItemIndex] = {
                name: '',
                color: '白色',
                quantity: 1,  // 添加数量字段，默认为1
                serviceTypes: ['洗衣'],
                specialRequirements: {
                    wash: '',
                    fineWashPrice: getServicePrice('精洗'),
                    luxuryWashPrice: getServicePrice('奢洗'),
                    darn: '',
                    darnPrice: getServicePrice('织补'),
                    alter: '',
                    alterPrice: getServicePrice('改衣'),
                    other: '',
                    otherPrice: getServicePrice('其他')
                },
                price: getServicePrice('洗衣'), // 默认只有洗衣服务的价格
                remarks: '',
                flaw: '',  // 添加瑕疵字段
                photos: []
            };

            console.log(`添加了新衣物项，索引: ${newItemIndex}，当前衣物数据:`, clothingItemsData);
        }

        // 打开摄像头弹窗
        function openCameraModal() {
            const modal = document.getElementById('cameraModal');
            const cameraFeed = document.getElementById('modalCameraFeed');
            const takePhotoBtn = document.getElementById('takePhotoBtn');
            const cancelBtn = document.getElementById('cancelBtn');

            // 显示模态框
            modal.style.display = 'block';

            // 重置UI
            takePhotoBtn.style.display = 'inline-block';
            cancelBtn.style.display = 'inline-block';
            cameraFeed.style.display = 'block';

            // 优先尝试调用后置摄像头，若失败则回退到默认摄像头
            const constraints = { video: { facingMode: { ideal: 'environment' } } };
            navigator.mediaDevices.getUserMedia(constraints)
                .then(stream => {
                    cameraStream = stream;
                    cameraFeed.srcObject = stream;
                })
                .catch(err => {
                    console.warn('后置摄像头不可用，尝试默认摄像头', err);
                    navigator.mediaDevices.getUserMedia({ video: true })
                        .then(stream => {
                            cameraStream = stream;
                            cameraFeed.srcObject = stream;
                        })
                        .catch(error => {
                            console.error('无法访问摄像头:', error);
                            alert('无法访问摄像头，请检查权限设置。');
                            closeCameraModal();
                        });
                });
        }

        // 关闭摄像头弹窗
        function closeCameraModal() {
            const modal = document.getElementById('cameraModal');

            // 停止摄像头
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
            }

            // 隐藏模态框
            modal.style.display = 'none';
        }

        // 显示加载指示器
        function showLoadingIndicator(text = "处理中...") {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const loadingText = document.getElementById('loadingText');

            if (loadingText) {
                loadingText.textContent = text;
            }

            if (loadingIndicator) {
                loadingIndicator.style.display = 'flex';
            }
        }

        // 隐藏加载指示器
        function hideLoadingIndicator() {
            const loadingIndicator = document.getElementById('loadingIndicator');

            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        }

        // 添加照片到指定衣物项的照片库
        function addPhotoToGallery(itemElement, photoDataUrl) {
            try {
                console.log("添加照片到相册");
                const itemIndex = parseInt(itemElement.dataset.itemIndex);
                console.log(`衣物索引: ${itemIndex}`);

                if (isNaN(itemIndex)) {
                    console.error("无效的衣物索引");
                    return;
                }

                const thumbnailsContainer = itemElement.querySelector('.photo-thumbnails');
                const thumbnailContainer = document.createElement('div');
                thumbnailContainer.className = 'photo-thumbnail-container';

                // 创建缩略图
                const thumbnail = document.createElement('img');
                thumbnail.className = 'photo-thumbnail';
                thumbnail.src = photoDataUrl;
                thumbnail.addEventListener('click', function() {
                    // 预览大图功能（可选）
                    alert('查看大图功能待实现');
                });

                // 创建删除按钮
                const removeBtn = document.createElement('div');
                removeBtn.className = 'remove-photo';
                removeBtn.textContent = '×';
                removeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();

                    // 从数据中移除照片
                    if (clothingItemsData[itemIndex]) {
                        const photoIndex = Array.from(thumbnailsContainer.querySelectorAll('.photo-thumbnail-container')).indexOf(thumbnailContainer);
                        if (photoIndex >= 0) {
                            clothingItemsData[itemIndex].photos.splice(photoIndex, 1);
                            console.log(`从索引 ${photoIndex} 移除照片`);
                        }
                    }

                    thumbnailContainer.remove();
                });

                // 添加到容器
                thumbnailContainer.appendChild(thumbnail);
                thumbnailContainer.appendChild(removeBtn);
                thumbnailsContainer.appendChild(thumbnailContainer);

                // 创建隐藏的输入字段保存照片数据
                const photoInput = document.createElement('input');
                photoInput.type = 'hidden';
                photoInput.name = `clothingImage_${thumbnailsContainer.querySelectorAll('.photo-thumbnail-container').length-1}`;
                // 不要在这里执行 split，直接使用完整数据URL
                photoInput.value = photoDataUrl;
                thumbnailContainer.appendChild(photoInput);

                // 添加照片数据到衣物数据
                if (clothingItemsData[itemIndex]) {
                    // 创建一个短版本的URL用于存储，以避免数据过大
                    const shortDataUrl = photoDataUrl.substring(0, 100) + "..."; // 只用于日志输出
                    console.log(`添加照片到衣物数据, 数据长度: ${photoDataUrl.length}, 预览: ${shortDataUrl}`);
                    clothingItemsData[itemIndex].photos.push(photoDataUrl);
                } else {
                    console.error(`衣物数据中不存在索引 ${itemIndex}`);
                }
            } catch (e) {
                console.error("添加照片出错:", e);
                alert(`添加照片出错: ${e.message}`);
            }
        }

        // 初始化摄像头模态框
        document.addEventListener('DOMContentLoaded', async function() {
            console.log("页面加载完成，初始化应用...");

            // 从数据库加载服务价格
            try {
                await loadServicePricesFromDatabase();
                console.log("服务价格加载完成:", defaultServicePrices);
            } catch (error) {
                console.warn("服务价格加载失败，使用默认价格:", error);
            }

            // 确保clothingItemsData数组正确初始化
            clothingItemsData = [];

            const modal = document.getElementById('cameraModal');
            const closeBtn = document.querySelector('.close-modal');
            const takePhotoBtn = document.getElementById('takePhotoBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            const cameraFeed = document.getElementById('modalCameraFeed');
            const photoCanvas = document.getElementById('photoCanvas');
            const previewImage = document.getElementById('previewImage');

            // 关闭按钮
            if (closeBtn) {
                closeBtn.addEventListener('click', closeCameraModal);
            } else {
                console.error('未找到关闭按钮(.close-modal)');
            }

            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    closeCameraModal();
                }
            });

            // 拍照按钮
            if (takePhotoBtn) {
                takePhotoBtn.addEventListener('click', async function() {
                    try {
                        console.log("开始拍照");
                        // 显示加载指示器
                        showLoadingIndicator();

                        // 设置canvas大小与视频流相同
                        photoCanvas.width = cameraFeed.videoWidth;
                        photoCanvas.height = cameraFeed.videoHeight;

                        console.log(`Canvas尺寸: ${photoCanvas.width}x${photoCanvas.height}`);

                        // 将视频帧绘制到canvas上
                        const context = photoCanvas.getContext('2d');
                        context.drawImage(cameraFeed, 0, 0, photoCanvas.width, photoCanvas.height);

                        // 生成原始照片URL
                        const originalPhotoDataUrl = photoCanvas.toDataURL('image/jpeg', 1.0);

                        // 使用高级图片压缩模块压缩图片
                        console.log("开始压缩图片...");
                        const deviceConfig = window.ImageCompressor.getDeviceOptimizedConfig();
                        const compressedPhotoDataUrl = await window.ImageCompressor.compress(originalPhotoDataUrl, deviceConfig);
                        console.log(`压缩后图片数据URL长度: ${compressedPhotoDataUrl.length} 字符`);

                        if (currentPhotoItem) {
                            addPhotoToGallery(currentPhotoItem, compressedPhotoDataUrl);
                            console.log("照片已添加到相册");
                        } else {
                            console.error("未找到当前操作的衣物项");
                        }

                        // 隐藏加载指示器
                        hideLoadingIndicator();

                        // 关闭摄像头弹窗
                        closeCameraModal();
                    } catch (e) {
                        // 隐藏加载指示器
                        hideLoadingIndicator();

                        console.error("拍照出错:", e);
                        alert(`拍照出错: ${e.message}`);
                    }
                });
            } else {
                console.error('未找到拍照按钮(#takePhotoBtn)');
            }

            // 取消按钮
            if (cancelBtn) {
                cancelBtn.addEventListener('click', closeCameraModal);
            } else {
                console.error('未找到取消按钮(#cancelBtn)');
            }

            // 初始化第一个衣物项
            addNewClothingItem();

            // 测试重要按钮是否存在
            const buttons = {
                'submitOrder': document.getElementById('submitOrder'),
                'addClothing': document.getElementById('addClothing'),
                'nextToClothing': document.getElementById('nextToClothing'),
                'nextToPayment': document.getElementById('nextToPayment'),
                'searchCustomer': document.getElementById('searchCustomer'),
                'openRecharge': document.getElementById('openRecharge'),
                'closeRechargeModal': document.getElementById('closeRechargeModal'),
                'confirmRecharge': document.getElementById('confirmRecharge')
            };

            for (let key in buttons) {
                if (!buttons[key]) {
                    console.error(`错误: 未找到按钮 #${key}`);
                } else {
                    console.log(`按钮 #${key} 已找到并准备就绪`);

                    // 对submitOrder按钮添加点击事件
                    if (key === 'submitOrder') {
                        initializeSubmitOrderButton(buttons[key]);
                    }
                }
            }

            console.log("初始化完成，衣物数据:", clothingItemsData);
        });

        // 初始化提交订单按钮的函数
        function initializeSubmitOrderButton(button) {
            button.addEventListener('click', async function() {
                showLoading(true);

                try {
                    const customerName = customerId ? customerDetails.name : document.getElementById('customerName').value;
                    const customerPhone = customerId ? customerDetails.phone : document.getElementById('customerPhone').value;
                    const address = document.getElementById('address').value;

                    // 获取支付方式
                    const paymentMethod = document.getElementById('paymentMethod').value;

                    // 验证数据
                    if (!isMallCustomer && (!customerName || !customerPhone)) {
                        showError("请提供客户姓名和电话");
                        showLoading(false);
                        return;
                    }

                    // 获取有效的衣物项
                    const clothingItems = clothingItemsData.filter(item => item && item.name);

                    if (clothingItems.length === 0) {
                        showError("请至少添加一件衣物");
                        showLoading(false);
                        return;
                    }

                    // 准备提交数据
                    const orderData = {
                        customer_name: customerName,
                        customer_phone: customerPhone,
                        address: address,
                        payment_method: paymentMethod,
                        items: clothingItems,
                        customer_id: customerId || null
                    };

                    if (isMallCustomer) {
                        orderData.is_mall_customer = true;
                        orderData.mall_customer_name = document.getElementById('mallCustomerName').value;
                        orderData.mall_customer_phone = document.getElementById('mallCustomerPhone').value;
                        // 添加商场客户ID
                        const mallCustomerName = document.getElementById('mallCustomerName');
                        if (mallCustomerName && mallCustomerName.dataset.mallCustomerId) {
                            orderData.mall_customer_id = parseInt(mallCustomerName.dataset.mallCustomerId);
                        } else {
                            showError("请选择有效的商场客户");
                            showLoading(false);
                            return;
                        }
                    }

                    // 发送请求
                    const response = await fetch('/submit_order', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(orderData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 显示成功信息
                        const html = `
                            <div class="alert alert-success">
                                <h4>订单创建成功!</h4>
                                <p>订单号: <strong>${result.order_number}</strong></p>
                                <p>总金额: ¥${result.total_amount.toFixed(2)}</p>
                                <div style="margin-top: 15px;">
                                    <button id="printReceiptBtn" class="btn btn-primary">打印小票</button>
                                    <button id="printLabelBtn" class="btn btn-info" style="margin-left: 10px;">打印水洗唛</button>
                                    <button id="newOrderBtn" class="btn btn-success" style="margin-left: 10px;">创建新订单</button>
                                </div>
                            </div>
                        `;

                        document.getElementById('orderResults').innerHTML = html;

                        // 保存订单信息到全局变量供打印使用
                        currentOrderResult = result;

                        // 输出订单结果以便调试
                        console.log("订单提交成功，返回数据:", result);

                        // ================= 自动打印逻辑 =================
                        (function autoPrintAfterOrderCreated() {
                            try {
                                const orderIdAuto = result.id || result.order_id || result.orderId;
                                if (!orderIdAuto) {
                                    console.warn('自动打印: 未找到有效的订单ID');
                                    return;
                                }

                                // -------- 自动打印功能已取消，直接返回 --------
                                return;
                                // ... existing code ...
                            } catch (e) {
                                console.error('自动打印过程中发生错误:', e);
                            }
                        })();

                        // 确保有效的订单ID
                        if (!result.id && result.order_id) {
                            console.log("使用order_id作为id:", result.order_id);
                            result.id = result.order_id;
                        }

                        // 设置打印小票按钮事件
                        const printReceiptButton = document.getElementById('printReceiptBtn');
                        if (printReceiptButton) {
                            // 直接设置点击事件 - 使用Lodop打印选择
                            printReceiptButton.onclick = function() {
                                console.log("订单结果页打印小票按钮被点击 - 选择打印方式");
                                console.log("当前订单结果:", result);
                                try {
                                    // 确定正确的订单ID
                                    const orderId = result.id || result.order_id || result.orderId;
                                    if (!orderId) {
                                        throw new Error('找不到有效的订单ID');
                                    }
                                    console.log("使用订单ID打印:", orderId);

                                    // 使用统一的打印模式选择功能
                                    if (typeof window.printReceiptWithModeSelection === 'function') {
                                        window.printReceiptWithModeSelection(orderId);
                                    } else {
                                        // 降级到原有打印功能
                                        printReceipt(orderId, showLoading);
                                    }
                                } catch (error) {
                                    console.error("打印小票出错:", error);
                                    alert("打印小票失败: " + error.message);
                                }
                            };
                        }

                        // 设置打印水洗唛按钮事件
                        const printLabelButton = document.getElementById('printLabelBtn');
                        if (printLabelButton) {
                            // 直接设置点击事件 - 使用Lodop打印选择
                            printLabelButton.onclick = function() {
                                console.log("订单结果页打印水洗唛按钮被点击");
                                console.log("订单创建结果:", result);

                                const orderId = result.id || result.order_id || result.orderId;
                                if (orderId) {
                                    // 使用统一的打印模式选择功能
                                    if (typeof window.printWashLabelWithModeSelection === 'function') {
                                        window.printWashLabelWithModeSelection(orderId, 'all');
                                    } else {
                                        // 降级到原有打印功能
                                        printLabel(orderId, showLoading, 'direct');
                                    }
                                } else {
                                    console.error("无法找到订单ID");
                                    alert("无法找到订单ID，请联系管理员");
                                }
                            };
                        }

                        // 设置新订单按钮事件
                        const newOrderButton = document.getElementById('newOrderBtn');
                        if (newOrderButton) {
                            // 直接设置点击事件
                            newOrderButton.onclick = function() {
                                window.location.reload();
                            };
                        }

                        // 隐藏提交按钮
                        button.style.display = 'none';

                        // 清除订单数据
                        if (result.new_balance !== undefined) {
                            // 如果返回了新的余额，更新用户余额显示
                            document.getElementById('customerBalance').textContent = result.new_balance.toFixed(2);
                            document.getElementById('summaryBalance').textContent = result.new_balance.toFixed(2);
                        }
                    } else {
                        showError(result.error || "订单提交失败，请重试");
                    }
                } catch (error) {
                    console.error("订单提交错误:", error);
                    showError("网络错误，请重试");
                }

                showLoading(false);
            });
        }

        // 计算赠送金额
        function calculateGiftAmount(amount) {
            if (!amount || amount <= 0) {
                document.getElementById('giftAmountDisplay').textContent = '¥0.00';
                return;
            }

            fetch('/api/calculate_gift', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ amount: amount })
            })
            .then(response => response.json())
            .then(data => {
                if (data.gift_amount !== undefined) {
                    document.getElementById('giftAmountDisplay').textContent = `¥${data.gift_amount.toFixed(2)}`;
                } else {
                    document.getElementById('giftAmountDisplay').textContent = '¥0.00';
                }
            })
            .catch(error => {
                console.error('计算赠送金额失败:', error);
                document.getElementById('giftAmountDisplay').textContent = '¥0.00';
            });
        }

        // 初始化充值相关的所有事件处理
        function initializeRechargeEvents() {
            console.log('初始化充值功能...');
            // 充值功能相关代码
            let selectedAmount = 0;
            let selectedPaymentMethod = '';

            // 打开充值弹窗
            const openRechargeBtn = document.getElementById('openRecharge');
            if (openRechargeBtn) {
                openRechargeBtn.addEventListener('click', function() {
                    const modal = document.getElementById('rechargeModal');
                    if (modal) {
                        modal.style.display = 'block';

                        // 查询用户状态
                        fetch(`/search_customer?phone=${document.getElementById('customerPhone').value}`)
                            .then(response => response.json())
                            .then(data => {
                                const statusElem = document.getElementById('rechargeUserStatus');
                                if (data.found) {
                                    // 计算余额信息
                                    const balance = data.balance || 0;
                                    const giftBalance = data.gift_balance || 0;
                                    const totalBalance = balance + giftBalance;

                                    // 显示用户信息
                                    statusElem.innerHTML = `为用户 <strong>${data.name}</strong> 充值，当前总余额: ¥${totalBalance.toFixed(2)}`;
                                    statusElem.style.color = '#28a745';

                                    // 优化：自动同步姓名到新客户表单（以备后续流程使用）
                                    document.getElementById('customerName').value = data.name;

                                    // 同步显示用户信息到主界面
                                    document.getElementById('customerNameDisplay').textContent = data.name;
                                    document.getElementById('customerBalance').textContent = totalBalance.toFixed(2);

                                    // 显示详细余额信息
                                    const balanceDetails = document.getElementById('balanceDetails');
                                    if (balanceDetails) {
                                        balanceDetails.innerHTML = generateBalanceDetailsHTML(balance, giftBalance, data);
                                    }

                                    document.getElementById('customerDetails').classList.remove('hidden');
                                    document.getElementById('newCustomerForm').classList.add('hidden');

                                    // 更新摘要页面的客户名称
                                    document.getElementById('summaryName').textContent = data.name;
                                } else {
                                    // 显示新用户提示
                                    statusElem.innerHTML = `将为新用户 <strong>${document.getElementById('customerPhone').value}</strong> 创建账户并充值`;
                                    statusElem.style.color = '#007BFF';

                                    // 清空表单，准备填写新用户信息
                                    document.getElementById('customerName').value = '';
                                    document.getElementById('customerDetails').classList.add('hidden');
                                    document.getElementById('newCustomerForm').classList.remove('hidden');
                                    document.getElementById('summaryName').textContent = '';
                                }
                            })
                            .catch(error => {
                                document.getElementById('rechargeUserStatus').innerHTML = '查询用户信息失败';
                                document.getElementById('rechargeUserStatus').style.color = '#dc3545';
                            });

                        // 重置选择状态
                        document.querySelectorAll('.recharge-amount-option').forEach(option => {
                            option.classList.remove('selected');
                        });
                        document.querySelectorAll('.recharge-payment-method').forEach(method => {
                            method.classList.remove('selected');
                        });

                        const customAmountInput = document.getElementById('customRechargeAmount');
                        if (customAmountInput) {
                            customAmountInput.value = '';
                        }
                    } else {
                        console.error('未找到充值弹窗(#rechargeModal)');
                    }
                });
            }

            // 关闭充值弹窗
            const closeRechargeModalBtn = document.getElementById('closeRechargeModal');
            console.log('关闭充值弹窗按钮:', closeRechargeModalBtn);

            if (closeRechargeModalBtn) {
                // 移除可能已存在的事件监听器
                const newCloseBtn = closeRechargeModalBtn.cloneNode(true);
                closeRechargeModalBtn.parentNode.replaceChild(newCloseBtn, closeRechargeModalBtn);

                newCloseBtn.addEventListener('click', function(e) {
                    console.log('关闭充值弹窗按钮被点击');
                    e.preventDefault();
                    e.stopPropagation();

                    const modal = document.getElementById('rechargeModal');
                    if (modal) {
                        modal.style.display = 'none';
                        console.log('充值弹窗已关闭');
                    } else {
                        console.error('找不到充值弹窗');
                    }
                });
            }

            // 增加点击弹窗外部关闭功能
            window.addEventListener('click', function(event) {
                const modal = document.getElementById('rechargeModal');
                if (event.target === modal) {
                    console.log('点击弹窗外部关闭');
                    modal.style.display = 'none';
                }
            });

            // 选择充值金额
            const amountOptions = document.querySelectorAll('.recharge-amount-option');
            console.log(`找到${amountOptions.length}个充值金额选项`);

            amountOptions.forEach(option => {
                // 确保移除任何可能已存在的事件监听器
                const newOption = option.cloneNode(true);
                option.parentNode.replaceChild(newOption, option);

                newOption.addEventListener('click', function(e) {
                    console.log('充值金额选项被点击:', this.dataset.amount);
                    e.preventDefault();
                    e.stopPropagation();

                    // 清除所有选中状态
                    document.querySelectorAll('.recharge-amount-option').forEach(opt => {
                        opt.classList.remove('selected');
                    });

                    // 添加当前选中状态
                    this.classList.add('selected');
                    selectedAmount = parseFloat(this.dataset.amount);
                    console.log('已选择金额:', selectedAmount);

                    // 清除自定义金额
                    const customAmountInput = document.getElementById('customRechargeAmount');
                    if (customAmountInput) {
                        customAmountInput.value = '';
                    }

                    // 计算赠送金额
                    calculateGiftAmount(selectedAmount);
                });
            });

            // 自定义充值金额
            const customAmountInput = document.getElementById('customRechargeAmount');
            if (customAmountInput) {
                customAmountInput.addEventListener('input', function() {
                    if (this.value) {
                        selectedAmount = parseFloat(this.value);

                        // 清除预设金额的选中状态
                        document.querySelectorAll('.recharge-amount-option').forEach(option => {
                            option.classList.remove('selected');
                        });

                        // 计算赠送金额
                        calculateGiftAmount(selectedAmount);
                    } else {
                        // 清空时重置赠送金额显示
                        document.getElementById('giftAmountDisplay').textContent = '¥0.00';
                    }
                });
            }

            // 选择支付方式
            const paymentMethods = document.querySelectorAll('.recharge-payment-method');
            console.log(`找到${paymentMethods.length}个支付方式选项`);

            paymentMethods.forEach(method => {
                // 确保移除任何可能已存在的事件监听器
                const newMethod = method.cloneNode(true);
                method.parentNode.replaceChild(newMethod, method);

                newMethod.addEventListener('click', function(e) {
                    console.log('支付方式被点击:', this.dataset.method);
                    e.preventDefault();
                    e.stopPropagation();

                    // 清除所有选中状态
                    document.querySelectorAll('.recharge-payment-method').forEach(m => {
                        m.classList.remove('selected');
                    });

                    // 添加当前选中状态
                    this.classList.add('selected');
                    selectedPaymentMethod = this.dataset.method;
                    console.log('已选择支付方式:', selectedPaymentMethod);
                });
            });

            // 确认充值
            const confirmRechargeBtn = document.getElementById('confirmRecharge');
            if (confirmRechargeBtn) {
                confirmRechargeBtn.addEventListener('click', async function() {
                    if (!selectedAmount || selectedAmount <= 0) {
                        alert('请选择或输入有效的充值金额');
                        return;
                    }

                    if (!selectedPaymentMethod) {
                        alert('请选择支付方式');
                        return;
                    }

                    const phone = document.getElementById('customerPhone').value;

                    try {
                        // 检查是新用户还是老用户
                        const statusText = document.getElementById('rechargeUserStatus').textContent;
                        const isNewUser = statusText.includes('新用户');

                        // 如果是新用户，尝试从新用户表单获取名字
                        let customerName = '';
                        if (isNewUser) {
                            customerName = document.getElementById('customerName').value || phone;
                        }

                        // 发送充值请求到服务器
                        const response = await fetch('/recharge_account', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                phone: phone,
                                amount: selectedAmount,
                                paymentMethod: selectedPaymentMethod,
                                customer_name: customerName
                            })
                        });

                        if (response.ok) {
                            const data = await response.json();

                            // 如果是新用户，显示用户详情
                            if (data.isNewCustomer) {
                                // 显示客户信息
                                document.getElementById('customerNameDisplay').textContent = data.customer_name;
                                document.getElementById('customerDetails').classList.remove('hidden');
                                document.getElementById('newCustomerForm').classList.add('hidden');

                                // 自动填充客户姓名到表单中，保持数据一致性
                                document.getElementById('customerName').value = data.customer_name;

                                // 更新摘要页面的客户名称
                                document.getElementById('summaryName').textContent = data.customer_name;
                            } else {
                                // 确保老用户数据一致性
                                document.getElementById('customerNameDisplay').textContent = data.customer_name;
                                document.getElementById('summaryName').textContent = data.customer_name;
                            }

                            // 更新显示的余额
                            document.getElementById('customerBalance').textContent = data.newBalance.toFixed(2);

                            // 更新详细余额信息
                            const balanceDetails = document.getElementById('balanceDetails');
                            if (balanceDetails && data.balance !== undefined && data.gift_balance !== undefined) {
                                balanceDetails.innerHTML = generateBalanceDetailsHTML(data.balance, data.gift_balance, data);
                            }

                            // 关闭充值弹窗
                            const modal = document.getElementById('rechargeModal');
                            if (modal) {
                                modal.style.display = 'none';
                            }

                            // 构建充值成功消息
                            let successMessage = '';
                            const giftMessage = data.giftAmount > 0 ? `，赠送金额: ¥${data.giftAmount.toFixed(2)}` : '';

                            if (data.isNewCustomer) {
                                successMessage = `已为新用户 ${data.customer_name} 创建账户并充值成功！新余额: ¥${data.newBalance.toFixed(2)}${giftMessage}`;
                            } else {
                                successMessage = `充值成功！新余额: ¥${data.newBalance.toFixed(2)}${giftMessage}`;
                            }

                            // 准备充值小票数据
                            const rechargeReceiptData = {
                                customer_name: data.customer_name,
                                phone: rechargeData.phone,
                                amount: rechargeData.amount,
                                giftAmount: data.giftAmount || 0,
                                paymentMethod: rechargeData.paymentMethod,
                                newBalance: data.newBalance,
                                isNewCustomer: data.isNewCustomer || false,
                                operator: data.operator || '{{ session.staff_name or "系统" }}'
                            };

                            // 自动调用充值小票打印（默认使用 Lodop）
                            if (typeof window.autoPrintRechargeReceipt === 'function') {
                                window.autoPrintRechargeReceipt(rechargeReceiptData);
                            }
                            // 弹出成功提示
                            alert(successMessage);
                        } else {
                            const errorText = await response.text();
                            try {
                                const errorData = JSON.parse(errorText);
                                alert(`充值失败: ${errorData.error || errorText}`);
                            } catch (e) {
                                alert(`充值失败: ${errorText}`);
                            }
                        }
                    } catch (error) {
                        console.error('充值过程出错:', error);
                        alert(`网络错误: ${error.message}`);
                    }
                });
            }
        }

        // 添加衣物按钮
        document.getElementById('addClothing').addEventListener('click', function() {
            // 折叠所有现有的衣物项
            document.querySelectorAll('.clothing-item').forEach(item => {
                if (!item.classList.contains('collapsed')) {
                    item.classList.add('collapsed');
                    const toggleBtn = item.querySelector('.toggle-collapse');
                    if (toggleBtn) {
                        toggleBtn.textContent = '展开';
                    }
                }
            });

            // 添加新的衣物项
            addNewClothingItem();

            // 滚动到新添加的衣物项
            setTimeout(() => {
                const newItem = document.querySelector('.clothing-item:last-child');
                if (newItem) {
                    newItem.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 100);
        });

        // 下一步按钮
        document.getElementById('nextToClothing').addEventListener('click', function() {
            // 获取客户类型
            const customerType = document.querySelector('.customer-type-option.active').getAttribute('data-type');

            if (customerType === 'normal') {
                // 普通客户验证
                const phone = document.getElementById('customerPhone').value;
                if (!phone) {
                    alert('请输入手机号');
                    return;
                }

                if (document.getElementById('customerDetails').classList.contains('hidden')) {
                    const name = document.getElementById('customerName').value;
                    if (!name) {
                        alert('请输入客户姓名');
                        return;
                    }
                }
            } else {
                // 商场客户验证
                const mallCustomerInput = document.getElementById('mallCustomerName');
                const mallCustomerId = mallCustomerInput.dataset.mallCustomerId;
                const mallCustomerName = mallCustomerInput.value;

                if (!mallCustomerId || !mallCustomerName) {
                    alert('请选择有效的商场客户');
                    return;
                }

                // 商场客户已经选择，不需要验证手机号
                document.getElementById('summaryName').textContent = mallCustomerName;
            }

            // 激活下一个标签页
            document.querySelectorAll('.tab-button')[1].click();
        });

        // 修改验证照片的逻辑
        document.getElementById('nextToPayment').addEventListener('click', function() {
            // 验证衣物信息
            const clothingItemsDom = document.querySelectorAll('.clothing-item');
            let isValid = true;
            let totalPrice = 0;

            // 清空摘要
            document.getElementById('clothingSummary').innerHTML = '';

            // 获取客户类型
            const customerType = document.querySelector('.customer-type-option.active').getAttribute('data-type');

            // 填充客户信息摘要
            if (customerType === 'normal') {
                // 普通客户
                if (document.getElementById('customerDetails').classList.contains('hidden')) {
                    document.getElementById('summaryName').textContent = document.getElementById('customerName').value;
                } else {
                    document.getElementById('summaryName').textContent = document.getElementById('customerNameDisplay').textContent;
                }
                document.getElementById('summaryPhone').textContent = document.getElementById('customerPhone').value;

                // 普通客户 - 确保月结选项不可见，余额选项可见
                const paymentMethodSelect = document.getElementById('paymentMethod');
                Array.from(paymentMethodSelect.options).forEach(option => {
                    if (option.value === '月结') {
                        option.remove(); // 移除月结选项
                    }
                    if (option.value === '余额') {
                        option.style.display = ''; // 显示余额选项
                    }
                });
            } else {
                // 商场客户
                const mallCustomerName = document.getElementById('mallCustomerName').value;
                const mallCustomerPhone = document.getElementById('mallCustomerPhone').value || '未提供';

                document.getElementById('summaryName').textContent = mallCustomerName;
                document.getElementById('summaryPhone').textContent = mallCustomerPhone;

                // 商场客户 - 添加月结选项并默认选中
                const paymentMethodSelect = document.getElementById('paymentMethod');

                // 检查是否已存在月结选项
                let monthlyOption = Array.from(paymentMethodSelect.options).find(opt => opt.value === '月结');

                if (!monthlyOption) {
                    monthlyOption = document.createElement('option');
                    monthlyOption.value = '月结';
                    monthlyOption.textContent = '月结';
                    paymentMethodSelect.appendChild(monthlyOption);
                }

                // 默认选择月结
                monthlyOption.selected = true;

                // 隐藏余额支付选项
                Array.from(paymentMethodSelect.options).forEach(option => {
                    if (option.value === '余额') {
                        option.style.display = 'none';
                    }
                });

                // 隐藏余额信息区域
                document.getElementById('balancePaymentInfo').style.display = 'none';
            }

            // 更新衣物总数 - 修复：使用数量总和而不是条目数
            let totalQuantity = 0;
            clothingItemsDom.forEach(item => {
                const quantityInput = item.querySelector('.clothing-quantity');
                const quantity = parseInt(quantityInput?.value || 1);
                totalQuantity += quantity;
            });
            document.getElementById('totalItems').textContent = totalQuantity;

            // 添加衣物摘要和计算总价
            clothingItemsDom.forEach((item, index) => {
                const nameSelect = item.querySelector('.clothing-name');
                const name = nameSelect.value;
                const color = item.querySelector('.clothing-color').value;
                const price = parseFloat(item.querySelector('.clothing-price').value || 0);
                const note = item.querySelector('.clothing-note').value;

                // 获取数量
                const quantityInput = item.querySelector('.clothing-quantity');
                const quantity = parseInt(quantityInput?.value || 1);

                const serviceTypes = [];

                item.querySelectorAll('.service-type:checked').forEach(checkbox => {
                    serviceTypes.push(checkbox.value);
                });

                // 获取特殊要求
                let requirements = '';
                let serviceDescription = [];

                if (serviceTypes.includes('洗衣')) {
                    const fineWashPrice = parseFloat(item.querySelector('.wash-price').value || 0);
                    const luxuryPrice   = parseFloat(item.querySelector('.luxury-wash-price').value || 0);
                    if(fineWashPrice){serviceDescription.push(`精洗(¥${fineWashPrice})`);}
                    if(luxuryPrice){serviceDescription.push(`奢洗(¥${luxuryPrice})`);}
                }

                if (serviceTypes.includes('织补')) {
                    const darnReq = item.querySelector('.darn-requirement').value;
                    const darnPrice = parseFloat(item.querySelector('.darn-price').value || 0);

                    if (darnReq) {
                        requirements += `织补要求: ${darnReq}; `;
                    }

                    serviceDescription.push(`织补(¥${darnPrice})`);
                }
                if (serviceTypes.includes('改衣')) {
                    const alterReq = item.querySelector('.alter-requirement').value;
                    const alterPrice = parseFloat(item.querySelector('.alter-price').value || 0);

                    if (alterReq) {
                        requirements += `改衣要求: ${alterReq}; `;
                    }

                    serviceDescription.push(`改衣(¥${alterPrice})`);
                }
                if (serviceTypes.includes('其他')) {
                    const otherReq = item.querySelector('.other-requirement').value;
                    const otherPrice = parseFloat(item.querySelector('.other-price').value || 0);

                    if (otherReq) {
                        requirements += `其他要求: ${otherReq}; `;
                    }

                    serviceDescription.push(`其他(¥${otherPrice})`);
                }

                if (!name || serviceTypes.length === 0) {
                    isValid = false;
                    return;
                }

                // 照片检查已改为可选项，不再强制要求
                // const photoThumbnails = item.querySelectorAll('.photo-thumbnail');
                // if (photoThumbnails.length === 0) {
                //     alert(`请为第 ${index + 1} 件衣物拍照`);
                //     isValid = false;
                //     return;
                // }

                totalPrice += price;

                // 创建摘要条目
                const summaryItem = document.createElement('div');
                summaryItem.className = 'summary-item';

                // 处理服务要求的显示
                let detailsHTML = `<p>服务: ${serviceDescription.join(', ')}</p>`;

                // 合并相关要求到一行
                let reqInfo = [];
                if (requirements) reqInfo.push(requirements);
                if (note) reqInfo.push(`备注: ${note}`);

                if (reqInfo.length > 0) {
                    detailsHTML += `<p>${reqInfo.join(' | ')}</p>`;
                }

                summaryItem.innerHTML = `
                    <div class="summary-item-header">
                        <span>${index + 1}. ${name} (${color}) × ${quantity}</span>
                        <span>¥${price.toFixed(2)}</span>
                    </div>
                    <div class="summary-item-details">
                        ${detailsHTML}
                    </div>
                `;

                document.getElementById('clothingSummary').appendChild(summaryItem);
            });

            if (!isValid) {
                alert('请完善所有衣物信息');
                return;
            }

            // 更新总价
            document.getElementById('totalAmount').textContent = totalPrice.toFixed(2);

            // 激活下一个标签页
            document.querySelectorAll('.tab-button')[2].click();
        });

        // 生成余额详情HTML，包含折扣信息
        function generateBalanceDetailsHTML(balance, giftBalance, discountData) {
            let detailsHTML = `充值余额: ¥${balance.toFixed(2)} | 赠送余额: ¥${giftBalance.toFixed(2)}`;
            
            // 添加折扣信息
            if (discountData && discountData.discount_rate && discountData.discount_rate !== 1.0) {
                const discountFold = (discountData.discount_rate * 10).toFixed(1);
                // 如果是整数折扣（如9.0），去掉小数点
                const discountText = `会员折扣: ${discountFold.endsWith('.0') ? discountFold.slice(0, -2) : discountFold}折`;
                
                if (discountData.discount_valid) {
                    detailsHTML += ` | <span style="color: #28a745;">${discountText}</span>`;
                    if (discountData.discount_expiry) {
                        detailsHTML += ` (有效至: ${discountData.discount_expiry})`;
                    }
                } else {
                    detailsHTML += ` | <span style="color: #dc3545;">${discountText} (已过期)</span>`;
                }
            }
            
            return detailsHTML;
        }

        // 客户查询函数
        async function searchCustomerByPhone(phone) {
            if (!phone) {
                return;
            }

            try {
                const response = await fetch(`/search_customer?phone=${phone}`);
                const data = await response.json();

                if (data.found) {
                    // 存储客户ID和详细信息
                    customerId = data.id;
                    customerDetails = {
                        name: data.name,
                        phone: phone,
                        balance: data.balance || 0
                    };

                    // 显示已有客户信息
                    document.getElementById('customerNameDisplay').textContent = data.name;
                    document.getElementById('customerDetails').classList.remove('hidden');
                    document.getElementById('newCustomerForm').classList.add('hidden');

                    // 显示客户余额（包含详细信息）
                    const balance = data.balance || 0;
                    const giftBalance = data.gift_balance || 0;
                    const totalBalance = balance + giftBalance;

                    document.getElementById('customerBalance').textContent = totalBalance.toFixed(2);

                    // 显示详细余额信息
                    const balanceDetails = document.getElementById('balanceDetails');
                    if (balanceDetails) {
                        balanceDetails.innerHTML = generateBalanceDetailsHTML(balance, giftBalance, data);
                    }

                    // 优化：自动同步姓名到新客户表单（以备后续流程使用）
                    document.getElementById('customerName').value = data.name;

                    // 更新摘要页面的客户名称
                    document.getElementById('summaryName').textContent = data.name;
                } else {
                    // 重置客户ID和详细信息
                    customerId = null;
                    customerDetails = {
                        name: '',
                        phone: '',
                        balance: 0
                    };

                    // 显示新客户表单
                    document.getElementById('customerDetails').classList.add('hidden');
                    document.getElementById('newCustomerForm').classList.remove('hidden');

                    // 清空已有值
                    document.getElementById('customerName').value = '';
                    document.getElementById('summaryName').textContent = '';
                }
            } catch (error) {
                console.error('查询失败: ' + error.message);
                // 发生错误时也重置客户信息
                customerId = null;
                customerDetails = {
                    name: '',
                    phone: '',
                    balance: 0
                };
            }
        }

        // 设置手机号输入延时搜索
        let searchTimeout = null;
        document.getElementById('customerPhone').addEventListener('input', function() {
            const phone = this.value;

            // 清除之前的定时器
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // 设置新的定时器，300毫秒后执行搜索（防止频繁请求）
            searchTimeout = setTimeout(() => {
                searchCustomerByPhone(phone);
            }, 300);
        });

        // 保留点击查询按钮功能
        document.getElementById('searchCustomer').addEventListener('click', async function() {
            const phone = document.getElementById('customerPhone').value;
            if (!phone) {
                alert('请输入手机号');
                return;
            }

            await searchCustomerByPhone(phone);
        });

        // 直接充值按钮
        document.getElementById('directRecharge').addEventListener('click', function() {
            const phone = document.getElementById('customerPhone').value;
            if (!phone) {
                alert('请输入手机号');
                return;
            }

            // 打开充值弹窗
            const modal = document.getElementById('rechargeModal');
            if (modal) {
                modal.style.display = 'block';

                // 查询用户状态
                fetch(`/search_customer?phone=${phone}`)
                    .then(response => response.json())
                    .then(data => {
                        const statusElem = document.getElementById('rechargeUserStatus');
                        if (data.found) {
                            // 计算总余额
                            const balance = data.balance || 0;
                            const giftBalance = data.gift_balance || 0;
                            const totalBalance = balance + giftBalance;

                            // 显示用户信息
                            statusElem.innerHTML = `为用户 <strong>${data.name}</strong> 充值，当前总余额: ¥${totalBalance.toFixed(2)}`;
                            statusElem.style.color = '#28a745';

                            // 优化：自动同步姓名到新客户表单（以备后续流程使用）
                            document.getElementById('customerName').value = data.name;

                            // 同步显示用户信息到主界面
                            document.getElementById('customerNameDisplay').textContent = data.name;
                            document.getElementById('customerBalance').textContent = totalBalance.toFixed(2);

                            // 显示详细余额信息
                            const balanceDetails = document.getElementById('balanceDetails');
                            if (balanceDetails) {
                                balanceDetails.innerHTML = generateBalanceDetailsHTML(balance, giftBalance, data);
                            }

                            document.getElementById('customerDetails').classList.remove('hidden');
                            document.getElementById('newCustomerForm').classList.add('hidden');

                            // 更新摘要页面的客户名称
                            document.getElementById('summaryName').textContent = data.name;
                        } else {
                            // 显示新用户提示
                            statusElem.innerHTML = `将为新用户 <strong>${phone}</strong> 创建账户并充值`;
                            statusElem.style.color = '#007BFF';

                            // 清空表单，准备填写新用户信息
                            document.getElementById('customerName').value = '';
                            document.getElementById('customerDetails').classList.add('hidden');
                            document.getElementById('newCustomerForm').classList.remove('hidden');
                            document.getElementById('summaryName').textContent = '';
                        }
                    })
                    .catch(error => {
                        document.getElementById('rechargeUserStatus').innerHTML = '查询用户信息失败';
                        document.getElementById('rechargeUserStatus').style.color = '#dc3545';
                    });

                // 重置选择状态
                document.querySelectorAll('.recharge-amount-option').forEach(option => {
                    option.classList.remove('selected');
                });
                document.querySelectorAll('.recharge-payment-method').forEach(method => {
                    method.classList.remove('selected');
                });

                const customAmountInput = document.getElementById('customRechargeAmount');
                if (customAmountInput) {
                    customAmountInput.value = '';
                }
            } else {
                console.error('未找到充值弹窗(#rechargeModal)');
            }
        });

        // 处理余额支付相关逻辑
        document.getElementById('paymentMethod').addEventListener('change', function() {
            const balanceInfo = document.getElementById('balancePaymentInfo');
            const balanceStatus = document.getElementById('balanceStatus');
            const totalAmount = parseFloat(document.getElementById('totalAmount').textContent);
            const customerBalance = parseFloat(document.getElementById('customerBalance').textContent);

            if (this.value === '余额') {
                // 显示余额支付信息
                balanceInfo.style.display = 'flex';
                document.getElementById('summaryBalance').textContent = customerBalance.toFixed(2);

                // 检查余额是否足够
                if (customerBalance >= totalAmount) {
                    balanceStatus.textContent = '余额充足';
                    balanceStatus.style.color = '#28a745';
                } else {
                    balanceStatus.textContent = '余额不足，请先充值';
                    balanceStatus.style.color = '#dc3545';
                }
            } else {
                // 隐藏余额支付信息
                balanceInfo.style.display = 'none';
            }
        });



        // 这些函数已移至共享的 print-functions.js 文件中

        // 点击弹窗外部关闭
        window.addEventListener('click', function(e) {
            const receiptModal = document.getElementById('receiptModal');
            const labelModal = document.getElementById('labelModal');
            if (receiptModal && e.target == receiptModal) {
                receiptModal.style.display = 'none';
            }
            if (labelModal && e.target == labelModal) {
                labelModal.style.display = 'none';
            }
        });

        // 初始化客户类型选择器
        function initializeCustomerTypeSelector() {
            const options = document.querySelectorAll('.customer-type-option');
            const phoneFormGroup = document.querySelector('.form-group:has(#customerPhone)');
            const mallSelector = document.querySelector('.mall-customer-selector');

            options.forEach(option => {
                option.addEventListener('click', function() {
                    // 移除所有active类
                    options.forEach(opt => opt.classList.remove('active'));

                    // 添加当前选项的active类
                    this.classList.add('active');

                    // 根据选择显示/隐藏表单
                    const type = this.getAttribute('data-type');
                    if (type === 'normal') {
                        // 普通客户设置
                        phoneFormGroup.style.display = 'block';
                        document.getElementById('customerDetails').style.display = '';
                        document.getElementById('newCustomerForm').style.display = '';
                        mallSelector.classList.remove('active');
                        // 设置为普通客户
                        isMallCustomer = false;
                    } else if (type === 'mall') {
                        // 商场客户设置
                        phoneFormGroup.style.display = 'none';
                        document.getElementById('customerDetails').style.display = 'none';
                        document.getElementById('newCustomerForm').style.display = 'none';
                        mallSelector.classList.add('active');
                        // 设置为商场客户
                        isMallCustomer = true;
                        // 重置普通客户ID和详细信息
                        customerId = null;
                        customerDetails = {
                            name: '',
                            phone: '',
                            balance: 0
                        };
                    }
                });
            });
        }

        function showLoading(isLoading) {
            const loader = document.getElementById('loadingOverlay');
            if (loader) {
                loader.style.display = isLoading ? 'flex' : 'none';
            }
        }

        // 初始化客户类型选择器
        initializeCustomerTypeSelector();

        // 初始化商场客户选择器
        function initializeMallCustomerSelector() {
            const searchInput = document.getElementById('mallCustomerName');
            const phoneInput = document.getElementById('mallCustomerPhone');

            if (!searchInput) return; // 防止元素不存在导致错误

            // 创建下拉列表
            const dropdown = document.createElement('div');
            dropdown.classList.add('mall-customer-dropdown');
            dropdown.style.display = 'none';
            dropdown.style.position = 'absolute';
            dropdown.style.width = '100%';
            dropdown.style.maxHeight = '200px';
            dropdown.style.overflowY = 'auto';
            dropdown.style.background = '#fff';
            dropdown.style.border = '1px solid #ddd';
            dropdown.style.borderRadius = '4px';
            dropdown.style.zIndex = '100';
            dropdown.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
            searchInput.parentNode.style.position = 'relative';
            searchInput.parentNode.appendChild(dropdown);

            // 添加输入事件
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.trim();
                if (searchTerm.length < 2) {
                    dropdown.style.display = 'none';
                    return;
                }

                // 搜索商场客户
                fetch(`/api/mall_customers?search=${searchTerm}&page=1&per_page=5`)
                    .then(response => response.json())
                    .then(data => {
                        dropdown.innerHTML = '';
                        if (data.customers && data.customers.length > 0) {
                            data.customers.forEach(customer => {
                                const item = document.createElement('div');
                                item.style.padding = '8px 12px';
                                item.style.cursor = 'pointer';
                                item.style.borderBottom = '1px solid #eee';
                                item.style.transition = 'background 0.2s';
                                item.innerHTML = `
                                    <div><strong>${customer.mall_name}</strong></div>
                                    <div style="font-size: 0.85rem; color: #666;">
                                        联系人: ${customer.contact_name || '-'} | 电话: ${customer.phone || '-'}
                                    </div>
                                `;

                                item.addEventListener('mouseover', function() {
                                    this.style.background = '#f5f5f5';
                                });

                                item.addEventListener('mouseout', function() {
                                    this.style.background = '#fff';
                                });

                                item.addEventListener('click', function() {
                                    searchInput.value = customer.mall_name;
                                    phoneInput.value = customer.phone || '';
                                    dropdown.style.display = 'none';

                                    // 存储客户ID以便后续使用
                                    searchInput.dataset.mallCustomerId = customer.id;

                                    // 存储当前商场客户信息
                                    window.currentMallCustomer = customer;

                                    // 获取该商场客户的所有折扣
                                    fetch(`/api/mall_customers/${customer.id}/discounts`)
                                        .then(response => response.json())
                                        .then(discountData => {
                                            // 存储折扣信息
                                            window.mallCustomerDiscounts = discountData.discounts || [];
                                            console.log('加载商场客户折扣:', window.mallCustomerDiscounts);

                                            // 重新计算所有已添加衣物的折扣
                                            applyDiscountsToExistingItems();
                                        })
                                        .catch(error => console.error('获取折扣信息失败:', error));
                                });

                                dropdown.appendChild(item);
                            });
                            dropdown.style.display = 'block';
                        } else {
                            dropdown.innerHTML = '<div style="padding: 8px 12px; color: #999;">没有找到匹配的商场品牌</div>';
                            dropdown.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('搜索商场品牌失败:', error);
                        dropdown.innerHTML = '<div style="padding: 8px 12px; color: #999;">搜索失败，请重试</div>';
                        dropdown.style.display = 'block';
                    });
            });

            // 点击外部区域关闭下拉列表
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });
        }

        // 初始化商场客户选择器
        initializeMallCustomerSelector();

        // 初始化充值事件
        initializeRechargeEvents();
    </script>

    <!-- 摄像头弹窗 -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-modal">&times;</span>
            <h3>拍摄衣物照片</h3>
            <video id="modalCameraFeed" class="camera-feed" autoplay></video>
            <canvas id="photoCanvas" style="display:none;"></canvas>
            <div class="camera-controls">
                <button id="takePhotoBtn" type="button">拍照</button>
                <button id="cancelBtn" type="button">取消</button>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="loading-overlay" style="display: none;">
        <div class="spinner"></div>
        <p id="loadingText">处理中...</p>
    </div>

    <!-- 充值弹窗 -->
    <div id="rechargeModal" class="recharge-modal">
        <div class="recharge-modal-content">
            <span class="close-modal" id="closeRechargeModal">&times;</span>
            <h3>账户充值</h3>
            <p id="rechargeUserStatus"></p>

            <div class="form-group">
                <label>选择充值金额</label>
                <div class="recharge-amount-options">
                    <div class="recharge-amount-option" data-amount="500">¥500</div>
                    <div class="recharge-amount-option" data-amount="1000">¥1000</div>
                    <div class="recharge-amount-option" data-amount="3000">¥3000</div>
                    <div class="recharge-amount-option" data-amount="10000">¥10000</div>
                </div>
                <div class="form-group" style="margin-top: 10px;">
                    <label>自定义金额</label>
                    <input type="number" id="customRechargeAmount" min="1" step="1" placeholder="输入充值金额">
                </div>
                <div class="form-group" id="giftRulesContainer" style="display: none; margin-top: 10px;">
                    <label>选择赠送规则（可选）</label>
                    <div id="giftRulesOptions">
                        <!-- 赠送规则选项将在这里动态加载 -->
                    </div>
                    <div style="margin-top: 5px; font-size: 0.9em; color: #666;">
                        不选择规则时将自动使用最优赠送规则
                    </div>
                </div>
                <div class="form-group" style="margin-top: 10px;">
                    <label>预计赠送金额</label>
                    <div id="giftAmountDisplay" style="color: #28a745; font-weight: bold; font-size: 1.1em;">¥0.00</div>
                </div>
            </div>

            <div class="form-group">
                <label>选择支付方式</label>
                <div class="recharge-payment-methods">
                    <div class="recharge-payment-method" data-method="扫银联码">扫银联码</div>
                    <div class="recharge-payment-method" data-method="商场POS">商场POS</div>
                </div>
            </div>

            <div class="form-group">
                <button type="button" id="confirmRecharge" style="width: 100%; padding: 12px; margin-top: 15px;">确认充值</button>
            </div>
        </div>
    </div>
    <!-- 修复充值弹窗功能 -->
    <script>
        // 计算赠送金额选项的函数（用于主页面充值弹窗）
        function calculateGiftAmountForModal(amount) {
            if (!amount || amount <= 0) {
                document.getElementById('giftRulesContainer').style.display = 'none';
                document.getElementById('giftAmountDisplay').textContent = '¥0.00';
                return;
            }

            fetch('/api/calculate_gift_options', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ amount: amount })
            })
            .then(response => response.json())
            .then(data => {
                if (data.options && data.options.length > 0) {
                    // 显示规则选择区域
                    document.getElementById('giftRulesContainer').style.display = 'block';

                    // 渲染规则选项
                    let html = '';

                    // 添加"不使用规则"选项
                    html += `
                        <div style="margin: 5px 0;">
                            <label style="display: flex; align-items: center; cursor: pointer; text-align: left;">
                                <input type="radio" name="giftRule" value=""
                                       data-gift-amount="0" style="margin-right: 8px;">
                                <span style="font-size: 0.9em; text-align: left;">不使用赠送规则 (赠送¥0.00)</span>
                            </label>
                        </div>
                    `;

                    data.options.forEach((option, index) => {
                        const isRecommended = data.recommended && option.rule_id === data.recommended.rule_id;
                        const checkedAttr = index === 0 ? 'checked' : ''; // 默认选择第一个（最优）
                        const recommendedBadge = isRecommended ? '<span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; margin-left: 5px;">推荐</span>' : '';

                        html += `
                            <div style="margin: 5px 0;">
                                <label style="display: flex; align-items: center; cursor: pointer; text-align: left;">
                                    <input type="radio" name="giftRule" value="${option.rule_id}"
                                           data-gift-amount="${option.gift_amount}" ${checkedAttr}
                                           style="margin-right: 8px;">
                                    <span style="font-size: 0.9em; text-align: left;">${option.description} (赠送¥${option.gift_amount.toFixed(2)})${recommendedBadge}</span>
                                </label>
                            </div>
                        `;
                    });

                    document.getElementById('giftRulesOptions').innerHTML = html;

                    // 绑定选择事件
                    document.querySelectorAll('input[name="giftRule"]').forEach(radio => {
                        radio.addEventListener('change', function() {
                            const giftAmount = parseFloat(this.getAttribute('data-gift-amount'));
                            document.getElementById('giftAmountDisplay').textContent = `¥${giftAmount.toFixed(2)}`;
                        });
                    });

                    // 设置默认赠送金额
                    const defaultGiftAmount = data.options[0].gift_amount;
                    document.getElementById('giftAmountDisplay').textContent = `¥${defaultGiftAmount.toFixed(2)}`;
                } else {
                    document.getElementById('giftRulesContainer').style.display = 'none';
                    document.getElementById('giftAmountDisplay').textContent = '¥0.00';
                }
            })
            .catch(error => {
                console.error('计算赠送选项失败:', error);
                document.getElementById('giftRulesContainer').style.display = 'none';
                document.getElementById('giftAmountDisplay').textContent = '¥0.00';
            });
        }

        // 修复方案
        function repairRechargeModal() {
            console.log('开始修复充值弹窗...');
            // 1. 修复关闭按钮
            const closeRechargeModalBtn = document.getElementById('closeRechargeModal');
            if (closeRechargeModalBtn) {
                // 移除所有现有事件监听器
                closeRechargeModalBtn.replaceWith(closeRechargeModalBtn.cloneNode(true));

                // 重新获取并添加事件监听器
                document.getElementById('closeRechargeModal').addEventListener('click', function() {
                    console.log('关闭按钮被点击');
                    document.getElementById('rechargeModal').style.display = 'none';
                });
            }

            // 2. 统一全局点击事件处理
            window.removeEventListener('click', window.modalCloseHandler);
            window.modalCloseHandler = function(e) {
                const modals = [
                    { id: 'rechargeModal', element: document.getElementById('rechargeModal') },
                    { id: 'receiptModal', element: document.getElementById('receiptModal') },
                    { id: 'labelModal', element: document.getElementById('labelModal') },
                    // 添加其他模态框
                ];

                modals.forEach(modal => {
                    if (modal.element && e.target == modal.element) {
                        modal.element.style.display = 'none';
                        console.log(`${modal.id} 已通过点击外部区域关闭`);
                    }
                });
            };
            window.addEventListener('click', window.modalCloseHandler);

            // 3. 确保模态框内容不会触发关闭
            const modalContent = document.querySelector('.recharge-modal-content');
            if (modalContent) {
                modalContent.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }

            // 4. 修复充值金额和支付方式选择
            document.querySelectorAll('.recharge-amount-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.recharge-amount-option').forEach(opt => {
                        opt.classList.remove('selected');
                    });
                    this.classList.add('selected');

                    // 清空自定义金额输入
                    const customAmountInput = document.getElementById('customRechargeAmount');
                    if (customAmountInput) {
                        customAmountInput.value = '';
                    }

                    // 计算赠送金额
                    const amount = parseFloat(this.dataset.amount);
                    calculateGiftAmountForModal(amount);
                });
            });

            // 添加自定义金额输入事件处理
            const customAmountInput = document.getElementById('customRechargeAmount');
            if (customAmountInput) {
                customAmountInput.addEventListener('input', function() {
                    if (this.value) {
                        // 清除预设金额的选中状态
                        document.querySelectorAll('.recharge-amount-option').forEach(option => {
                            option.classList.remove('selected');
                        });

                        // 计算赠送金额
                        const amount = parseFloat(this.value);
                        calculateGiftAmountForModal(amount);
                    } else {
                        // 清空时重置赠送金额显示
                        document.getElementById('giftAmountDisplay').textContent = '¥0.00';
                    }
                });
            }

            document.querySelectorAll('.recharge-payment-method').forEach(method => {
                method.addEventListener('click', function() {
                    document.querySelectorAll('.recharge-payment-method').forEach(m => {
                        m.classList.remove('selected');
                    });
                    this.classList.add('selected');
                });
            });

            // 5. 确保确认充值按钮正常工作
            const confirmRechargeBtn = document.getElementById('confirmRecharge');
            if (confirmRechargeBtn) {
                confirmRechargeBtn.replaceWith(confirmRechargeBtn.cloneNode(true));
                document.getElementById('confirmRecharge').addEventListener('click', function() {
                    // 获取选择的金额
                    let selectedAmount = 0;
                    const selectedAmountOption = document.querySelector('.recharge-amount-option.selected');
                    if (selectedAmountOption) {
                        selectedAmount = parseFloat(selectedAmountOption.dataset.amount);
                    } else {
                        // 检查自定义金额
                        const customAmount = document.getElementById('customRechargeAmount').value;
                        if (customAmount) {
                            selectedAmount = parseFloat(customAmount);
                        }
                    }

                    // 获取选择的支付方式
                    const selectedPaymentMethod = document.querySelector('.recharge-payment-method.selected');
                    let paymentMethod = '';
                    if (selectedPaymentMethod) {
                        paymentMethod = selectedPaymentMethod.dataset.method;
                    }

                    // 验证输入
                    if (!selectedAmount || selectedAmount <= 0) {
                        alert('请选择或输入有效的充值金额');
                        return;
                    }

                    if (!paymentMethod) {
                        alert('请选择支付方式');
                        return;
                    }

                    // 执行充值
                    const phone = document.getElementById('customerPhone').value;
                    const customerName = document.getElementById('customerName').value || phone;

                    console.log(`执行充值: ${customerName}, ${phone}, ${selectedAmount}, ${paymentMethod}`);

                    // 准备充值数据
                    const rechargeData = {
                        phone: phone,
                        amount: selectedAmount,
                        paymentMethod: paymentMethod,
                        customer_name: customerName
                    };

                    // 获取选择的规则
                    const selectedRule = document.querySelector('input[name="giftRule"]:checked');
                    if (selectedRule) {
                        if (selectedRule.value === '') {
                            // 用户选择了"不使用规则"
                            rechargeData.no_gift_rule = true;
                        } else {
                            // 用户选择了特定规则
                            rechargeData.selected_rule_id = parseInt(selectedRule.value);
                        }
                    }

                    fetch('/recharge_account', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(rechargeData)
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('充值结果:', data);
                        if (data.success) {
                            // 更新界面显示
                            document.getElementById('customerBalance').textContent = data.newBalance.toFixed(2);
                            document.getElementById('rechargeModal').style.display = 'none';

                            // 构建充值成功消息
                            let successMessage = '';
                            const giftMessage = data.giftAmount > 0 ? `，赠送金额: ¥${data.giftAmount.toFixed(2)}` : '';

                            if (data.isNewCustomer) {
                                successMessage = `已为新用户 ${data.customer_name} 创建账户并充值成功！新余额: ¥${data.newBalance.toFixed(2)}${giftMessage}`;
                                // 更新客户信息显示
                                document.getElementById('customerNameDisplay').textContent = data.customer_name;
                                document.getElementById('customerDetails').classList.remove('hidden');
                                document.getElementById('newCustomerForm').classList.add('hidden');
                            } else {
                                successMessage = `充值成功！新余额: ¥${data.newBalance.toFixed(2)}${giftMessage}`;
                            }

                            // 准备充值小票数据
                            const rechargeReceiptData = {
                                customer_name: data.customer_name,
                                phone: rechargeData.phone,
                                amount: rechargeData.amount,
                                giftAmount: data.giftAmount || 0,
                                paymentMethod: rechargeData.paymentMethod,
                                newBalance: data.newBalance,
                                isNewCustomer: data.isNewCustomer || false,
                                operator: data.operator || '{{ session.staff_name or "系统" }}'
                            };

                            // 自动调用充值小票打印（默认使用 Lodop）
                            if (typeof window.autoPrintRechargeReceipt === 'function') {
                                window.autoPrintRechargeReceipt(rechargeReceiptData);
                            }
                            // 弹出成功提示
                            alert(successMessage);
                        } else {
                            alert(`充值失败: ${data.error || '未知错误'}`);
                        }
                    })
                    .catch(error => {
                        console.error('充值请求出错:', error);
                        alert('网络错误，请重试');
                    });
                });
            }

            console.log('充值弹窗修复完成');
        }

        // 页面加载后执行修复
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(repairRechargeModal, 500); // 延迟执行以确保其他脚本已加载
        });

        // 立即尝试修复一次（适用于已加载的页面）
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            setTimeout(repairRechargeModal, 100);
        }

        // 折扣相关函数
        function applyDiscountToItem(itemIndex) {
            if (!window.currentMallCustomer || !clothingItemsData[itemIndex]) {
                return;
            }

            const itemData = clothingItemsData[itemIndex];
            const productName = itemData.name;

            if (!productName) {
                return;
            }

            // 获取产品类型（可以从产品名称推断或使用默认值）
            const productType = getProductTypeFromName(productName);

            // 计算当前总价（基于选中的服务）
            let currentTotalPrice = 0;
            if (itemData.serviceTypes.includes('洗衣')) {
                currentTotalPrice += itemData.specialRequirements.fineWashPrice || 0;
                currentTotalPrice += itemData.specialRequirements.luxuryWashPrice || 0;
            }
            if (itemData.serviceTypes.includes('织补')) {
                currentTotalPrice += itemData.specialRequirements.darnPrice || 0;
            }
            if (itemData.serviceTypes.includes('改衣')) {
                currentTotalPrice += itemData.specialRequirements.alterPrice || 0;
            }
            if (itemData.serviceTypes.includes('其他')) {
                currentTotalPrice += itemData.specialRequirements.otherPrice || 0;
            }

            // 应用折扣
            const discountResult = applyMallCustomerDiscount(productName, productType, currentTotalPrice);

            // 获取DOM元素
            const itemElement = document.querySelector(`[data-item-index="${itemIndex}"]`);
            const priceInput = itemElement.querySelector('.clothing-price');

            if (discountResult.discountAmount > 0) {
                // 更新价格显示
                updatePriceInputWithDiscount(priceInput, discountResult);

                // 更新数据
                itemData.price = discountResult.discountedPrice;
                itemData.originalPrice = discountResult.originalPrice;
                itemData.discountRate = discountResult.discountRate;

                // 更新价格输入框的值
                priceInput.value = discountResult.discountedPrice.toFixed(2);

                console.log(`应用折扣到衣物 ${productName}: 原价¥${discountResult.originalPrice} → 折后¥${discountResult.discountedPrice.toFixed(2)}`);
            } else {
                // 没有折扣时，清除折扣信息显示
                const discountDisplay = priceInput.parentElement.querySelector('.discount-info');
                if (discountDisplay) {
                    discountDisplay.remove();
                }

                // 更新价格输入框的值
                priceInput.value = currentTotalPrice.toFixed(2);
                itemData.price = currentTotalPrice;
                itemData.originalPrice = currentTotalPrice;
                itemData.discountRate = 1.0;
            }

            // 更新订单总计折扣显示
            updateOrderTotalDiscountDisplay();
        }

        function applyDiscountsToExistingItems() {
            clothingItemsData.forEach((item, index) => {
                if (item && item.name) {
                    applyDiscountToItem(index);
                }
            });

            // 更新订单总计折扣显示
            updateOrderTotalDiscountDisplay();
        }

        function getProductTypeFromName(productName) {
            // 简单的产品类型映射，可以根据实际需要扩展
            const typeMapping = {
                '衬衫': '上衣',
                'T恤': '上衣',
                '西装外套': '外套',
                '大衣': '外套',
                '西裤': '裤装',
                '牛仔裤': '裤装',
                '裙子': '裙装',
                '连衣裙': '裙装',
                '羽绒服': '外套'
            };

            return typeMapping[productName] || '其他';
        }
    </script>
</body>
</html>