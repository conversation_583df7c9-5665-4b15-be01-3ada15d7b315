# 充值退充和小票补打印功能设计文档

## 概述

本设计文档详细描述了充值退充功能和小票补打印功能的技术实现方案。该功能将扩展现有的充值系统，增加退充管理、权限控制、小票补打印等核心功能，确保系统的完整性和用户体验。

## 架构设计

### 系统架构图

```mermaid
graph TB
    A[前端界面] --> B[API网关]
    B --> C[退充服务]
    B --> D[打印服务]
    B --> E[权限服务]
    
    C --> F[数据库]
    D --> G[Lodop打印]
    D --> H[网页打印]
    E --> F
    
    F --> I[充值记录表]
    F --> J[退充记录表]
    F --> K[操作日志表]
    F --> L[员工权限表]
```

### 核心模块

1. **退充管理模块**：处理退充业务逻辑
2. **权限管理模块**：控制退充操作权限
3. **打印服务模块**：处理小票打印和补打印
4. **日志审计模块**：记录操作日志
5. **余额管理模块**：处理客户余额变动

## 组件和接口设计

### 1. 数据模型设计

#### 退充记录表 (RechargeRefund)
```python
class RechargeRefund(db.Model):
    __tablename__ = 'recharge_refund'
    
    id = db.Column(db.Integer, primary_key=True)
    original_recharge_id = db.Column(db.Integer, db.ForeignKey('recharge_record.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    refund_amount = db.Column(db.Float, nullable=False)  # 退充金额
    refund_gift_amount = db.Column(db.Float, default=0.0)  # 扣除的赠送金额
    refund_reason = db.Column(db.String(500))  # 退充原因
    operator = db.Column(db.String(100), nullable=False)  # 操作员
    status = db.Column(db.String(20), default='completed')  # 状态：completed, cancelled
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    approved_by = db.Column(db.String(100))  # 审批人（大额退充）
    
    # 关联关系
    original_recharge = db.relationship('RechargeRecord', backref='refunds')
    customer = db.relationship('Customer', backref='recharge_refunds')
```

#### 小票补打印记录表 (ReceiptReprint)
```python
class ReceiptReprint(db.Model):
    __tablename__ = 'receipt_reprint'
    
    id = db.Column(db.Integer, primary_key=True)
    record_type = db.Column(db.String(20), nullable=False)  # 'recharge' or 'refund'
    record_id = db.Column(db.Integer, nullable=False)  # 对应记录ID
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    operator = db.Column(db.String(100), nullable=False)
    reprint_reason = db.Column(db.String(255))  # 补打印原因
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    
    customer = db.relationship('Customer', backref='receipt_reprints')
```

#### 员工权限扩展
```python
# 在Staff模型中添加字段
class Staff(db.Model):
    # ... 现有字段 ...
    can_refund_recharge = db.Column(db.Boolean, default=False)  # 退充权限
    max_refund_amount = db.Column(db.Float, default=0.0)  # 最大退充金额限制
```

### 2. API接口设计

#### 退充相关接口

```python
# 获取可退充的充值记录
GET /api/recharge/{recharge_id}/refundable
Response: {
    "success": true,
    "refundable_amount": 100.0,
    "gift_amount_to_deduct": 10.0,
    "current_balance": 500.0,
    "usage_details": {...}
}

# 执行退充操作
POST /api/recharge/{recharge_id}/refund
Request: {
    "refund_amount": 100.0,
    "refund_reason": "客户要求退款",
    "print_receipt": true
}
Response: {
    "success": true,
    "refund_id": 123,
    "new_balance": 400.0,
    "receipt_printed": true
}

# 获取退充记录列表
GET /api/recharge/refunds
Parameters: start_date, end_date, customer_phone, operator, page, per_page
Response: {
    "success": true,
    "refunds": [...],
    "pagination": {...}
}

# 导出退充记录
GET /api/recharge/refunds/export
Parameters: start_date, end_date, format=csv
Response: CSV文件下载
```

#### 补打印相关接口

```python
# 补打印充值小票
POST /api/recharge/{recharge_id}/reprint
Request: {
    "reprint_reason": "客户要求补打印"
}
Response: {
    "success": true,
    "reprint_id": 456
}

# 补打印退充小票
POST /api/recharge/refund/{refund_id}/reprint
Request: {
    "reprint_reason": "客户要求补打印"
}
Response: {
    "success": true,
    "reprint_id": 457
}

# 获取补打印记录
GET /api/receipts/reprints
Parameters: record_type, start_date, end_date, page, per_page
Response: {
    "success": true,
    "reprints": [...],
    "pagination": {...}
}
```

### 3. 前端组件设计

#### 退充操作组件
```javascript
// 退充确认对话框组件
class RefundConfirmDialog {
    constructor(rechargeRecord) {
        this.rechargeRecord = rechargeRecord;
        this.refundableAmount = 0;
        this.giftAmountToDeduct = 0;
    }
    
    async show() {
        // 获取可退充信息
        const refundInfo = await this.getRefundableInfo();
        // 显示确认对话框
        this.renderDialog(refundInfo);
    }
    
    async executeRefund(refundAmount, reason) {
        // 执行退充操作
        // 处理结果和错误
        // 自动打印小票
    }
}
```

#### 补打印组件
```javascript
// 小票补打印组件
class ReceiptReprintManager {
    async reprintRechargeReceipt(rechargeId, reason) {
        // 补打印充值小票
        const response = await fetch(`/api/recharge/${rechargeId}/reprint`, {
            method: 'POST',
            body: JSON.stringify({ reprint_reason: reason })
        });
        
        if (response.ok) {
            // 调用打印函数
            await this.printRechargeReceipt(rechargeId, true); // true表示补打印
        }
    }
    
    async reprintRefundReceipt(refundId, reason) {
        // 补打印退充小票
        // 类似实现
    }
}
```

## 数据模型详细设计

### 退充业务逻辑

#### 退充金额计算规则
1. **基础退充金额**：不超过原充值金额
2. **赠送金额处理**：按比例扣除赠送金额
3. **余额检查**：确保客户当前余额足够扣除
4. **使用限制**：如果充值金额已被使用，计算可退充金额

#### 退充流程
```mermaid
sequenceDiagram
    participant U as 营业员
    participant F as 前端
    participant A as API
    participant D as 数据库
    participant P as 打印服务
    
    U->>F: 点击退充按钮
    F->>A: 获取可退充信息
    A->>D: 查询充值记录和余额
    D-->>A: 返回可退充金额
    A-->>F: 返回退充信息
    F->>U: 显示退充确认对话框
    U->>F: 确认退充
    F->>A: 提交退充请求
    A->>D: 开始事务
    A->>D: 创建退充记录
    A->>D: 更新客户余额
    A->>D: 提交事务
    A->>P: 打印退充小票
    A-->>F: 返回成功结果
    F-->>U: 显示成功提示
```

### 小票打印设计

#### 退充小票格式
```
================================
        Soulweave改衣坊
        充值退充小票
================================
客户姓名: 张三
手机号码: 138****5678
退充时间: 2025-01-15 14:30:25
操作员: 李四
--------------------------------
原充值信息:
充值时间: 2025-01-10 10:20:15
充值金额: ¥200.00
赠送金额: ¥20.00
--------------------------------
退充详情:
退充金额: ¥100.00
扣除赠送: ¥10.00
实际退充: ¥90.00
退充原因: 客户要求退款
--------------------------------
余额变动:
退充前余额: ¥180.00
退充后余额: ¥90.00
================================
感谢您的理解，欢迎再次光临！
```

#### 补打印标识
补打印的小票需要添加特殊标识：
```
================================
        Soulweave改衣坊
      账户充值小票 [补打印]
================================
补打印时间: 2025-01-15 15:45:30
补打印操作员: 王五
原小票时间: 2025-01-10 10:20:15
--------------------------------
[原小票内容]
================================
```

## 错误处理设计

### 错误类型定义
```python
class RefundError(Exception):
    """退充相关错误"""
    pass

class InsufficientBalanceError(RefundError):
    """余额不足错误"""
    pass

class RefundPermissionError(RefundError):
    """退充权限错误"""
    pass

class RefundAmountExceedsLimitError(RefundError):
    """退充金额超限错误"""
    pass
```

### 错误处理策略
1. **数据库事务回滚**：操作失败时自动回滚
2. **用户友好提示**：将技术错误转换为用户可理解的提示
3. **日志记录**：记录所有错误信息用于调试
4. **重试机制**：对于网络或临时性错误提供重试选项

## 测试策略

### 单元测试
- 退充金额计算逻辑测试
- 权限验证逻辑测试
- 余额更新逻辑测试
- 小票内容生成测试

### 集成测试
- 退充完整流程测试
- 补打印功能测试
- 权限控制集成测试
- 打印服务集成测试

### 边界测试
- 零金额退充测试
- 超额退充测试
- 权限边界测试
- 并发操作测试

## 安全考虑

### 权限控制
1. **角色基础权限**：基于员工角色控制退充权限
2. **金额限制**：设置员工最大退充金额限制
3. **审批流程**：大额退充需要管理员审批
4. **操作日志**：记录所有敏感操作

### 数据安全
1. **事务完整性**：使用数据库事务确保数据一致性
2. **输入验证**：严格验证所有输入参数
3. **SQL注入防护**：使用参数化查询
4. **访问控制**：基于会话验证用户身份

### 审计追踪
1. **操作日志**：记录所有退充和补打印操作
2. **IP地址记录**：记录操作来源IP
3. **时间戳**：精确记录操作时间
4. **数据变更追踪**：记录余额变更前后状态

## 性能优化

### 数据库优化
1. **索引设计**：为查询字段添加适当索引
2. **分页查询**：大数据量查询使用分页
3. **连接池**：使用数据库连接池提高性能
4. **查询优化**：优化复杂查询语句

### 前端优化
1. **异步操作**：使用异步请求避免界面阻塞
2. **缓存策略**：缓存常用数据减少请求
3. **懒加载**：按需加载数据和组件
4. **防抖处理**：防止重复提交操作

## 部署和维护

### 数据库迁移
```sql
-- 创建退充记录表
CREATE TABLE recharge_refund (
    id INT PRIMARY KEY AUTO_INCREMENT,
    original_recharge_id INT NOT NULL,
    customer_id INT NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL,
    refund_gift_amount DECIMAL(10,2) DEFAULT 0.00,
    refund_reason VARCHAR(500),
    operator VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'completed',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    approved_by VARCHAR(100),
    FOREIGN KEY (original_recharge_id) REFERENCES recharge_record(id),
    FOREIGN KEY (customer_id) REFERENCES customer(id)
);

-- 创建补打印记录表
CREATE TABLE receipt_reprint (
    id INT PRIMARY KEY AUTO_INCREMENT,
    record_type VARCHAR(20) NOT NULL,
    record_id INT NOT NULL,
    customer_id INT NOT NULL,
    operator VARCHAR(100) NOT NULL,
    reprint_reason VARCHAR(255),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customer(id)
);

-- 添加员工权限字段
ALTER TABLE staff 
ADD COLUMN can_refund_recharge BOOLEAN DEFAULT FALSE,
ADD COLUMN max_refund_amount DECIMAL(10,2) DEFAULT 0.00;
```

### 配置管理
```python
# 退充相关配置
REFUND_CONFIG = {
    'max_refund_amount_without_approval': 1000.0,  # 无需审批的最大退充金额
    'refund_time_limit_hours': 24,  # 退充时间限制（小时）
    'auto_print_refund_receipt': True,  # 自动打印退充小票
    'require_refund_reason': True,  # 是否必须填写退充原因
}
```

这个设计文档提供了完整的技术实现方案，涵盖了数据模型、API接口、前端组件、错误处理、安全考虑等各个方面，为后续的开发工作提供了详细的指导。