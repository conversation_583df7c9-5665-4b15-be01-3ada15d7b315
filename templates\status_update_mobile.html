<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - 洗衣系统</title>
    <!-- 引入Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/mobile_status.css">
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.2.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .form-container {
            margin-top: 20px;
        }
        .result-container {
            margin-top: 20px;
            display: none;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }
        .status-badge {
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .status-info {
            margin-top: 15px;
        }
        #orderInput {
            font-size: 16px;
            padding: 10px;
        }
        .btn-submit {
            padding: 10px 20px;
            font-size: 16px;
        }
        .btn-clear {
            margin-left: 10px;
        }
        .order-details {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .back-link {
            margin-top: 20px;
            display: block;
        }
        .scan-btn {
            margin-left: 10px;
        }
        .info-badge {
            background-color: #e9f5ff;
            border-left: 3px solid #007bff;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .order-actions {
            text-align: center;
            padding: 15px 0;
            border-top: 1px solid #e9ecef;
        }

        .order-actions .btn {
            min-width: 160px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .order-actions .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
    </style>
    {% if target_status == '出厂' %}
    <!-- Lodop 打印控件脚本 -->
    <script language="javascript" src="http://localhost:8000/CLodopfuncs.js"></script>
    <script language="javascript" src="http://localhost:18000/CLodopfuncs.js"></script>
    <!-- 项目内 Lodop 辅助函数，包含 getLodop 包装 -->
    <script src="/static/js/LodopFuncs.js"></script>
    {% endif %}
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas {{ icon }}"></i> {{ title }}</h1>
            <div>
                <span>操作员: {{ staff_name }}</span>
                <a href="/" class="btn btn-outline-secondary ms-2">返回首页</a>
            </div>
        </div>

        <div class="mobile-status-menu">
            <a href="/status/to_factory?m=1" class="status-link" id="statusMenuToFactory">送洗处理</a>
            <a href="/status/factory_in?m=1" class="status-link" id="statusMenuFactoryIn">入厂处理</a>
            <a href="/status/factory_out?m=1" class="status-link" id="statusMenuFactoryOut">出厂处理</a>
            <a href="/status/on_shelf?m=1" class="status-link" id="statusMenuOnShelf">上架处理</a>
            <a href="/status/self_pickup?m=1" class="status-link" id="statusMenuSelfPickup">自取处理</a>
        </div>

        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> 输入订单号或扫描水洗唛条码可将状态更新为"{{ target_status }}"
        </div>

        <div class="info-badge">
            <i class="fas fa-tag"></i> 支持扫描格式：<br>
            - 订单号：如 "100004"<br>
            - 水洗唛条码：如 "100004-01"（订单号-衣物序号）
        </div>

        <div class="form-container">
            <form id="statusUpdateForm">
                <div class="input-group mb-3">
                    <input type="text" id="orderInput" class="form-control" placeholder="请输入或扫描订单号/水洗唛条码" autofocus>
                    <button class="btn btn-primary btn-submit" type="submit">
                        <i class="fas fa-check"></i> 确认更新
                    </button>
                    <button class="btn btn-outline-secondary scan-btn" type="button">
                        <i class="fas fa-barcode"></i> 扫码
                    </button>
                </div>
            </form>
        </div>

        <div class="result-container" id="resultContainer">
            <div class="alert" id="alertBox" role="alert"></div>
            
            <div class="order-details" id="orderDetails">
                <h4>更新信息</h4>
                <table class="table">
                    <tr>
                        <th width="30%">订单号</th>
                        <td id="orderNumber"></td>
                    </tr>
                    <tr>
                        <th>客户</th>
                        <td id="customerName"></td>
                    </tr>
                    <tr id="clothingInfo" style="display: none;">
                        <th>衣物名称</th>
                        <td id="clothingName"></td>
                    </tr>
                    <tr>
                        <th>原状态</th>
                        <td id="oldStatus"></td>
                    </tr>
                    <tr>
                        <th>新状态</th>
                        <td id="newStatus"><span class="badge bg-success status-badge">{{ target_status }}</span></td>
                    </tr>
                    <tr>
                        <th>更新时间</th>
                        <td id="updateTime"></td>
                    </tr>
                </table>

                <!-- 添加查看/编辑订单详情按钮 -->
                <div class="order-actions mt-3">
                    <button id="viewOrderDetailBtn" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye"></i> 查看/编辑订单详情
                    </button>
                </div>
            </div>
        </div>

        <div class="status-history mt-4">
            <h4>最近处理记录</h4>
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="historyTable">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            {% if target_status == '出厂' %}<th>营业员</th>{% else %}<th>客户</th>{% endif %}
                            <th>衣物</th>
                            <th>原状态</th>
                            <th>更新为</th>
                            <th>处理时间</th>
                        </tr>
                    </thead>
                    <tbody id="historyBody">
                        <!-- 历史记录将通过JavaScript动态添加 -->
                    </tbody>
                </table>
            </div>
            {% if target_status == '出厂' %}
            <div class="text-end mt-2">
                <button id="printOutboundBtn" class="btn btn-success">
                    <i class="fas fa-print"></i> 打印出库单
                </button>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 引入JavaScript库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后执行
        $(document).ready(function() {
            // 获取历史记录
            fetchHistory();

            // 表单提交处理
            $('#statusUpdateForm').on('submit', function(e) {
                e.preventDefault();
                const barcodeInput = $('#orderInput').val().trim();
                
                if (!barcodeInput) {
                    showAlert('请输入订单号或水洗唛条码', 'danger');
                    return;
                }
                
                updateStatus(barcodeInput);
            });

            // 扫码按钮处理
            $('.scan-btn').on('click', function() {
                $('#orderInput').focus();
                // 这里可以添加调用扫码设备的代码
                // 由于浏览器限制，真实的扫码功能需要通过特定硬件或原生App实现
            });

            // 清除按钮处理
            $('.btn-clear').on('click', function() {
                $('#orderInput').val('').focus();
                hideResult();
            });
        });

        // 解析条码
        function parseBarcode(barcode) {
            // 检查是否是水洗唛条码格式（如"100004-01"）
            const regex = /^(\d+)-(\d+)$/;
            const match = barcode.match(regex);
            
            if (match) {
                return {
                    isClothingBarcode: true,
                    orderNumber: match[1],
                    clothingIndex: parseInt(match[2])
                };
            } else {
                return {
                    isClothingBarcode: false,
                    orderNumber: barcode
                };
            }
        }

        // 更新状态
        function updateStatus(barcode) {
            // 显示加载状态
            showAlert('处理中...', 'info');
            
            // 解析条码
            const barcodeInfo = parseBarcode(barcode);
            
            // 构建请求参数
            let requestData = {
                target_status: '{{ target_status }}'
            };
            
            // 根据条码类型选择API和参数
            let apiUrl = '';
            if (barcodeInfo.isClothingBarcode) {
                apiUrl = '/api/update_clothing_status';
                requestData.order_number = barcodeInfo.orderNumber;
                requestData.clothing_index = barcodeInfo.clothingIndex;
            } else {
                apiUrl = '{{ update_endpoint }}';
                requestData.order_number = barcodeInfo.orderNumber;
            }
            
            $.ajax({
                url: apiUrl,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(response) {
                    if (response.success) {
                        showResult(response);
                        $('#orderInput').val('').focus();
                        fetchHistory(); // 刷新历史记录
                    } else {
                        showAlert(response.error || '操作失败', 'danger');
                    }
                },
                error: function(xhr) {
                    let errorMsg = '系统错误，请稍后再试';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        errorMsg = response.error || errorMsg;
                    } catch (e) {}
                    
                    showAlert(errorMsg, 'danger');
                }
            });
        }

        // 全局变量存储当前订单号
        let currentOrderNumber = null;

        // 显示结果
        function showResult(data) {
            const now = new Date();
            const formattedTime = now.toLocaleString();

            // 存储当前订单号
            currentOrderNumber = data.order_number;

            // 设置订单详情
            $('#orderNumber').text(data.order_number);
            $('#customerName').text(data.customer_name);
            $('#oldStatus').text(data.old_status);
            $('#updateTime').text(formattedTime);

            // 如果是衣物状态更新，显示衣物信息
            if (data.clothing_name) {
                $('#clothingInfo').show();
                $('#clothingName').text(data.clothing_name);
            } else {
                $('#clothingInfo').hide();
            }

            // 显示成功信息
            showAlert('状态已成功更新为"{{ target_status }}"', 'success');

            // 显示结果容器
            $('#resultContainer').show();
            $('#orderDetails').show();

            // 绑定查看订单详情按钮事件
            $('#viewOrderDetailBtn').off('click').on('click', function() {
                if (currentOrderNumber) {
                    // 构建历史页面URL，并传递订单号参数
                    const historyUrl = `/history?order_number=${encodeURIComponent(currentOrderNumber)}`;
                    // 在新标签页中打开历史页面
                    window.open(historyUrl, '_blank');
                }
            });
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertBox = $('#alertBox');
            alertBox.text(message);
            alertBox.removeClass('alert-success alert-danger alert-info');
            alertBox.addClass('alert-' + type);
            
            $('#resultContainer').show();
        }

        // 隐藏结果
        function hideResult() {
            $('#resultContainer').hide();
        }

        // 获取历史记录
        function fetchHistory() {
            $.ajax({
                url: '{{ history_endpoint }}',
                type: 'GET',
                success: function(response) {
                    updateHistoryTable(response.history || []);
                },
                error: function() {
                    console.error('获取历史记录失败');
                }
            });
        }

        // 更新历史表格
        function updateHistoryTable(history) {
            const tbody = $('#historyBody');
            tbody.empty();
            
            const isFactoryOut = {{ 'true' if target_status == '出厂' else 'false' }};
            history.forEach(function(item) {
                const operatorOrCustomer = isFactoryOut ? (item.operator || '-') : (item.customer_name || '-');
                const row = `
                    <tr>
                        <td>${item.order_number}</td>
                        <td>${operatorOrCustomer}</td>
                        <td>${item.clothing_name || '-'}</td>
                        <td>${item.old_status}</td>
                        <td>${item.new_status}</td>
                        <td>${formatDate(item.created_at)}</td>
                    </tr>
                `;
                tbody.append(row);
            });
            
            if (history.length === 0) {
                tbody.append('<tr><td colspan="6" class="text-center">暂无记录</td></tr>');
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleString();
        }

        // === 出库单打印功能仅在"出厂"页面启用 ===
        {% if target_status == '出厂' %}
        // 绑定打印按钮
        $(document).on('click', '#printOutboundBtn', function() {
            $.get('/api/outbound_pending', function(res) {
                if (!res.success) {
                    alert(res.error || '获取待打印数据失败');
                    return;
                }
                const orders = res.orders || [];
                if (orders.length === 0) {
                    alert('没有待打印的出库记录');
                    return;
                }

                // 执行打印
                printOutboundSlip(orders);

                // 打印后标记已打印
                const ids = orders.map(o => o.id);
                $.ajax({
                    url: '/api/outbound_mark_printed',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({order_ids: ids}),
                    success: function(markRes) {
                        if (!markRes.success) {
                            console.error(markRes.error || '标记失败');
                        }
                        fetchHistory(); // 刷新历史
                    },
                    error: function() {
                        console.error('标记出库单已打印失败');
                    }
                });
            });
        });

        // 出库单 Lodop 打印实现
        function printOutboundSlip(history) {
            try {
                const LODOP = getLodop();
                LODOP.PRINT_INIT("出库单");
                // 80mm 小票宽度, 高度自适应(0)
                LODOP.SET_PRINT_PAGESIZE(1, "80mm", 0, "");

                let currentTop = 2; // mm
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", "出库单");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
                currentTop += 6;

                // 营业员（去重后打印）
                var opsSet = new Set();
                for (var i = 0; i < history.length; i++) {
                    var opName = history[i].operator;
                    if (opName && opName !== '-') {
                        opsSet.add(opName);
                    }
                }
                var operators = Array.from(opsSet);
                for (var j = 0; j < operators.length; j++) {
                    LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", "营业员: " + operators[j]);
                    currentTop += 6;
                }

                // 出厂日期
                const dateStr = (new Date()).toLocaleString();
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", `出厂日期: ${dateStr}`);
                currentTop += 6;

                // 分割线
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", "------------------------------");
                currentTop += 6;

                // 表头
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "40mm", "6mm", "衣物名称");
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "45mm", "20mm", "6mm", "数量");
                currentTop += 6;

                // 明细
                for (var k = 0; k < history.length; k++) {
                    var h = history[k];
                    if (h.clothing_name && h.clothing_name !== '-') {
                        var itemArr = h.clothing_name.split(',');
                        for (var m = 0; m < itemArr.length; m++) {
                            var name = itemArr[m].trim();
                            if (name) {
                                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "40mm", "6mm", name);
                                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "45mm", "20mm", "6mm", "1");
                                currentTop += 6;
                            }
                        }
                    }
                }

                // 自动走纸一点距离
                currentTop += 10;
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", "\n");

                LODOP.PRINT();
            } catch (e) {
                alert('打印失败: ' + e.message);
            }
        }
        {% endif %}
    </script>

    <nav class="mobile-bottom-nav">
        <a href="/mobile" class="nav-item" id="navHome">
            <span class="nav-icon">🧺</span>
            <span class="nav-label">收衣</span>
        </a>
        <a href="/status/to_factory?m=1" class="nav-item" id="navStatus">
            <span class="nav-icon">🔄</span>
            <span class="nav-label">状态处理</span>
        </a>
        <a href="/data_summary?m=1" class="nav-item" id="navData">
            <span class="nav-icon">📊</span>
            <span class="nav-label">数据查询</span>
        </a>
        <a href="/history?m=1" class="nav-item" id="navHistory">
            <span class="nav-icon">📜</span><span class="nav-label">历史订单</span>
        </a>
    </nav>

    <script>
        // 高亮底部导航
        document.addEventListener('DOMContentLoaded', function() {
            const path = window.location.pathname;
            if (path.startsWith('/mobile')) {
                document.getElementById('navHome').classList.add('active');
            } else if (path.startsWith('/status')) {
                document.getElementById('navStatus').classList.add('active');
            } else if (path.startsWith('/data_summary')) {
                document.getElementById('navData').classList.add('active');
            } else if (path.startsWith('/history')) {
                document.getElementById('navHistory').classList.add('active');
            }

            // 高亮状态快速菜单
            if (path.includes('/status/to_factory')) {
                document.getElementById('statusMenuToFactory').classList.add('active');
            } else if (path.includes('/status/factory_in')) {
                document.getElementById('statusMenuFactoryIn').classList.add('active');
            } else if (path.includes('/status/factory_out')) {
                document.getElementById('statusMenuFactoryOut').classList.add('active');
            } else if (path.includes('/status/on_shelf')) {
                document.getElementById('statusMenuOnShelf').classList.add('active');
            } else if (path.includes('/status/self_pickup')) {
                document.getElementById('statusMenuSelfPickup').classList.add('active');
            }
        });
    </script>
</body>
</html> 