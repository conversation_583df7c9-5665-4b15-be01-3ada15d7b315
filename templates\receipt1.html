<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小票打印</title>
    <style>
        @media print {
            body {
                width: 80mm;
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
                font-size: 21px; /* 从16px增大到21px (约30%增长) */
            }

            .receipt {
                width: 100%;
                padding: 5mm;
            }

            .receipt-header {
                text-align: center;
                margin-bottom: 5mm;
            }

            .receipt-header h1 {
                font-size: 27px; /* 从21px增大到27px (约30%增长) */
                margin: 0;
                padding: 0;
            }

            .receipt-header p {
                margin: 2mm 0;
                font-size: 21px; /* 从16px增大到21px (约30%增长) */
            }

            .receipt-body {
                margin-bottom: 5mm;
            }

            .receipt-body table {
                width: 100%;
                border-collapse: collapse;
            }

            .receipt-body th, .receipt-body td {
                text-align: left;
                padding: 1mm 0;
                font-size: 21px; /* 从16px增大到21px (约30%增长) */
            }

            .receipt-body th {
                border-bottom: 1px solid #000;
            }

            .receipt-footer {
                text-align: center;
                margin-top: 5mm;
                font-size: 10px;
            }

            .divider {
                border-top: 1px dashed #000;
                margin: 3mm 0;
            }

            .total-row {
                font-weight: bold;
                border-top: 1px solid #000;
                padding-top: 2mm;
            }

            .barcode {
                text-align: center;
                margin: 5mm 0;
            }

            .barcode img {
                max-width: 100%;
                height: auto;
            }
        }

        /* 非打印样式 */
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }

        .receipt {
            width: 80mm;
            margin: 20px auto;
            padding: 10mm;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .receipt-header {
            text-align: center;
            margin-bottom: 10mm;
        }

        .receipt-header h1 {
            font-size: 18px;
            margin: 0;
            padding: 0;
        }

        .receipt-header p {
            margin: 4mm 0;
            color: #666;
        }

        .receipt-body {
            margin-bottom: 10mm;
        }

        .receipt-body table {
            width: 100%;
            border-collapse: collapse;
        }

        .receipt-body th, .receipt-body td {
            text-align: left;
            padding: 2mm 0;
        }

        .receipt-body th {
            border-bottom: 1px solid #ddd;
            color: #333;
        }

        .receipt-footer {
            text-align: center;
            margin-top: 10mm;
            color: #999;
            font-size: 12px;
        }

        .divider {
            border-top: 1px dashed #ddd;
            margin: 5mm 0;
        }

        .total-row {
            font-weight: bold;
            border-top: 1px solid #ddd;
            padding-top: 3mm;
        }

        .barcode {
            text-align: center;
            margin: 10mm 0;
        }

        .barcode img {
            max-width: 100%;
            height: auto;
        }

        /* 移动端样式 */
        @media screen and (max-width: 768px) {
            .receipt {
                width: 90%;
                padding: 5mm;
            }

            .print-button {
                display: block;
                width: 80mm;
                margin: 20px auto;
                padding: 10px;
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 16px;
                cursor: pointer;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="receipt-header">
            <h1>Soulweave改衣坊</h1>
            <p>订单号: {{ order.order_number }}</p>
            <p>日期: {{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
        </div>

        <div class="receipt-body">
            <p><strong>客户信息:</strong></p>
            <p>姓名: {{ customer.name }}</p>
            <p>电话: {{ customer.phone }}</p>
            {% if order.address %}
            <p>地址: {{ order.address }}</p>
            {% endif %}

            <div class="divider"></div>

            <p><strong>衣物清单:</strong></p>
            <table class="receipt-items">
                <colgroup>
                    <col class="col-name">
                    <col class="col-quantity">
                    <col class="col-service">
                    <col class="col-price">
                </colgroup>
                <thead>
                    <tr>
                        <th class="th-name">品名</th>
                        <th class="th-quantity">数量</th>
                        <th class="th-service">服务</th>
                        <th class="th-price">单价</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in clothes %}
                    <tr>
                        <td class="td-name">{{ item.name }}{% if item.color %}({{ item.color }}){% endif %}</td>
                        <td class="td-quantity">{{ item.quantity or 1 }}</td>
                        <td class="td-service">
                            {% if item.services %}
                                {% set main_services = item.services | reject('equalto', '加急') | list %}
                                {% if main_services %}
                                    {{ main_services | join('/') }}{% if '加急' in item.services %}(急){% endif %}
                                {% else %}
                                    洗衣{% if '加急' in item.services %}(急){% endif %}
                                {% endif %}
                            {% else %}
                                洗衣
                            {% endif %}
                        </td>
                        <td class="td-price">¥{{ "%.2f"|format(item.price) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            <div class="receipt-total">
                <p style="margin: 2px 0; font-size: 18px;">数量: 共{{ clothes | sum(attribute='quantity') or clothes | length }}件</p>
                {% if order.discount_amount > 0 %}
                <p style="margin: 2px 0; font-size: 18px;">原价: ¥{{ "%.2f"|format(order.total_amount + order.discount_amount) }}</p>
                <p style="margin: 2px 0; font-size: 18px;">折扣: ¥{{ "%.2f"|format(order.discount_amount) }}</p>
                <p style="margin: 2px 0; font-size: 18px;">实付: ¥{{ "%.2f"|format(order.total_amount) }}</p>
                {% else %}
                <p style="margin: 2px 0; font-size: 18px;">总计: ¥{{ "%.2f"|format(order.total_amount) }}</p>
                {% endif %}
            </div>

            <div class="divider"></div>

            <p><strong>支付信息:</strong></p>
            <p>支付方式: {{ order.payment_method }}</p>
            <p>支付状态: {{ order.payment_status }}</p>
            {% if order.payment_time %}
            <p>支付时间: {{ order.payment_time.strftime('%Y-%m-%d %H:%M') }}</p>
            {% endif %}

            {% if customer_balance_info %}
            <div class="divider"></div>
            <p><strong>客户余额信息:</strong></p>
            {% if customer_balance_info.is_balance_payment %}
            <p>订单前总余额: ¥{{ "%.2f"|format(customer_balance_info.balance_before_order) }}</p>
            <p>本次消费: ¥{{ "%.2f"|format(customer_balance_info.balance_used) }}</p>
            <p>订单后总余额: ¥{{ "%.2f"|format(customer_balance_info.balance_after_order) }}</p>
            <p style="font-size: 11px; color: #666;">充值余额: ¥{{ "%.2f"|format(customer_balance_info.balance) }} | 赠送余额: ¥{{ "%.2f"|format(customer_balance_info.gift_balance) }}</p>
            {% else %}
            <p>当前总余额: ¥{{ "%.2f"|format(customer_balance_info.total_balance) }}</p>
            <p style="font-size: 18px; color: #666;">充值余额: ¥{{ "%.2f"|format(customer_balance_info.balance) }} | 赠送余额: ¥{{ "%.2f"|format(customer_balance_info.gift_balance) }}</p>
            {% endif %}
            {% endif %}

            <div class="divider"></div>

            <p><strong>订单状态:</strong> {{ order.status }}</p>
            <p><strong>操作员:</strong> {{ order.operator }}</p>
            {% if remarks %}
            <div class="divider"></div>
            <p><strong>备注:</strong> {{ remarks }}</p>
            {% endif %}
        </div>

        <div class="barcode">
            <img src="/barcode/{{ order.order_number }}/0" alt="订单条码">
        </div>

        <div class="receipt-footer">
            <p>感谢您的惠顾，欢迎再次光临！</p>
        </div>
    </div>

    {% if is_mobile %}
    <button class="print-button" onclick="window.print()">打印小票</button>
    {% endif %}
</body>
</html>
