# 通用模块使用指南

## 概述

本目录包含了项目中通用的工具模块，旨在提高代码质量、减少重复代码、统一异常处理和响应格式。

## 模块说明

### 1. validators.py - 数据验证模块

提供通用的数据验证功能，避免重复的验证逻辑。

#### 主要类和方法

- `DataValidator`: 通用数据验证器
  - `validate_required_fields()`: 验证必填字段
  - `validate_phone()`: 验证手机号格式
  - `validate_discount_rate()`: 验证折扣率
  - `validate_date_range()`: 验证日期范围
  - `validate_service_type()`: 验证服务类型
  - `validate_amount()`: 验证金额
  - `validate_mall_name()`: 验证商场名称
  - `validate_password()`: 验证密码

#### 使用示例

```python
from common.validators import DataValidator, ValidationError

# 验证必填字段
try:
    DataValidator.validate_required_fields(data, ['name', 'phone'])
except ValidationError as e:
    return APIResponse.error(e.message)

# 验证折扣率
try:
    DataValidator.validate_discount_rate(0.8)
except ValidationError as e:
    return APIResponse.error(e.message)
```

### 2. auth_utils.py - 权限验证模块

提供统一的权限检查功能，支持角色权限和区域权限。

#### 主要类和方法

- `PermissionChecker`: 权限检查器
  - `get_current_staff_info()`: 获取当前员工信息
  - `is_manager()`: 检查是否为管理员
  - `check_manager_permission()`: 检查管理员权限
  - `check_area_permission()`: 检查区域权限
  - `filter_by_area_permission()`: 根据权限过滤查询

#### 装饰器

- `@require_manager_permission`: 要求管理员权限
- `@require_area_permission`: 要求区域权限

#### 使用示例

```python
from common.auth_utils import require_manager_permission, PermissionChecker

# 使用装饰器检查权限
@require_manager_permission
def create_mall_customer():
    # 只有管理员可以访问
    pass

# 手动检查权限
staff_info = PermissionChecker.get_current_staff_info()
PermissionChecker.check_data_access_permission(customer.area)

# 根据权限过滤查询
query = PermissionChecker.filter_by_area_permission(
    MallCustomer.query, MallCustomer, 'area'
)
```

### 3. response_utils.py - 统一响应格式模块

提供标准化的API响应格式和错误处理。

#### 主要类和方法

- `APIResponse`: API响应格式化器
  - `success()`: 成功响应
  - `error()`: 错误响应
  - `paginated_success()`: 分页成功响应
  - `not_found()`: 资源未找到响应
  - `forbidden()`: 权限不足响应

#### 装饰器

- `@handle_api_error`: 统一API错误处理

#### 使用示例

```python
from common.response_utils import APIResponse, handle_api_error

# 成功响应
return APIResponse.success(data, "操作成功")

# 错误响应
return APIResponse.error("数据验证失败", 400)

# 分页响应
return APIResponse.paginated_success(items, total, page, per_page)

# 使用错误处理装饰器
@handle_api_error
def create_customer():
    # 异常会被自动捕获和处理
    pass
```

### 4. business_validators.py - 业务验证模块

提供特定业务逻辑的验证功能。

#### 主要类

- `MallCustomerValidator`: 商场客户验证器
- `DiscountValidator`: 折扣验证器
- `OrderValidator`: 订单验证器
- `UserValidator`: 用户验证器

#### 使用示例

```python
from common.business_validators import MallCustomerValidator, DiscountValidator

# 验证商场客户数据
validated_data = MallCustomerValidator.validate_create_data(request.json)

# 验证名称唯一性
MallCustomerValidator.validate_name_uniqueness(mall_name, area)

# 验证产品折扣数据
discount_data = DiscountValidator.validate_product_discount_data(request.json)
```

## 最佳实践

### 1. 统一的错误处理

```python
@login_required
@require_manager_permission
@handle_api_error
def create_resource():
    # 业务逻辑
    # 异常会被自动处理
    return APIResponse.success(data)
```

### 2. 数据验证流程

```python
def create_customer():
    data = request.json or {}
    
    # 1. 基础验证
    DataValidator.validate_required_fields(data, required_fields)
    
    # 2. 业务验证
    validated_data = MallCustomerValidator.validate_create_data(data)
    
    # 3. 唯一性验证
    MallCustomerValidator.validate_name_uniqueness(name, area)
    
    # 4. 权限验证
    PermissionChecker.check_area_permission(area)
```

### 3. 查询权限过滤

```python
def get_resources():
    query = Resource.query
    
    # 根据权限过滤
    query = PermissionChecker.filter_by_area_permission(
        query, Resource, 'area'
    )
    
    # 执行查询
    items = query.all()
    return APIResponse.success(items)
```

## 迁移指南

### 现有代码如何迁移

1. **替换手动权限检查**：
   ```python
   # 旧代码
   if session.get('staff_role') != 'manager':
       return jsonify({'error': '权限不足'}), 403
   
   # 新代码
   @require_manager_permission
   def your_function():
       pass
   ```

2. **替换手动数据验证**：
   ```python
   # 旧代码
   if not data.get('name'):
       return jsonify({'error': '名称不能为空'}), 400
   
   # 新代码
   DataValidator.validate_required_fields(data, ['name'])
   ```

3. **统一响应格式**：
   ```python
   # 旧代码
   return jsonify({'success': True, 'data': data}), 200
   
   # 新代码
   return APIResponse.success(data)
   ```

### 性能优化建议

1. 使用 `@handle_api_error` 装饰器统一异常处理
2. 使用权限过滤器减少不必要的数据查询
3. 利用验证器避免重复的验证逻辑
4. 使用标准响应格式提高前端处理效率

## 测试建议

1. 为每个验证器编写单元测试
2. 测试权限检查的各种场景
3. 验证响应格式的一致性
4. 测试异常处理的完整性

## 维护说明

1. 新增验证规则时，优先考虑添加到通用验证器
2. 业务特定的验证逻辑放在 business_validators 中
3. 保持响应格式的向后兼容性
4. 定期检查和更新权限规则

## 权限系统修复说明

### 修复背景
在代码优化过程中发现权限系统存在不一致的问题：
- 用户管理页面使用 `'admin'` 权限检查
- 数据汇总页面使用 `'manager'` 权限检查  
- 商场用户管理混合使用了多种权限逻辑

### 权限角色定义
系统中存在以下权限角色：
- `'admin'`: 超级管理员，拥有最高权限
- `'manager'`: 管理员，拥有管理权限
- `'staff'`: 普通员工，基础权限

### 修复后的权限系统

#### 1. 统一权限检查器 (`common/auth_utils.py`)
```python
class PermissionChecker:
    @staticmethod
    def is_admin() -> bool:
        """检查是否为超级管理员(admin角色)"""
        return session.get('staff_role') == 'admin'
    
    @staticmethod
    def is_manager() -> bool:
        """检查是否为管理员(manager角色)"""
        return session.get('staff_role') == 'manager'
    
    @staticmethod
    def is_admin_or_manager() -> bool:
        """检查是否为管理员(admin或manager角色)"""
        staff_role = session.get('staff_role')
        return staff_role in ['admin', 'manager']
    
    @staticmethod
    def has_headquarters_access() -> bool:
        """检查是否有总部权限"""
        staff_role = session.get('staff_role', '')
        staff_area = session.get('staff_area', '')
        return staff_role == 'admin' or staff_area == '总部'
```

#### 2. 权限装饰器
- `@require_admin_permission`: 要求admin权限
- `@require_manager_permission`: 要求manager权限
- `@require_admin_or_manager_permission`: 要求admin或manager权限
- `@require_area_permission`: 要求总部权限

#### 3. 页面权限修复

**用户管理页面** (`blueprints/admin.py`):
- 保持原有的 `'admin'` 权限检查
- 只有超级管理员才能管理用户

**数据汇总页面** (`app.py`):
- 保持原有的 `'manager'` 权限检查
- manager可以查看数据汇总，但有区域限制

**商场用户管理** (`blueprints/mall_customer.py`):
- 统一使用 `'manager'` 权限检查
- 添加区域权限控制：admin和总部用户可访问所有区域，其他用户只能访问自己区域

### 区域权限逻辑
1. **admin角色**: 可以访问所有区域的数据
2. **总部用户**: 可以访问所有区域的数据  
3. **区域manager**: 只能访问自己区域的数据
4. **普通员工**: 只能访问自己区域的数据

### 修复要点
1. **保持向后兼容**: 不改变现有的权限逻辑，只统一实现方式
2. **清晰的权限层级**: admin > 总部权限 > 区域manager > 普通员工
3. **统一的错误响应**: 权限不足时返回统一的错误信息
4. **简化的代码**: 移除复杂的装饰器，使用直接的权限检查

### 测试验证
修复后需要验证以下功能：
- [ ] 用户管理页面：只有admin能访问
- [ ] 数据汇总页面：manager及以上能访问，有区域限制
- [ ] 商场用户管理：manager及以上能访问，有区域限制
- [ ] 各项CRUD操作：权限检查正确
- [ ] 区域数据过滤：用户只能看到有权限的数据 