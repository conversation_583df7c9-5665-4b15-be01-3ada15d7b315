<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值小票补打印</title>
    <script language="javascript" src="/static/js/LodopFuncs.js"></script>
    <script src="/static/js/lodop-print.js"></script>
    <script src="/static/js/print-functions.js"></script>
    <style>
        body {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .receipt-container {
            max-width: 300px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border: 2px dashed #000;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .receipt-header {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .receipt-header h2 {
            margin: 0 0 5px 0;
            font-size: 20px;
            font-weight: bold;
        }
        
        .receipt-header .subtitle {
            margin: 0;
            font-size: 16px;
            color: #666;
        }
        
        .reprint-notice {
            text-align: center;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 14px;
            color: #856404;
        }
        
        .receipt-info {
            margin: 15px 0;
            line-height: 1.6;
        }
        
        .receipt-info div {
            margin: 5px 0;
            font-size: 14px;
        }
        
        .amount-section {
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 10px 0;
            margin: 15px 0;
        }
        
        .amount-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .total-amount {
            font-weight: bold;
            font-size: 16px;
        }
        
        .receipt-footer {
            text-align: center;
            margin-top: 15px;
            font-size: 12px;
            color: #666;
        }
        
        .print-button {
            display: block;
            width: 100%;
            padding: 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin: 20px 0;
        }
        
        .print-button:hover {
            background-color: #0056b3;
        }
        
        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            
            .print-button {
                display: none;
            }
            
            .receipt-container {
                box-shadow: none;
                max-width: none;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <div class="receipt-header">
            <h2>Soulweave改衣坊</h2>
            <p class="subtitle">充值小票</p>
        </div>
        
        <div class="reprint-notice">
            [补打印] - {{ reprint_time }}
            <br>补打印原因: {{ reprint_reason }}
        </div>
        
        <div class="receipt-info">
            <div><strong>客户姓名:</strong> {{ customer_name }}</div>
            <div><strong>手机号码:</strong> {{ customer_phone }}</div>
            <div><strong>充值时间:</strong> {{ recharge_time }}</div>
            <div><strong>操作员:</strong> {{ operator }}</div>
        </div>
        
        <div class="amount-section">
            <div class="amount-row">
                <span>充值金额:</span>
                <span>¥{{ "%.2f"|format(amount) }}</span>
            </div>
            {% if gift_amount > 0 %}
            <div class="amount-row">
                <span>赠送金额:</span>
                <span style="color: #28a745;">¥{{ "%.2f"|format(gift_amount) }}</span>
            </div>
            <div class="amount-row total-amount">
                <span>到账总额:</span>
                <span>¥{{ "%.2f"|format(amount + gift_amount) }}</span>
            </div>
            {% else %}
            <div class="amount-row total-amount">
                <span>充值金额:</span>
                <span>¥{{ "%.2f"|format(amount) }}</span>
            </div>
            {% endif %}
            <div class="amount-row">
                <span>支付方式:</span>
                <span>{{ payment_method }}</span>
            </div>
            {% if current_balance is defined %}
            <div class="amount-row">
                <span>当前余额:</span>
                <span>¥{{ "%.2f"|format(current_balance) }}</span>
            </div>
            {% endif %}
        </div>
        
        <div class="receipt-footer">
            <p>感谢您的信任，欢迎再次光临！</p>
            <p>{{ reprint_time }}</p>
        </div>
    </div>
    
    <button class="print-button" onclick="window.print()">打印小票</button>
    
    <script>
        // 页面加载完成后执行补打印逻辑
        window.addEventListener('load', async function() {
            const urlParams = new URLSearchParams(window.location.search);
            const rechargeId = urlParams.get('recharge_id');
            const reason = urlParams.get('reason') || '客户要求补打印';

            if (!rechargeId) {
                alert('错误：未提供充值ID，无法进行补打印。');
                return;
            }

            try {
                // 1. 从后端API获取小票数据
                const response = await fetch(`/api/recharge_receipt_data?recharge_id=${rechargeId}`);
                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.error || '获取充值数据失败');
                }

                const receiptData = result.receipt_data;

                // 2. 动态填充小票内容 (此部分可以根据需要进一步完善)
                document.querySelector('.reprint-notice').innerHTML = `[补打印] - ${receiptData.print_time}<br>补打印原因: ${reason}`;
                document.querySelector('.receipt-info').innerHTML = `
                    <div><strong>客户姓名:</strong> ${receiptData.customer_name}</div>
                    <div><strong>手机号码:</strong> ${receiptData.customer_phone}</div>
                    <div><strong>充值时间:</strong> ${receiptData.recharge_time}</div>
                    <div><strong>操作员:</strong> ${receiptData.operator}</div>
                `;
                // ... (可以继续填充其他字段)

                // 3. 尝试使用Lodop打印，如果失败则使用浏览器打印
                if (typeof window.autoPrintRechargeReceipt === 'function' && window.checkLodopAvailable()) {
                    console.log('检测到Lodop，执行Lodop补打印...');
                    // 为数据添加补打印标记
                    receiptData.isReprint = true;
                    receiptData.reprintTime = new Date().toLocaleString('zh-CN');
                    receiptData.reprintReason = reason;
                    window.autoPrintRechargeReceipt(receiptData);
                } else {
                    console.log('未检测到Lodop或相关函数，将使用浏览器打印。');
                    // 延迟以确保页面内容已渲染
                    setTimeout(() => window.print(), 500);
                }

            } catch (error) {
                console.error('补打印流程失败:', error);
                alert(`补打印失败: ${error.message}`);
            }
        });
    </script>
</body>
</html>