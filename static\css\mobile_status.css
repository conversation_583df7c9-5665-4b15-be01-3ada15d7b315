@media (max-width: 768px) {
    body {
        padding-top: 10px !important;
        padding-bottom: 70px !important;
    }

    .container {
        max-width: 100% !important;
        border-radius: 0 !important;
        padding: 15px !important;
        box-shadow: none !important;
    }

    .header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px !important;
    }

    .header .btn {
        width: 100% !important;
        margin-top: 8px !important;
    }

    .input-group {
        flex-direction: column !important;
    }

    .input-group .form-control {
        margin-bottom: 10px !important;
    }

    .btn-submit,
    .scan-btn {
        width: 100% !important;
    }

    .order-actions .btn {
        min-width: 100% !important;
        margin-top: 10px !important;
    }

    /* Mobile status page quick menu */
    .mobile-status-menu {
        display: flex !important;
        overflow-x: auto !important;
        margin-bottom: 15px !important;
        gap: 8px !important;
    }

    .mobile-status-menu a {
        flex: none !important;
        padding: 6px 12px !important;
        border: 1px solid #007BFF !important;
        border-radius: 20px !important;
        color: #007BFF !important;
        font-size: 14px !important;
        text-decoration: none !important;
    }

    .mobile-status-menu a.active {
        background: #007BFF !important;
        color: #fff !important;
    }

    /* Bottom nav reuse */
    .mobile-bottom-nav {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 56px !important;
        background: #ffffff !important;
        border-top: 1px solid #ddd !important;
        display: flex !important;
        justify-content: space-around !important;
        align-items: center !important;
        z-index: 1000 !important;
    }

    .mobile-bottom-nav .nav-item {
        flex: 1 1 auto !important;
        text-align: center !important;
        color: #666 !important;
        font-size: 12px !important;
        text-decoration: none !important;
    }

    .mobile-bottom-nav .nav-item .nav-icon {
        font-size: 20px !important;
        display: block !important;
        line-height: 20px !important;
        margin-bottom: 2px !important;
    }

    .mobile-bottom-nav .nav-item.active {
        color: #007BFF !important;
    }

    /* Hide back-home button */
    .header .btn-outline-secondary {
        display: none !important;
    }

    /* Fix order input field appearance */
    .input-group .form-control {
        height: 44px !important;
        padding: 10px 12px !important;
        font-size: 16px !important;
    }

    /* Ensure input-group children full width without extra border spacing */
    .input-group > * {
        width: 100% !important;
    }

    .input-group {
        border: none !important;
    }
} 