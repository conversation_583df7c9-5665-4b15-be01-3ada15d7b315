from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for, flash
from functools import wraps
import datetime
import json

from models import db, Customer, Order, RechargeRecord, MemberServiceDiscount
from blueprints.auth import login_required

bp = Blueprint('member', __name__)

@bp.route('/member_management')
@login_required
def member_management():
    """会员管理页面"""
    staff_name = session.get('staff_name', '未登录')
    staff_role = session.get('staff_role', '')

    # 允许管理员和营业员访问该页面
    if staff_role not in ['manager', 'staff']:
        flash('您没有权限访问该页面', 'error')
        return redirect(url_for('index'))

    return render_template('member_management.html', staff_name=staff_name)

# =====================================================================
# 会员基础CRUD操作
# =====================================================================

@bp.route('/api/members', methods=['GET'])
@login_required
def get_members():
    """获取会员列表"""
    try:
        # 记录开始时间
        start_time = datetime.datetime.now()
        print(f"开始获取会员列表: {start_time}")

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # 搜索参数
        search = request.args.get('search', '')
        status = request.args.get('status', 'all')  # all, active, inactive

        # 构建查询
        query = Customer.query

        # 应用搜索过滤
        if search:
            query = query.filter(db.or_(
                Customer.name.like(f'%{search}%'),
                Customer.phone.like(f'%{search}%')
            ))

        # 计算180天前的日期，用于活跃状态筛选
        active_cutoff_date = datetime.datetime.now() - datetime.timedelta(days=180)

        # 应用状态过滤
        if status == 'active':
            query = query.filter(Customer.updated_at >= active_cutoff_date)
        elif status == 'inactive':
            query = query.filter(Customer.updated_at < active_cutoff_date)

        # 获取总数，提前检查是否有结果
        total = query.count()

        # 如果没有记录，提前返回空结果
        if total == 0:
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            print(f"会员列表查询完成，耗时: {execution_time}秒，无匹配记录")

            return jsonify({
                'members': [],
                'total': 0,
                'pages': 0,
                'current_page': page,
                'total_pages': 0,
                'execution_time': execution_time
            })

        # 执行分页查询 - 优化排序，添加索引
        pagination = query.order_by(Customer.updated_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        members = pagination.items

        # 构建响应 - 使用列表推导式一次性构建结果
        member_list = [{
            'id': member.id,
            'name': member.name,
            'phone': member.phone,
            'balance': member.balance,
            'gift_balance': member.gift_balance or 0.0,
            'total_balance': member.total_balance,
            'created_at': member.created_at.isoformat(),
            'updated_at': member.updated_at.isoformat()
        } for member in members]

        # 计算执行时间
        end_time = datetime.datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        print(f"会员列表查询完成，耗时: {execution_time}秒，返回 {len(member_list)} 条记录")

        # 构建响应
        return jsonify({
            'members': member_list,
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page,
            'total_pages': pagination.pages,
            'execution_time': execution_time  # 添加执行时间到响应中，方便前端调试
        })
    except Exception as e:
        import traceback
        print(f"获取会员列表出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@bp.route('/api/members', methods=['POST'])
@login_required
def create_member():
    """创建新会员"""
    try:
        data = request.json

        # 验证必填字段
        if not data.get('name') or not data.get('phone'):
            return jsonify({'error': '姓名和电话是必填字段'}), 400

        # 检查电话号码是否已存在
        existing_member = Customer.query.filter_by(phone=data['phone']).first()
        if existing_member:
            return jsonify({'error': '该电话号码已被注册'}), 400

        # 创建新会员
        new_member = Customer(
            name=data['name'],
            phone=data['phone'],
            balance=float(data.get('balance', 0.0))
        )

        db.session.add(new_member)
        db.session.commit()

        return jsonify({
            'id': new_member.id,
            'name': new_member.name,
            'phone': new_member.phone,
            'balance': new_member.balance,
            'created_at': new_member.created_at.isoformat(),
            'updated_at': new_member.updated_at.isoformat()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@bp.route('/api/members/<int:member_id>', methods=['GET'])
@login_required
def get_member(member_id):
    """获取单个会员详情"""
    try:
        member = Customer.query.get_or_404(member_id)

        return jsonify({
            'id': member.id,
            'name': member.name,
            'phone': member.phone,
            'balance': member.balance,
            'gift_balance': member.gift_balance or 0.0,
            'total_balance': member.total_balance,
            'discount_rate': member.discount_rate or 1.0,
            'discount_expiry': member.discount_expiry.isoformat() if member.discount_expiry else None,
            'created_at': member.created_at.isoformat(),
            'updated_at': member.updated_at.isoformat()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/members/<int:member_id>', methods=['PUT'])
@login_required
def update_member(member_id):
    """更新会员信息"""
    try:
        member = Customer.query.get_or_404(member_id)
        data = request.json

        # 验证必填字段
        if not data.get('name') or not data.get('phone'):
            return jsonify({'error': '姓名和电话是必填字段'}), 400

        # 如果电话号码已更改，检查新电话号码是否已被其他会员使用
        if data['phone'] != member.phone:
            existing_member = Customer.query.filter_by(phone=data['phone']).first()
            if existing_member and existing_member.id != member_id:
                return jsonify({'error': '该电话号码已被其他会员使用'}), 400

        # 更新会员信息
        member.name = data['name']
        member.phone = data['phone']
        # 余额更新应该通过充值API进行，这里不直接修改余额

        db.session.commit()

        return jsonify({
            'id': member.id,
            'name': member.name,
            'phone': member.phone,
            'balance': member.balance,
            'created_at': member.created_at.isoformat(),
            'updated_at': member.updated_at.isoformat()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# @bp.route('/api/members/<int:member_id>', methods=['DELETE'])
# @login_required
# def delete_member(member_id):
#     """删除会员"""
#     try:
#         member = Customer.query.get_or_404(member_id)
#
#         # 如果会员有订单或充值记录，不允许删除
#         has_orders = Order.query.filter_by(customer_id=member_id).first() is not None
#         has_recharges = RechargeRecord.query.filter_by(customer_id=member_id).first() is not None
#
#         if has_orders or has_recharges:
#             return jsonify({'error': '该会员有订单或充值记录，无法删除'}), 400
#
#         db.session.delete(member)
#         db.session.commit()
#
#         return jsonify({'message': '会员删除成功'})
#     except Exception as e:
#         db.session.rollback()
#         return jsonify({'error': str(e)}), 500

# =====================================================================
# 会员充值功能
# =====================================================================

@bp.route('/api/members/<int:member_id>/recharge', methods=['POST'])
@login_required
def recharge_member(member_id):
    """会员账户充值"""
    try:
        member = Customer.query.get_or_404(member_id)
        data = request.json

        # 验证充值金额
        amount = float(data.get('amount', 0))
        if amount <= 0:
            return jsonify({'error': '充值金额必须大于零'}), 400

        # 验证支付方式
        payment_method = data.get('payment_method')
        if not payment_method:
            return jsonify({'error': '请选择支付方式'}), 400

        # 计算赠送金额（支持用户选择的规则）
        selected_rule_id = data.get('selected_rule_id')
        no_gift_rule = data.get('no_gift_rule', False)  # 新增：是否明确选择不使用规则

        if no_gift_rule:
            # 用户明确选择不使用任何赠送规则
            gift_amount = 0.0
        elif selected_rule_id:
            # 用户选择了特定规则
            from models import RechargeGiftRule
            rule = RechargeGiftRule.query.filter_by(id=selected_rule_id, is_active=True).first()
            if rule and rule.min_amount <= amount:
                if rule.gift_type == 'percentage':
                    gift_amount = amount * (rule.gift_value / 100.0)
                else:  # fixed
                    gift_amount = rule.gift_value
            else:
                gift_amount = 0.0
        else:
            # 使用默认最优规则
            from utils import calculate_gift_amount
            gift_amount = calculate_gift_amount(amount)

        # 更新会员余额（包含赠送金额）
        from utils import update_customer_balance
        new_balance = update_customer_balance(member, amount, gift_amount=gift_amount)

        # 创建充值记录
        recharge = RechargeRecord(
            customer_id=member_id,
            amount=amount,
            gift_amount=gift_amount,
            payment_method=payment_method,
            operator=session.get('staff_name', '未知'),
            remarks=data.get('remarks', '')
        )

        db.session.add(recharge)
        db.session.commit()

        return jsonify({
            'member_id': member_id,
            'amount': amount,
            'gift_amount': gift_amount,
            'new_balance': new_balance,
            'recharge_id': recharge.id,
            'operator': session.get('staff_name', '未知')
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# =====================================================================
# 会员订单和充值记录查询
# =====================================================================

@bp.route('/api/members/<int:member_id>/orders', methods=['GET'])
@login_required
def get_member_orders(member_id):
    """获取会员的订单历史"""
    try:
        member = Customer.query.get_or_404(member_id)

        # 查询该会员的所有订单，按创建时间降序排序
        orders = Order.query.filter_by(customer_id=member_id).order_by(Order.created_at.desc()).all()

        return jsonify({
            'member_id': member_id,
            'orders': [{
                'id': order.id,
                'order_number': order.order_number,
                'total_amount': order.total_amount,
                'payment_method': order.payment_method,
                'payment_status': order.payment_status,
                'status': order.status,
                'created_at': order.created_at.isoformat()
            } for order in orders]
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/members/<int:member_id>/recharges', methods=['GET'])
@login_required
def get_member_recharges(member_id):
    """获取会员的充值记录"""
    try:
        member = Customer.query.get_or_404(member_id)

        # 查询该会员的所有充值记录，按创建时间降序排序
        recharges = RechargeRecord.query.filter_by(customer_id=member_id).order_by(RechargeRecord.created_at.desc()).all()

        return jsonify({
            'member_id': member_id,
            'recharges': [{
                'id': recharge.id,
                'amount': recharge.amount,
                'gift_amount': recharge.gift_amount or 0.0,
                'payment_method': recharge.payment_method,
                'operator': recharge.operator,
                'remarks': recharge.remarks,
                'created_at': recharge.created_at.isoformat()
            } for recharge in recharges]
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# =====================================================================
# 会员折扣管理
# =====================================================================

@bp.route('/api/members/<int:member_id>/discounts', methods=['GET'])
@login_required
def get_member_discounts(member_id):
    """获取会员的服务折扣列表"""
    try:
        member = Customer.query.get_or_404(member_id)
        discounts = MemberServiceDiscount.query.filter_by(customer_id=member_id).all()

        result = []
        for discount in discounts:
            result.append({
                'id': discount.id,
                'service_type': discount.service_type,
                'discount_rate': discount.discount_rate,
                'valid_from': discount.valid_from.isoformat(),
                'valid_to': discount.valid_to.isoformat(),
                'is_active': discount.is_active,
                'created_at': discount.created_at.isoformat(),
                'updated_at': discount.updated_at.isoformat()
            })

        return jsonify({
            'discounts': result,
            'member': {
                'id': member.id,
                'name': member.name,
                'phone': member.phone,
                'discount_rate': member.discount_rate or 1.0,
                'discount_expiry': member.discount_expiry.isoformat() if member.discount_expiry else None
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/members/<int:member_id>/discounts', methods=['POST'])
@login_required
def create_member_discount(member_id):
    """创建会员服务折扣"""
    try:
        member = Customer.query.get_or_404(member_id)
        data = request.json

        # 验证必填字段
        required_fields = ['service_type', 'discount_rate', 'valid_from', 'valid_to']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必填字段: {field}'}), 400

        # 验证数据
        service_type = data['service_type']
        discount_rate = float(data['discount_rate'])
        valid_from = datetime.datetime.strptime(data['valid_from'], '%Y-%m-%d').date()
        valid_to = datetime.datetime.strptime(data['valid_to'], '%Y-%m-%d').date()

        if discount_rate <= 0 or discount_rate > 1:
            return jsonify({'error': '折扣率必须在0-1之间'}), 400

        if valid_from >= valid_to:
            return jsonify({'error': '生效日期必须早于失效日期'}), 400

        if service_type not in ['洗衣', '织补', '改衣', '其他']:
            return jsonify({'error': '无效的服务类型'}), 400

        # 检查是否存在重叠的折扣
        existing_discount = MemberServiceDiscount.query.filter(
            MemberServiceDiscount.customer_id == member_id,
            MemberServiceDiscount.service_type == service_type,
            MemberServiceDiscount.is_active == True,
            db.or_(
                db.and_(MemberServiceDiscount.valid_from <= valid_from, MemberServiceDiscount.valid_to >= valid_from),
                db.and_(MemberServiceDiscount.valid_from <= valid_to, MemberServiceDiscount.valid_to >= valid_to),
                db.and_(MemberServiceDiscount.valid_from >= valid_from, MemberServiceDiscount.valid_to <= valid_to)
            )
        ).first()

        if existing_discount:
            return jsonify({'error': f'该服务类型在指定时间段内已存在折扣'}), 400

        # 创建新折扣
        new_discount = MemberServiceDiscount(
            customer_id=member_id,
            service_type=service_type,
            discount_rate=discount_rate,
            valid_from=valid_from,
            valid_to=valid_to,
            is_active=data.get('is_active', True)
        )

        db.session.add(new_discount)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '服务折扣创建成功',
            'discount': {
                'id': new_discount.id,
                'service_type': new_discount.service_type,
                'discount_rate': new_discount.discount_rate,
                'valid_from': new_discount.valid_from.isoformat(),
                'valid_to': new_discount.valid_to.isoformat(),
                'is_active': new_discount.is_active,
                'created_at': new_discount.created_at.isoformat(),
                'updated_at': new_discount.updated_at.isoformat()
            }
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@bp.route('/api/member_discounts/<int:discount_id>', methods=['PUT'])
@login_required
def update_member_discount(discount_id):
    """更新会员服务折扣"""
    try:
        discount = MemberServiceDiscount.query.get_or_404(discount_id)
        data = request.json

        # 验证数据
        if 'discount_rate' in data:
            discount_rate = float(data['discount_rate'])
            if discount_rate <= 0 or discount_rate > 1:
                return jsonify({'error': '折扣率必须在0-1之间'}), 400
            discount.discount_rate = discount_rate

        if 'valid_from' in data:
            valid_from = datetime.datetime.strptime(data['valid_from'], '%Y-%m-%d').date()
            discount.valid_from = valid_from

        if 'valid_to' in data:
            valid_to = datetime.datetime.strptime(data['valid_to'], '%Y-%m-%d').date()
            discount.valid_to = valid_to

        if 'is_active' in data:
            discount.is_active = bool(data['is_active'])

        # 验证日期逻辑
        if discount.valid_from >= discount.valid_to:
            return jsonify({'error': '生效日期必须早于失效日期'}), 400

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '服务折扣更新成功',
            'discount': {
                'id': discount.id,
                'service_type': discount.service_type,
                'discount_rate': discount.discount_rate,
                'valid_from': discount.valid_from.isoformat(),
                'valid_to': discount.valid_to.isoformat(),
                'is_active': discount.is_active,
                'created_at': discount.created_at.isoformat(),
                'updated_at': discount.updated_at.isoformat()
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@bp.route('/api/member_discounts/<int:discount_id>', methods=['DELETE'])
@login_required
def delete_member_discount(discount_id):
    """删除会员服务折扣"""
    try:
        discount = MemberServiceDiscount.query.get_or_404(discount_id)
        db.session.delete(discount)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '服务折扣删除成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@bp.route('/api/members/<int:member_id>/overall_discount', methods=['PUT'])
@login_required
def update_member_overall_discount(member_id):
    """更新会员整体折扣率"""
    try:
        member = Customer.query.get_or_404(member_id)
        data = request.json

        # 验证数据
        if 'discount_rate' in data:
            discount_rate = float(data['discount_rate'])
            if discount_rate <= 0 or discount_rate > 1:
                return jsonify({'error': '折扣率必须在0-1之间'}), 400
            member.discount_rate = discount_rate

        if 'discount_expiry' in data:
            if data['discount_expiry']:
                discount_expiry = datetime.datetime.strptime(data['discount_expiry'], '%Y-%m-%d').date()
                member.discount_expiry = discount_expiry
            else:
                member.discount_expiry = None

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '会员整体折扣更新成功',
            'member': {
                'id': member.id,
                'name': member.name,
                'phone': member.phone,
                'discount_rate': member.discount_rate or 1.0,
                'discount_expiry': member.discount_expiry.isoformat() if member.discount_expiry else None
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# =====================================================================
# 会员折扣计算函数（供其他模块使用）
# =====================================================================

def calculate_member_discount(customer_id, clothing_items):
    """计算会员折扣

    Args:
        customer_id: 客户ID
        clothing_items: 衣物列表，每个项目包含服务类型和价格

    Returns:
        dict: 包含折扣信息的字典
    """
    try:
        customer = Customer.query.get(customer_id)
        if not customer:
            return {'total_discount': 0, 'discounted_items': []}

        today = datetime.date.today()
        total_discount = 0
        discounted_items = []

        for item in clothing_items:
            item_price = float(item.get('price', 0))
            item_services = item.get('services', [])
            item_discount = 0
            applied_discount_rate = 1.0
            discount_source = None

            # 检查是否有特定服务类型的折扣
            service_discount_applied = False
            for service in item_services:
                service_discount = MemberServiceDiscount.query.filter(
                    MemberServiceDiscount.customer_id == customer_id,
                    MemberServiceDiscount.service_type == service,
                    MemberServiceDiscount.is_active == True,
                    MemberServiceDiscount.valid_from <= today,
                    MemberServiceDiscount.valid_to >= today
                ).first()

                if service_discount:
                    # 使用最低的折扣率（最大的优惠）
                    if service_discount.discount_rate < applied_discount_rate:
                        applied_discount_rate = service_discount.discount_rate
                        discount_source = f"{service}服务折扣"
                    service_discount_applied = True

            # 如果没有特定服务折扣，检查整体折扣
            if not service_discount_applied and customer.discount_rate and customer.discount_rate < 1.0:
                # 检查整体折扣是否有效
                if not customer.discount_expiry or customer.discount_expiry >= today:
                    applied_discount_rate = customer.discount_rate
                    discount_source = "会员整体折扣"

            # 计算折扣金额
            if applied_discount_rate < 1.0:
                discounted_price = item_price * applied_discount_rate
                item_discount = item_price - discounted_price
                total_discount += item_discount

                discounted_items.append({
                    'original_price': item_price,
                    'discounted_price': discounted_price,
                    'discount_amount': item_discount,
                    'discount_rate': applied_discount_rate,
                    'discount_source': discount_source
                })
            else:
                discounted_items.append({
                    'original_price': item_price,
                    'discounted_price': item_price,
                    'discount_amount': 0,
                    'discount_rate': 1.0,
                    'discount_source': None
                })

        return {
            'total_discount': total_discount,
            'discounted_items': discounted_items,
            'customer_discount_rate': customer.discount_rate or 1.0,
            'customer_discount_expiry': customer.discount_expiry.isoformat() if customer.discount_expiry else None
        }

    except Exception as e:
        print(f"计算会员折扣出错: {str(e)}")
        return {'total_discount': 0, 'discounted_items': []}