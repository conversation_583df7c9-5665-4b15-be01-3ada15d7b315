<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soulweave改衣坊 - 数据汇总</title>

    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fff;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            border-radius: 8px;
            flex-wrap: wrap;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .staff-info {
            display: flex;
            align-items: center;
            gap: 15px;
            background: #f5f5f5;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .logout-btn {
            color: #dc3545;
            text-decoration: none;
            font-weight: 500;
        }
        .logout-btn:hover {
            text-decoration: underline;
        }
        h1 {
            font-size: 1.8rem;
            margin: 0;
        }
        a {
            text-decoration: none;
            color: #007BFF;
        }
        a:hover {
            text-decoration: underline;
        }
        .date-range {
            display: flex;
            background: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            align-items: center;
            gap: 15px;
        }
        .date-range-label {
            font-weight: bold;
            margin-right: 10px;
        }
        .date-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            padding: 8px 16px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .dashboard-item {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
            transition: transform 0.3s;
        }
        .dashboard-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .dashboard-item h2 {
            font-size: 1.2rem;
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
        }
        .dashboard-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007BFF;
            margin-bottom: 10px;
        }
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .chart-header h2 {
            font-size: 1.2rem;
            margin: 0;
            color: #333;
        }
        .table-container {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            overflow-x: auto;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: 600;
        }
        .data-table tr:hover {
            background-color: #f9f9f9;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            font-size: 1.2rem;
            color: #666;
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
        
        /* 打印样式 */
        @media print {
            .header-controls, .date-range, .no-print {
                display: none !important;
            }
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
            }
            .dashboard-item, .table-container {
                box-shadow: none;
                border: 1px solid #ddd;
                break-inside: avoid;
            }
        }
        .payment-status-note {
            color: #e74c3c;
            font-size: 0.9em;
            margin: 0;
            flex-basis: 100%;
            margin-top: 5px;
            font-weight: bold;
        }

        .table-controls {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
        }

        .export-btn {
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .export-btn:hover {
            background-color: #219a52;
        }

        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding: 10px 0;
        }

        .pagination-info {
            font-size: 14px;
            color: #666;
        }

        .pagination-controls {
            display: flex;
            gap: 10px;
        }

        .pagination-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .pagination-btn:hover {
            background-color: #2980b9;
        }

        .pagination-btn:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>数据汇总分析</h1>
            <p class="payment-status-note">（注：仅统计付款状态为"已付款"的订单，汇总数据按照支付时间而非创建时间进行统计）</p>
            <div class="header-controls">
                {% if session.staff_name %}
                <div class="staff-info">
                    <span>营业员: {{ session.staff_name }}</span>
                    <a href="/logout" class="logout-btn">登出</a>
                </div>
                {% endif %}
                <a href="/" style="margin-right: 15px;">返回首页</a>
                <a href="/rack" style="margin-right: 15px; color: #ff6b35;">
                    📦 格架管理
                </a>
                <a href="/history">查询历史订单</a>
            </div>
        </div>

        <!-- 添加权限提示信息 -->
        {% if session.get('staff_role') != 'manager' %}
        <div style="margin-bottom: 15px; background-color: #fff3cd; padding: 10px; border-radius: 8px; border-left: 4px solid #ffc107;">
            <strong>提示：</strong> 您当前查看的是由您操作的订单数据和个人业绩。普通营业员只能查看自己操作的数据。
        </div>
        {% elif session.get('staff_area') and session.get('staff_area') != '总部' %}
        <div style="margin-bottom: 15px; background-color: #d1ecf1; padding: 10px; border-radius: 8px; border-left: 4px solid #17a2b8;">
            <strong>提示：</strong> 您当前查看的是{{ session.get('staff_area') }}区域的数据和营业员业绩。区域管理员只能查看本区域数据。
        </div>
        {% else %}
        <div style="margin-bottom: 15px; background-color: #d4edda; padding: 10px; border-radius: 8px; border-left: 4px solid #28a745;">
            <strong>提示：</strong> 您具有超级管理员权限，可以查看所有区域的完整数据和营业员业绩。
        </div>
        {% endif %}

        <!-- 添加数据统计范围说明 -->
        <div style="margin-bottom: 15px; background-color: #f8f9fa; padding: 10px; border-radius: 8px; border-left: 4px solid #6c757d;">
            <strong>统计说明：</strong> 数据汇总仅包含已付款订单，不包含余额付款的订单。按支付时间统计，未付款订单不参与汇总。
        </div>

        <div class="date-range">
            <div>
                <span class="date-range-label">开始日期:</span>
                <input type="date" id="startDate" class="date-input">
            </div>
            <div>
                <span class="date-range-label">结束日期:</span>
                <input type="date" id="endDate" class="date-input">
            </div>
            <div>
                <span class="date-range-label">营业员:</span>
                <select id="operatorSelect" class="date-input">
                    <option value="">全部营业员</option>
                </select>
            </div>
            <button id="applyDateRange" class="btn">应用</button>
            <button id="printReport" class="btn" style="background-color: #28a745;">打印报表</button>
        </div>

        <div id="dashboard" class="dashboard">
            <div class="dashboard-item">
                <div class="dashboard-item-header">
                    <h3>总订单数</h3>
                </div>
                <div class="dashboard-item-content">
                    <span id="totalOrders">-</span>
                </div>
            </div>
            
            <div class="dashboard-item">
                <div class="dashboard-item-header">
                    <h3>总收入 (元)</h3>
                </div>
                <div class="dashboard-item-content">
                    <span id="totalRevenue">-</span>
                </div>
            </div>
            
            <div class="dashboard-item">
                <div class="dashboard-item-header">
                    <h3>衣物总件数</h3>
                </div>
                <div class="dashboard-item-content">
                    <span id="totalItems">-</span>
                </div>
            </div>
            

            
            <div class="dashboard-item">
                <div class="dashboard-item-header">
                    <h3>充值金额 (元)</h3>
                </div>
                <div class="dashboard-item-content">
                    <span id="totalRecharge">-</span>
                </div>
            </div>
            
            <div class="dashboard-item">
                <div class="dashboard-item-header">
                    <h3>平均订单金额 (元)</h3>
                </div>
                <div class="dashboard-item-content">
                    <span id="avgOrderValue">-</span>
                </div>
            </div>
            
            <div class="dashboard-item">
                <div class="dashboard-item-header">
                    <h3>日均订单数</h3>
                </div>
                <div class="dashboard-item-content">
                    <span id="avgOrdersPerDay">-</span>
                </div>
            </div>
        </div>





        <div class="table-container">
            <div class="chart-header">
                <h2>营业员充值汇总</h2>
                <div class="table-controls">
                    <button id="exportRechargeBtn" class="export-btn">导出Excel</button>
                </div>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>营业员姓名</th>
                        <th>充值笔数</th>
                        <th>充值金额 (元)</th>
                        <th>赠送金额 (元)</th>
                        <th>总金额 (元)</th>
                        <th>占比 (%)</th>
                    </tr>
                </thead>
                <tbody id="rechargeDetailsBody">
                    <tr>
                        <td colspan="7" class="loading">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="table-container">
            <div class="chart-header">
                <h2>每日详细数据</h2>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>支付日期</th>
                        <th>订单数量</th>
                        <th>衣物件数</th>
                        <th>收入 (元)</th>
                        <th>平均订单金额 (元)</th>
                    </tr>
                </thead>
                <tbody id="dailyDataBody">
                    <tr>
                        <td colspan="5" class="loading">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="table-container">
            <div class="chart-header">
                <h2>营业员业绩明细</h2>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>营业员</th>
                        <th>订单数量</th>
                        <th>衣物件数</th>
                        <th>收入 (元)</th>
                        <th>平均订单金额 (元)</th>
                        <th>业绩占比 (%)</th>
                    </tr>
                </thead>
                <tbody id="operatorStatsBody">
                    <tr>
                        <td colspan="7" class="loading">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="table-container">
            <div class="chart-header">
                <h2>服务类型汇总</h2>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>服务名称</th>
                        <th>衣物件数</th>
                        <th>金额 (元)</th>
                        <th>占比 (%)</th>
                    </tr>
                </thead>
                <tbody id="serviceTypesBody">
                    <tr>
                        <td colspan="5" class="loading">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        
        // 设置默认日期范围（近7天）
        function setDefaultDateRange() {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 7);
            
            document.getElementById('startDate').value = formatDate(startDate);
            document.getElementById('endDate').value = formatDate(endDate);
        }
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 获取汇总数据
        async function fetchSummaryData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const selectedOperator = document.getElementById('operatorSelect').value;

            // 构建查询参数
            let queryParams = `start_date=${startDate}&end_date=${endDate}`;
            if (selectedOperator) {
                queryParams += `&operator=${encodeURIComponent(selectedOperator)}`;
            }

            try {
                const response = await fetch(`/api/summary_data?${queryParams}`);
                if (!response.ok) {
                    throw new Error('服务器响应错误');
                }
                
                const data = await response.json();
                if (data.success) {
                    updateDashboard(data.data);
                    updateTables(data.data);
                } else {
                    showError(data.error || '获取数据失败');
                }
            } catch (error) {
                showError('获取数据失败: ' + error.message);
            }
        }
        
        // 更新仪表盘数据
        function updateDashboard(data) {
            document.getElementById('totalOrders').textContent = data.total_orders;
            document.getElementById('totalRevenue').textContent = data.total_revenue.toFixed(2);
            document.getElementById('totalItems').textContent = data.total_items;
            document.getElementById('totalRecharge').textContent = data.total_recharge.toFixed(2);

            // 计算平均订单金额
            const avgOrderValue = data.total_orders > 0 ? data.total_revenue / data.total_orders : 0;
            document.getElementById('avgOrderValue').textContent = avgOrderValue.toFixed(2);

            // 计算日均订单数
            const days = Object.keys(data.daily_data).length || 1;
            const avgOrdersPerDay = data.total_orders / days;
            document.getElementById('avgOrdersPerDay').textContent = avgOrdersPerDay.toFixed(1);
        }
        




        
        // 更新表格数据
        function updateTables(data) {
            updateDailyDataTable(data.daily_data);
            updateOperatorStatsTable(data.operator_stats);
            updateServiceTypesTable(data.service_types);
            updateRechargeDetailsTable(data.recharge_summary);  // 使用汇总数据而不是明细数据
        }
        


        // 更新充值汇总表格
        function updateRechargeDetailsTable(rechargeSummary) {
            const tableBody = document.getElementById('rechargeDetailsBody');
            tableBody.innerHTML = '';

            if (!rechargeSummary || Object.keys(rechargeSummary).length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="loading">暂无充值记录</td></tr>';
                return;
            }

            // 计算总充值金额，用于占比计算
            const totalRechargeAmount = Object.values(rechargeSummary).reduce((sum, stats) => sum + stats.total_amount, 0);

            // 过滤掉总金额为0的营业员，然后按总金额降序排序
            const filteredOperators = Object.entries(rechargeSummary).filter(([operator, stats]) => stats.total_amount > 0);
            const sortedOperators = filteredOperators.sort((a, b) => b[1].total_amount - a[1].total_amount);

            sortedOperators.forEach(([operator, stats], index) => {
                const percentage = totalRechargeAmount > 0 ? (stats.total_amount / totalRechargeAmount * 100) : 0;
                const row = `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${operator}</td>
                        <td>${stats.count}</td>
                        <td>${stats.recharge_amount.toFixed(2)}</td>
                        <td>${stats.gift_amount.toFixed(2)}</td>
                        <td>${stats.total_amount.toFixed(2)}</td>
                        <td>${percentage.toFixed(2)}%</td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }

        // 更新每日数据表格
        function updateDailyDataTable(dailyData) {
            const tableBody = document.getElementById('dailyDataBody');
            tableBody.innerHTML = '';
            
            const dates = Object.keys(dailyData).sort((a, b) => new Date(b) - new Date(a)); // 降序排列
            
            dates.forEach(date => {
                const orders = dailyData[date].orders;
                const items = dailyData[date].items || 0;
                const revenue = dailyData[date].revenue;
                const avgOrderValue = orders > 0 ? revenue / orders : 0;

                const row = `
                    <tr>
                        <td>${date}</td>
                        <td>${orders}</td>
                        <td>${items}</td>
                        <td>${revenue.toFixed(2)}</td>
                        <td>${avgOrderValue.toFixed(2)}</td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
            
            if (dates.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5">暂无数据</td></tr>';
            }
        }
        
        // 更新营业员业绩明细表格
        function updateOperatorStatsTable(operatorStats) {
            const tableBody = document.getElementById('operatorStatsBody');
            tableBody.innerHTML = '';
            
            // 如果没有数据
            if (!operatorStats || Object.keys(operatorStats).length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7">暂无数据</td></tr>';
                return;
            }
            
            // 计算总收入，用于计算占比
            const totalRevenue = Object.values(operatorStats).reduce((sum, stats) => sum + stats.revenue, 0);
            
            // 转换为数组以便排序
            const operators = Object.entries(operatorStats);
            
            operators.forEach(([operator, stats], index) => {
                // 计算平均订单金额
                const avgOrderValue = stats.orders > 0 ? stats.revenue / stats.orders : 0;
                // 计算业绩占比
                const revenuePercentage = totalRevenue > 0 ? (stats.revenue / totalRevenue * 100) : 0;
                
                const row = `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${operator}</td>
                        <td>${stats.orders}</td>
                        <td>${stats.items}</td>
                        <td>${stats.revenue.toFixed(2)}</td>
                        <td>${avgOrderValue.toFixed(2)}</td>
                        <td>${revenuePercentage.toFixed(2)}%</td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }
        
        // 更新服务类型汇总表格
        function updateServiceTypesTable(serviceTypes) {
            const tableBody = document.getElementById('serviceTypesBody');
            tableBody.innerHTML = '';

            if (!serviceTypes || Object.keys(serviceTypes).length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5">暂无数据</td></tr>';
                return;
            }

            // 计算总件数，用于占比
            const totalItems = Object.values(serviceTypes).reduce((sum, obj) => sum + (obj.items || 0), 0);

            // serviceTypes 已按件数降序排序于后端，这里保持顺序
            Object.entries(serviceTypes).forEach(([service, obj], index) => {
                const count = obj.items || 0;
                const amount = obj.amount ? obj.amount.toFixed(2) : '0.00';
                const percentage = totalItems > 0 ? (count / totalItems * 100) : 0;
                const row = `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${service}</td>
                        <td>${count}</td>
                        <td>${amount}</td>
                        <td>${percentage.toFixed(2)}%</td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }
        
        // 显示错误消息
        function showError(message) {
            alert('错误: ' + message);
        }

        // 充值汇总数据已经包含在主数据汇总API中，不需要单独获取

        // 导出充值汇总Excel
        async function exportRechargeDetails() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const selectedOperator = document.getElementById('operatorSelect').value;

            // 构建查询参数
            let queryParams = `start_date=${startDate}&end_date=${endDate}`;
            if (selectedOperator) {
                queryParams += `&operator=${encodeURIComponent(selectedOperator)}`;
            }

            try {
                const response = await fetch(`/api/export_recharge_summary?${queryParams}`);
                if (!response.ok) {
                    throw new Error('导出失败');
                }

                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `充值汇总_${startDate}_${endDate}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } catch (error) {
                showError('导出失败: ' + error.message);
            }
        }

        // 加载营业员列表
        async function loadOperators() {
            try {
                const response = await fetch('/api/operators');
                if (!response.ok) {
                    throw new Error('获取营业员列表失败');
                }

                const data = await response.json();
                if (data.success) {
                    const operatorSelect = document.getElementById('operatorSelect');

                    // 清空现有选项（保留"全部营业员"选项）
                    operatorSelect.innerHTML = '<option value="">全部营业员</option>';

                    // 添加营业员选项
                    data.operators.forEach(operator => {
                        const option = document.createElement('option');
                        option.value = operator;
                        option.textContent = operator;
                        operatorSelect.appendChild(option);
                    });
                } else {
                    console.error('获取营业员列表失败:', data.error);
                }
            } catch (error) {
                console.error('加载营业员列表失败:', error);
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            setDefaultDateRange();
            loadOperators();  // 加载营业员列表
            fetchSummaryData();

            // 日期范围应用按钮
            document.getElementById('applyDateRange').addEventListener('click', function() {
                fetchSummaryData();
            });

            // 绑定导出按钮事件
            document.getElementById('exportRechargeBtn').addEventListener('click', exportRechargeDetails);

            // 打印报表按钮
            document.getElementById('printReport').addEventListener('click', function() {
                window.print();
            });
        });
    </script>
</body>
</html> 