import os
import uuid
import base64
import datetime
import io
from flask import current_app
from sqlalchemy import func
from models import db, Order, Customer, RechargeRecord
import re  # 新增：用于移动端UA检测
import socket  # 新增：UDP通信
import struct  # 新增：字节打包

# 增加生成条形码所需的依赖
try:
    from PIL import Image, ImageDraw, ImageFont
    import barcode  # 添加python-barcode库依赖
    from barcode.writer import ImageWriter
except ImportError:
    print("警告: PIL库或barcode库未安装，条形码生成功能将不可用")
    print("请使用pip安装: pip install pillow python-barcode")

def save_base64_image(base64_data, customer_phone, index=0):
    """将Base64格式的图片数据保存为文件

    Args:
        base64_data: Base64编码的图片数据
        customer_phone: 客户手机号
        index: 图片索引

    Returns:
        str: 相对路径，失败返回None
    """
    try:
        print(f"开始保存图片: 客户 {customer_phone}, 索引 {index}")
        if not base64_data:
            print("空的base64数据")
            return None

        if isinstance(base64_data, str) and len(base64_data) < 100:
            print(f"base64数据异常短: {base64_data[:20]}...")
            return None

        timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        random_id = uuid.uuid4().hex[:8]
        filename = f"{customer_phone}_{timestamp}_{random_id}_{index}.jpg"

        # 确保使用相对路径
        upload_folder = current_app.config['UPLOAD_FOLDER']
        filepath = os.path.join(upload_folder, filename)

        # 确保目标目录存在
        full_dir_path = os.path.join(current_app.root_path, 'static', upload_folder)
        print(f"创建目录: {full_dir_path}")
        os.makedirs(full_dir_path, exist_ok=True)

        full_file_path = os.path.join(current_app.root_path, 'static', filepath)
        print(f"保存图片到: {full_file_path}")

        # 将Base64数据解码为二进制
        if isinstance(base64_data, str):
            # 检查是否包含data:image前缀
            if ',' in base64_data:
                print("从数据URL中提取Base64内容")
                base64_data = base64_data.split(',')[1]

            # 解码Base64
            try:
                binary_data = base64.b64decode(base64_data)
                print(f"解码成功，数据大小: {len(binary_data)} 字节")
            except Exception as e:
                print(f"Base64解码失败: {str(e)}")
                return None
        else:
            print(f"不是字符串类型的Base64数据: {type(base64_data)}")
            return None

        # 写入文件
        with open(full_file_path, "wb") as fh:
            fh.write(binary_data)
            print(f"图片文件写入成功")

        # 返回相对路径
        return filepath
    except Exception as e:
        print(f"保存图片失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def generate_order_number():
    """生成订单编号 - 6位纯数字的顺序编号

    Returns:
        str: 6位纯数字订单编号
    """
    try:
        # 获取当前最大订单号，如果不存在则从100000开始
        max_order = db.session.query(func.max(Order.order_number)).scalar()

        # 检查最大订单号是否为6位数字
        if max_order is None or not max_order.isdigit() or len(max_order) != 6:
            # 如果没有订单或订单号格式不是6位纯数字，则从初始值开始
            new_order_num = 100000  # 6位数字起始值
        else:
            # 已有6位订单号，递增
            new_order_num = int(max_order) + 1

        # 如果递增后超过6位，则重新从100000开始
        if new_order_num >= 1000000:
            new_order_num = 100000

        # 在生成订单号后，检查是否已存在
        while Order.query.filter_by(order_number=str(new_order_num)).first() is not None:
            # 如果订单号已存在，则继续递增
            new_order_num += 1
            # 如果递增后超过6位，则从100000重新开始
            if new_order_num >= 1000000:
                new_order_num = 100000

        # 转为字符串返回
        return str(new_order_num)
    except Exception as e:
        print(f"生成订单号出错: {str(e)}")
        # 出错时使用时间戳+随机数作为备选方案
        timestamp = datetime.datetime.now().strftime('%y%m%d%H%M')
        random_id = str(uuid.uuid4().int)[:3]
        fallback_number = f"{timestamp}{random_id}"[-6:].zfill(6)

        # 同样检查备用订单号是否重复
        while Order.query.filter_by(order_number=fallback_number).first() is not None:
            random_id = str(uuid.uuid4().int)[:3]
            fallback_number = f"{timestamp}{random_id}"[-6:].zfill(6)

        return fallback_number

def update_customer_balance(customer, amount, is_recharge=True, gift_amount=0.0):
    """更新客户余额

    Args:
        customer: 客户对象
        amount: 金额
        is_recharge: 是否为充值，默认为True。
                    如果为False，则为消费，金额为负数
        gift_amount: 赠送金额（仅在充值时使用）

    Returns:
        float: 更新后的总余额
    """
    if is_recharge:
        # 充值：增加充值余额和赠送余额
        customer.balance += amount
        if gift_amount > 0:
            customer.gift_balance = (customer.gift_balance or 0.0) + gift_amount
    else:
        # 消费：优先使用赠送余额，不足时使用充值余额
        remaining_amount = amount

        # 先扣除赠送余额
        if customer.gift_balance and customer.gift_balance > 0:
            if customer.gift_balance >= remaining_amount:
                customer.gift_balance -= remaining_amount
                remaining_amount = 0
            else:
                remaining_amount -= customer.gift_balance
                customer.gift_balance = 0

        # 如果还有剩余金额，从充值余额中扣除
        if remaining_amount > 0:
            customer.balance -= remaining_amount

    return customer.balance + (customer.gift_balance or 0.0)

def calculate_gift_amount(recharge_amount):
    """计算充值赠送金额

    Args:
        recharge_amount: 充值金额

    Returns:
        float: 赠送金额
    """
    from models import RechargeGiftRule

    try:
        # 查询所有启用的赠送规则，按最小金额降序排列
        rules = RechargeGiftRule.query.filter(
            RechargeGiftRule.is_active == True,
            RechargeGiftRule.min_amount <= recharge_amount
        ).order_by(RechargeGiftRule.min_amount.desc()).all()

        if not rules:
            return 0.0

        # 使用第一个匹配的规则（金额最高的规则）
        rule = rules[0]

        if rule.gift_type == 'percentage':
            # 按比例赠送
            return recharge_amount * (rule.gift_value / 100.0)
        elif rule.gift_type == 'fixed':
            # 固定金额赠送
            return rule.gift_value
        else:
            return 0.0

    except Exception as e:
        print(f"计算赠送金额失败: {str(e)}")
        return 0.0

def generate_barcode_base64(order_number, item_index=0, operator_name="", phone="", remarks="", defects=""):
    """根据订单号生成条形码的base64编码图片，优化布局以适应120mm*20mm的打印纸

    Args:
        order_number: 订单号
        item_index: 衣物索引
        operator_name: 营业员姓名
        phone: 客户手机号
        remarks: 备注内容
        defects: 瑕疵内容

    Returns:
        str: base64编码的条形码图片
    """
    try:
        # 确保PIL已安装
        if 'Image' not in globals() or 'barcode' not in globals():
            print("PIL库或barcode库未安装，无法生成条形码")
            return None

        # 确保订单号是纯数字并有6位
        order_number = str(order_number).replace('-', '').replace(' ', '')
        if not order_number.isdigit():
            order_number = ''.join(c for c in order_number if c.isdigit())
        if len(order_number) != 6:
            order_number = order_number.zfill(6)

        # 创建适合120mm*20mm尺寸的图像(6:1比例)
        width, height = 1200, 200  # 调整为符合打印纸比例的尺寸
        # 白色背景
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)

        # 使用更小的字体尺寸以适应窄高度
        try:
            small_font = ImageFont.truetype("arial.ttf", 14)
            medium_font = ImageFont.truetype("arial.ttf", 16)
            large_font = ImageFont.truetype("arial.ttf", 20)
        except Exception as e:
            # 如果找不到指定字体，尝试使用系统默认字体
            try:
                from PIL import ImageFont
                small_font = ImageFont.load_default()
                medium_font = ImageFont.load_default()
                large_font = ImageFont.load_default()
            except:
                small_font = None
                medium_font = None
                large_font = None
                print(f"加载字体错误: {str(e)}")

        # 不再绘制任何左侧信息，全部留给条形码使用

        # 使用python-barcode库生成条形码
        # 构建用于条形码的数据：使用完整订单号
        barcode_data = order_number + "-" + str(item_index+1).zfill(2)

        # 创建一个内存中的文件对象用于保存条形码图像
        barcode_buffer = io.BytesIO()

        # 使用Code128格式创建条形码
        code128 = barcode.get_barcode_class('code128')

        # 配置条形码写入器 - 调整条码高度以减少留白
        barcode_options = {
            'module_height': 80.0,  # 降低条码高度以适应15mm高度
            'module_width': 1.2,     # 调整条码宽度
            'quiet_zone': 1.0,       # 保持最小的两侧空白区
            'font_size': 0,          # 不显示条码下方文字
            'text_distance': 0.5,    # 最小化文字与条码的距离
        }
        barcode_writer = ImageWriter()
        for option, value in barcode_options.items():
            setattr(barcode_writer, option, value)

        # 生成条形码并保存到内存缓冲区
        barcode_image = code128(barcode_data, writer=barcode_writer)
        barcode_image.write(barcode_buffer)

        # 读取生成的条形码图像
        barcode_buffer.seek(0)
        barcode_img = Image.open(barcode_buffer)

        # 调整条形码图像大小以匹配整体布局
        barcode_width = 750  # 调整宽度为750像素 (75mm,基于120mm=1200像素)
        barcode_height = 150 # 调整高度为150像素 (15mm,基于20mm=200像素)
        barcode_img = barcode_img.resize((barcode_width, barcode_height), Image.LANCZOS)

        # 计算条形码放置位置（水平居中且垂直居中）
        barcode_x = (width - barcode_width) // 2  # 水平居中
        barcode_y = (height - barcode_height) // 2  # 垂直居中

        # 将条形码图像粘贴到主图像
        image.paste(barcode_img, (barcode_x, barcode_y))

        # 不添加额外文本，让HTML内的元素处理文本显示

        # 转换为base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode('utf-8')

        return f"data:image/png;base64,{img_str}"
    except Exception as e:
        print(f"生成条形码出错: {str(e)}")
        import traceback
        traceback.print_exc()
        # 出错时返回默认编码
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEzUlEQVR42u3dW2icRRjG8X9m1kRTA6YxSROVSiHSWlsrVpqLSm+sF1YIgVjw7EURRfBGBL2wiVgQFDz0QvBYRetZWxWpJ6SJxhJTLCHGJk3TJDY12WY/L7KJTZrdnf3m+6Zn5n3uFtJ8+5/nTHa/WUUIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCrBzKeAQFvArcAZQ5jOVfYAg4APwEeAZzF9AP3AtkgLXAReA4sAeYNJy9Cqw3nAnwO3Cs0HcnvQhewPxDWSlSQI5F1sBqcZhppHXQyxjMDNx0I7qS9MHGfqrAMEPAO8DXwHGgv9BXAluA24HngZuAswazU8AMkIXMSX3xY6/X6fI6D+YrjwCvAe8DHwLTBlNqCgXbAujPTrAKaASuL0QXMGooux5IFY6HtPZubWrzDrXfU9MBTndGQfSgn0zP7h8+c/HdSZt51VQANgvFQBroLfw0/QYcARoK10CuAd4ETlrKviawPRO8nF+8OQ10bZoFptLdnYMblH8gckXxRVc7AHVHXL86zHRVAdSwDxpXugEYSX8MnO0cjsZPdxR+P3p2Uy3+sUzvcAl+d7orSneNZnoHK6K9LcWXKh2AsN4yuwZoRGc758AbbQTv0dTL+W/5UUBnOpuj8fwJYH9L20LW6a7zcbSrpfhipQMQahbOAbQGb6zRBH+0CbyDjX4Ubz9WfH1fSywvz5nOUYNxcYYCaCzx2m9wUHy0g+DDJn86f9K/IXYSc9Lg3BxgMeXNAVZ07Q81ms6fzn8o1+RH8e/bYnl55r4uF3NzgEWl5gAVnwR+FHjRxmD92QfBx4326/ydTbH8wuP5iWDxZrCjeTYHqNgkcHnFx60+5wJR/GEz+F3Ns1F8W+y6vDzfnQtFwDU5wCWrGkB7fV4PRPGvLUT7Wv0o3tm8sJcf9GHvhiNzgIIYBmB9EpjuXvAc4Hfn5wA7mmJr51/j3dS7qZdL1uD/NKCRw8aLP9Dszztu7/tR/PHWhdnn/vCvON21qMw0Th+DXfPmAP9VBWB9Euh35yeBJ5r8rX1P84Isvzt/yRQ3s4ZLLCeP6dnB0twg/LLnAKoYAM9fBYCdSeDZhXOA0+1+d376k8UzYe4Pf3J2AOSTPsdN4pfDRuA04eXvUKoeQLhpwacV79sZnL+VH8U7Wjad4eNZv7w8r52dZJ9qHVQelKMbh4oHEP5iQVJg0CT/XFs/it92cmEvzymgO3eSVEcj/vXAy0CX4VijpgAiGIClOcC5JvCns2v60/n7Hqrxo/gTJ/JS/BRQHSs+1dqtQz+G39lkMrbYFUEACfA2C++XHt7P1H/+lj+df+TbxbP+k8CH+NP54VJrfxT3PhBnP/LBL6G8WBLyHoDT+wLaW3Z442n4/+38N/Kfx/GXlRYOgOH9AOQnlfPWzzr5Sw/n7yw6+auAV3CfX5PUFcDlW8SSnAP4e/WTkJ/E2aM88e0AJUcA60D72QfB7o7ZrXp4/1ZuDj9zPM4etdT5C4iiX73GmRmAjTt3Xb0fcDHgCDjNLmO2BACyACSe1LkLLwFIAAkf6tSFt+oD+A9urzIWW7mCBwAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyMC0wNi0wMlQxODoxNjozOSswMDowMGVRhRQAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMjAtMDYtMDJUMTg6MTY6MzkrMDA6MDAUDDyoAAAAAElFTkSuQmCC"

def process_refund(order, refund_amount, refund_reason, operator, refund_method='余额退回'):
    """处理订单退款

    Args:
        order: 订单对象
        refund_amount: 退款金额
        refund_reason: 退款原因
        operator: 操作员
        refund_method: 退款方式，默认为余额退回

    Returns:
        dict: 退款处理结果
    """
    from models import db, RefundRecord, Customer

    try:
        # 验证退款金额
        if refund_amount <= 0:
            return {'success': False, 'error': '退款金额必须大于0'}

        if refund_amount > order.total_amount:
            return {'success': False, 'error': '退款金额不能超过订单金额'}

        # 检查订单状态 - 检查支付状态来判断是否已退款
        if order.payment_status in ['已退款']:
            return {'success': False, 'error': '订单已经全额退款，不能重复退款'}

        # 获取客户信息
        customer = Customer.query.get(order.customer_id)
        if not customer:
            return {'success': False, 'error': '客户信息不存在'}

        # -----------------------------
        # 智能修正退款方式
        # -----------------------------
        # 如果未显式指定退款方式，根据订单支付方式推断最合适的退款方式。
        if not refund_method:
            if order.payment_method == '余额':
                refund_method = '余额退回'
            elif order.payment_method == '现金':
                refund_method = '现金退款'
            else:
                refund_method = '原路退回'

        # 针对"现金支付 + 会员从未充值"且仍要求"余额退回"的场景进行校正
        if refund_method == '余额退回' and order.payment_method == '现金':
            has_recharge = RechargeRecord.query.filter_by(customer_id=customer.id).count() > 0
            if not has_recharge:
                # 客户历史从未充值，自动改为现金退款
                refund_method = '现金退款'

        # 确定退款类型
        refund_type = '全额退款' if refund_amount == order.total_amount else '部分退款'

        # 处理不同支付方式的退款
        if order.payment_method == '余额' or refund_method == '余额退回':
            # 余额支付或选择余额退回，直接退回到客户余额
            customer.balance = (customer.balance or 0.0) + refund_amount
            actual_refund_method = '余额退回'
        else:
            # 其他支付方式，根据选择的退款方式处理
            if refund_method == '余额退回':
                customer.balance = (customer.balance or 0.0) + refund_amount
                actual_refund_method = '余额退回'
            else:
                # 现金退款或原路退回，不修改客户余额
                actual_refund_method = refund_method

        # 创建退款记录
        refund_record = RefundRecord(
            order_id=order.id,
            order_number=order.order_number,
            customer_id=order.customer_id,
            refund_amount=refund_amount,
            original_amount=order.total_amount,
            original_payment_method=order.payment_method,
            refund_method=actual_refund_method,
            refund_reason=refund_reason,
            refund_type=refund_type,
            operator=operator,
            processed_at=datetime.datetime.now()
        )

        # 更新订单状态和支付状态
        if refund_type == '全额退款':
            order.status = '已取消'  # 全额退款后订单状态改为已取消
            order.payment_status = '已退款'  # 全额退款时支付状态改为已退款
        else:
            # 部分退款时，支付状态改为部分退款
            order.payment_status = '部分退款'

        # 保存到数据库
        db.session.add(refund_record)
        db.session.commit()

        return {
            'success': True,
            'message': f'{refund_type}处理成功',
            'refund_record': {
                'id': refund_record.id,
                'refund_amount': refund_amount,
                'refund_method': actual_refund_method,
                'refund_type': refund_type,
                'new_balance': customer.total_balance if actual_refund_method == '余额退回' else None
            }
        }

    except Exception as e:
        db.session.rollback()
        return {'success': False, 'error': f'退款处理失败: {str(e)}'}

# === 设备检测工具函数 ===

def is_mobile_request(req):
    """根据User-Agent检测是否为移动端请求

    参数:
        req: Flask request对象

    返回:
        bool: True表示移动端，False表示桌面端
    """
    user_agent = req.headers.get('User-Agent', '').lower()
    mobile_patterns = [
        r'mobile', r'android', r'iphone', r'ipad', r'windows phone',
        r'blackberry', r'nokia', r'opera mini', r'samsung'
    ]
    
    for pattern in mobile_patterns:
        if re.search(pattern, user_agent):
            return True
    return False

# ========================================================================
# 输送线控制工具函数
# ========================================================================

def build_conveyor_command(side: str, slot_no: int) -> bytes:
    """构建输送线UDP指令
    
    Args:
        side: 'A' 或 'B' 表示输送线侧别
        slot_no: 目标格架号 (1-700)
    
    Returns:
        bytes: 6字节的UDP指令包
    """
    try:
        # 根据协议文档构建指令
        header = 0xC0  # 前缀
        
        # 功能字节：0xFA表示A侧，0xFB表示B侧
        if side.upper() == 'A':
            function = 0xFA
        elif side.upper() == 'B':
            function = 0xFB
        else:
            raise ValueError(f"无效的输送线侧别: {side}")
            
        # 目标格架号转换：Data1 = slot_no // 100, Data2 = slot_no % 100
        if not (1 <= slot_no <= 700):
            raise ValueError(f"格架号超出范围: {slot_no}")
            
        data1 = slot_no // 100
        data2 = slot_no % 100
        
        # 校验字节：Data1 与 Data2 按位与操作
        checksum = data1 & data2
        
        # 后缀
        trailer = 0xC1
        
        # 构建完整指令包
        command = struct.pack('BBBBBB', header, function, data1, data2, checksum, trailer)
        
        print(f"构建输送线指令: 侧别={side}, 格架={slot_no}, 指令={command.hex().upper()}")
        return command
        
    except Exception as e:
        print(f"构建输送线指令失败: {str(e)}")
        raise

def send_conveyor_command(ip: str, port: int, side: str, slot_no: int, timeout: float = 2.0) -> dict:
    """发送输送线UDP指令
    
    Args:
        ip: 输送线设备IP地址
        port: UDP端口
        side: 输送线侧别 'A' 或 'B'
        slot_no: 目标格架号
        timeout: 超时时间（秒）
    
    Returns:
        dict: 发送结果 {'success': bool, 'message': str, 'command': str}
    """
    try:
        # 构建指令
        command = build_conveyor_command(side, slot_no)
        command_hex = command.hex().upper()
        
        # 创建UDP套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(timeout)
        
        try:
            # 发送指令
            print(f"发送UDP指令到 {ip}:{port}, 内容: {command_hex}")
            sock.sendto(command, (ip, port))
            
            # 可选：接收设备回复（如果设备有回复机制）
            try:
                response, addr = sock.recvfrom(1024)
                response_hex = response.hex().upper()
                print(f"收到设备回复: {response_hex}")
                
                return {
                    'success': True,
                    'message': f'指令发送成功，设备回复: {response_hex}',
                    'command': command_hex,
                    'response': response_hex
                }
            except socket.timeout:
                # 设备无回复也视为成功（根据具体设备特性调整）
                return {
                    'success': True,
                    'message': '指令发送成功（设备无回复）',
                    'command': command_hex,
                    'response': None
                }
                
        finally:
            sock.close()
            
    except socket.timeout:
        return {
            'success': False,
            'message': f'发送超时（{timeout}秒）',
            'command': command_hex if 'command_hex' in locals() else '',
            'response': None
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'发送失败: {str(e)}',
            'command': command_hex if 'command_hex' in locals() else '',
            'response': None
        }

def get_conveyor_config(store_name: str = None) -> dict:
    """获取输送线配置

    Args:
        store_name: 门店名称，如果为空则使用默认配置

    Returns:
        dict: {'ip': str, 'port': int} 或 None
    """
    try:
        from models import ConveyorDeviceConfig
        
        # 从数据库获取配置
        if store_name:
            config = ConveyorDeviceConfig.query.filter_by(store_name=store_name, is_active=True).first()
        else:
            config = ConveyorDeviceConfig.query.filter_by(is_active=True).first()
        
        if config:
            result = {
                'ip': config.ip,
                'port': config.port,
                'max_slots': config.max_slots
            }
            print(f"获取输送线配置: 门店={config.store_name}, IP={config.ip}, 端口={config.port}")
            return result
        else:
            # 如果没有配置，返回默认配置
            default_config = {
                'ip': '*************',  # 根据实际设备IP修改
                'port': 8080,           # 根据实际端口修改
                'max_slots': 700
            }
            print(f"使用默认输送线配置: IP={default_config['ip']}, 端口={default_config['port']}")
            return default_config
        
    except Exception as e:
        print(f"获取输送线配置失败: {str(e)}")
        # 出错时返回默认配置
        return {
            'ip': '*************',
            'port': 8080,
            'max_slots': 700
        }

# ========================================================================
# 充值退充相关工具函数
# ========================================================================

class RefundCalculationError(Exception):
    """退充计算相关错误"""
    pass

class InsufficientBalanceError(RefundCalculationError):
    """余额不足错误"""
    pass

class RefundAmountExceedsLimitError(RefundCalculationError):
    """退充金额超限错误"""
    pass

def calculate_refundable_amount(recharge_record_id):
    """
    计算指定充值记录的可退充金额
    
    Args:
        recharge_record_id (int): 充值记录ID
        
    Returns:
        dict: 包含可退充信息的字典
    """
    try:
        # 获取充值记录
        recharge_record = RechargeRecord.query.get(recharge_record_id)
        if not recharge_record:
            raise RefundCalculationError(f"充值记录 {recharge_record_id} 不存在")
        
        # 获取客户信息
        customer = Customer.query.get(recharge_record.customer_id)
        if not customer:
            raise RefundCalculationError(f"客户 {recharge_record.customer_id} 不存在")
        
        # 检查是否已经退充过
        from models import RechargeRefund
        existing_refunds = RechargeRefund.query.filter_by(
            recharge_record_id=recharge_record_id,
            status='completed'
        ).all()
        
        total_refunded = sum(refund.refund_amount for refund in existing_refunds)
        # 注意：现有数据库结构中没有refund_gift_amount字段，暂时设为0
        total_gift_refunded = 0.0
        
        # 计算剩余可退充金额
        remaining_amount = recharge_record.amount - total_refunded
        remaining_gift_amount = (recharge_record.gift_amount or 0) - total_gift_refunded
        
        if remaining_amount <= 0:
            return {
                'refundable_amount': 0.0,
                'gift_amount_to_deduct': 0.0,
                'current_balance': customer.balance or 0.0,
                'current_gift_balance': customer.gift_balance or 0.0,
                'usage_details': {
                    'original_amount': recharge_record.amount,
                    'original_gift_amount': recharge_record.gift_amount or 0,
                    'total_refunded': total_refunded,
                    'total_gift_refunded': total_gift_refunded,
                    'remaining_amount': remaining_amount,
                    'remaining_gift_amount': remaining_gift_amount
                },
                'can_refund': False,
                'reason': '该充值记录已全额退充'
            }
        
        # 计算客户余额使用情况
        usage_info = calculate_balance_usage(customer, recharge_record)
        
        # 根据使用情况计算实际可退充金额
        actual_refundable = min(remaining_amount, customer.balance or 0.0)
        
        # 计算需要扣除的赠送金额（按比例）
        if remaining_amount > 0 and remaining_gift_amount > 0:
            gift_deduction_ratio = actual_refundable / remaining_amount
            gift_amount_to_deduct = remaining_gift_amount * gift_deduction_ratio
            # 确保不超过客户当前的赠送余额
            gift_amount_to_deduct = min(gift_amount_to_deduct, customer.gift_balance or 0.0)
        else:
            gift_amount_to_deduct = 0.0
        
        return {
            'refundable_amount': actual_refundable,
            'gift_amount_to_deduct': gift_amount_to_deduct,
            'current_balance': customer.balance or 0.0,
            'current_gift_balance': customer.gift_balance or 0.0,
            'usage_details': {
                'original_amount': recharge_record.amount,
                'original_gift_amount': recharge_record.gift_amount or 0,
                'total_refunded': total_refunded,
                'total_gift_refunded': total_gift_refunded,
                'remaining_amount': remaining_amount,
                'remaining_gift_amount': remaining_gift_amount,
                **usage_info
            },
            'can_refund': actual_refundable > 0,
            'reason': '可以退充' if actual_refundable > 0 else '客户余额不足'
        }
        
    except Exception as e:
        print(f"计算可退充金额失败: {e}")
        raise RefundCalculationError(f"计算可退充金额失败: {str(e)}")

def calculate_balance_usage(customer, recharge_record):
    """
    计算客户余额使用情况
    
    Args:
        customer: 客户对象
        recharge_record: 充值记录对象
        
    Returns:
        dict: 余额使用详情
    """
    try:
        # 计算充值后的消费总额
        orders_after_recharge = Order.query.filter(
            Order.customer_id == customer.id,
            Order.created_at >= recharge_record.created_at,
            Order.payment_method == '余额',
            Order.payment_status == '已付款'
        ).all()
        
        total_consumed = sum(order.total_amount for order in orders_after_recharge)
        
        # 计算充值后的其他退充金额
        from models import RechargeRefund
        other_refunds = RechargeRefund.query.join(RechargeRecord).filter(
            RechargeRecord.customer_id == customer.id,
            RechargeRecord.created_at >= recharge_record.created_at,
            RechargeRefund.recharge_record_id != recharge_record.id,
            RechargeRefund.status == 'completed'
        ).all()
        
        other_refund_amount = sum(refund.refund_amount for refund in other_refunds)
        
        return {
            'orders_count_after_recharge': len(orders_after_recharge),
            'total_consumed_after_recharge': total_consumed,
            'other_refunds_after_recharge': other_refund_amount,
            'available_for_refund': max(0, (customer.balance or 0) - total_consumed)
        }
        
    except Exception as e:
        print(f"计算余额使用情况失败: {e}")
        return {
            'orders_count_after_recharge': 0,
            'total_consumed_after_recharge': 0.0,
            'other_refunds_after_recharge': 0.0,
            'available_for_refund': customer.balance or 0.0
        }

def validate_refund_amount(recharge_record_id, refund_amount):
    """
    验证退充金额是否有效
    
    Args:
        recharge_record_id (int): 充值记录ID
        refund_amount (float): 要退充的金额
        
    Returns:
        dict: 验证结果
    """
    try:
        if refund_amount <= 0:
            return {
                'valid': False,
                'message': '退充金额必须大于0',
                'max_refundable': 0.0
            }
        
        # 获取可退充信息
        refund_info = calculate_refundable_amount(recharge_record_id)
        
        if not refund_info['can_refund']:
            return {
                'valid': False,
                'message': refund_info.get('reason', '无法退充'),
                'max_refundable': 0.0
            }
        
        max_refundable = refund_info['refundable_amount']
        
        if refund_amount > max_refundable:
            return {
                'valid': False,
                'message': f'退充金额不能超过最大可退充金额 ¥{max_refundable:.2f}',
                'max_refundable': max_refundable
            }
        
        return {
            'valid': True,
            'message': '退充金额有效',
            'max_refundable': max_refundable
        }
        
    except Exception as e:
        print(f"验证退充金额失败: {e}")
        return {
            'valid': False,
            'message': f'验证失败: {str(e)}',
            'max_refundable': 0.0
        }

def check_customer_balance_sufficient(customer_id, refund_amount, gift_amount_to_deduct=0.0):
    """
    检查客户余额是否足够扣除退充金额
    
    Args:
        customer_id (int): 客户ID
        refund_amount (float): 退充金额
        gift_amount_to_deduct (float): 需要扣除的赠送金额
        
    Returns:
        dict: 检查结果
    """
    try:
        customer = Customer.query.get(customer_id)
        if not customer:
            return {
                'sufficient': False,
                'current_balance': 0.0,
                'current_gift_balance': 0.0,
                'balance_after_refund': 0.0,
                'gift_balance_after_refund': 0.0,
                'message': '客户不存在'
            }
        
        current_balance = customer.balance or 0.0
        current_gift_balance = customer.gift_balance or 0.0
        
        # 检查充值余额是否足够
        if current_balance < refund_amount:
            return {
                'sufficient': False,
                'current_balance': current_balance,
                'current_gift_balance': current_gift_balance,
                'balance_after_refund': current_balance,
                'gift_balance_after_refund': current_gift_balance,
                'message': f'充值余额不足，当前余额 ¥{current_balance:.2f}，需要 ¥{refund_amount:.2f}'
            }
        
        # 检查赠送余额是否足够
        if current_gift_balance < gift_amount_to_deduct:
            return {
                'sufficient': False,
                'current_balance': current_balance,
                'current_gift_balance': current_gift_balance,
                'balance_after_refund': current_balance,
                'gift_balance_after_refund': current_gift_balance,
                'message': f'赠送余额不足，当前赠送余额 ¥{current_gift_balance:.2f}，需要扣除 ¥{gift_amount_to_deduct:.2f}'
            }
        
        return {
            'sufficient': True,
            'current_balance': current_balance,
            'current_gift_balance': current_gift_balance,
            'balance_after_refund': current_balance - refund_amount,
            'gift_balance_after_refund': current_gift_balance - gift_amount_to_deduct,
            'message': '余额充足'
        }
        
    except Exception as e:
        print(f"检查客户余额失败: {e}")
        return {
            'sufficient': False,
            'current_balance': 0.0,
            'current_gift_balance': 0.0,
            'balance_after_refund': 0.0,
            'gift_balance_after_refund': 0.0,
            'message': f'检查失败: {str(e)}'
        }

def calculate_gift_deduction_amount(original_amount, original_gift_amount, refund_amount):
    """
    计算退充时需要扣除的赠送金额
    
    Args:
        original_amount (float): 原充值金额
        original_gift_amount (float): 原赠送金额
        refund_amount (float): 退充金额
        
    Returns:
        float: 需要扣除的赠送金额
    """
    try:
        if original_amount <= 0 or original_gift_amount <= 0 or refund_amount <= 0:
            return 0.0
        
        # 按比例计算需要扣除的赠送金额
        deduction_ratio = refund_amount / original_amount
        gift_deduction = original_gift_amount * deduction_ratio
        
        return round(gift_deduction, 2)
        
    except Exception as e:
        print(f"计算赠送金额扣除失败: {e}")
        return 0.0

def execute_recharge_refund(recharge_record_id, refund_amount, refund_reason, operator, approved_by=None):
    """
    执行充值退充操作
    
    Args:
        recharge_record_id (int): 充值记录ID
        refund_amount (float): 退充金额
        refund_reason (str): 退充原因
        operator (str): 操作员
        approved_by (str, optional): 审批人
        
    Returns:
        dict: 操作结果
        {
            'success': bool,
            'refund_id': int,
            'message': str,
            'balance_after_refund': float,
            'gift_balance_after_refund': float
        }
    """
    try:
        # 数据库事务会自动开始，无需手动调用 begin()
        
        # 1. 验证退充金额
        validation_result = validate_refund_amount(recharge_record_id, refund_amount)
        if not validation_result['valid']:
            db.session.rollback()
            return {
                'success': False,
                'refund_id': None,
                'message': validation_result['message'],
                'balance_after_refund': 0.0,
                'gift_balance_after_refund': 0.0
            }
        
        # 2. 获取充值记录和客户信息
        recharge_record = RechargeRecord.query.get(recharge_record_id)
        customer = Customer.query.get(recharge_record.customer_id)
        
        # 3. 计算需要扣除的赠送金额
        refund_info = calculate_refundable_amount(recharge_record_id)
        gift_amount_to_deduct = calculate_gift_deduction_amount(
            refund_info['usage_details']['remaining_amount'],
            refund_info['usage_details']['remaining_gift_amount'],
            refund_amount
        )
        
        # 4. 检查客户余额是否充足
        balance_check = check_customer_balance_sufficient(
            customer.id, refund_amount, gift_amount_to_deduct
        )
        if not balance_check['sufficient']:
            db.session.rollback()
            return {
                'success': False,
                'refund_id': None,
                'message': balance_check['message'],
                'balance_after_refund': balance_check['current_balance'],
                'gift_balance_after_refund': balance_check['current_gift_balance']
            }
        
        # 5. 创建退充记录
        from models import RechargeRefund
        refund_record = RechargeRefund(
            recharge_record_id=recharge_record_id,
            customer_id=customer.id,
            refund_amount=refund_amount,
            original_amount=recharge_record.amount,  # 记录原始充值金额
            refund_reason=refund_reason,
            refund_method='余额退回',
            operator=operator,
            approved_by=approved_by,
            status='completed',
            processed_at=datetime.datetime.now()
        )
        db.session.add(refund_record)
        db.session.flush()  # 获取退充记录ID
        
        # 6. 更新客户余额
        customer.balance = (customer.balance or 0.0) - refund_amount
        customer.gift_balance = (customer.gift_balance or 0.0) - gift_amount_to_deduct
        
        # 7. 记录操作日志
        log_recharge_refund_operation(
            refund_record.id,
            'refund_created',
            operator,
            {
                'original_recharge_id': recharge_record_id,
                'refund_amount': refund_amount,
                'gift_amount_deducted': gift_amount_to_deduct,
                'customer_id': customer.id,
                'reason': refund_reason
            }
        )
        
        # 8. 提交事务
        db.session.commit()
        
        return {
            'success': True,
            'refund_id': refund_record.id,
            'message': '退充操作成功',
            'balance_after_refund': customer.balance,
            'gift_balance_after_refund': customer.gift_balance
        }
        
    except Exception as e:
        db.session.rollback()
        print(f"执行退充操作失败: {e}")
        return {
            'success': False,
            'refund_id': None,
            'message': f'退充操作失败: {str(e)}',
            'balance_after_refund': 0.0,
            'gift_balance_after_refund': 0.0
        }

def log_recharge_refund_operation(refund_id, operation_type, operator, operation_data):
    """
    记录退充操作日志
    
    Args:
        refund_id (int): 退充记录ID
        operation_type (str): 操作类型
        operator (str): 操作员
        operation_data (dict): 操作数据
    """
    try:
        import json
        import logging
        
        # 配置日志记录器
        logger = logging.getLogger('recharge_refund')
        if not logger.handlers:
            handler = logging.FileHandler('logs/recharge_refund_operations.log', encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        
        # 记录操作日志
        log_data = {
            'refund_id': refund_id,
            'operation_type': operation_type,
            'operator': operator,
            'timestamp': datetime.datetime.now().isoformat(),
            'operation_data': operation_data
        }
        
        logger.info(f"退充操作: {json.dumps(log_data, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"记录退充操作日志失败: {e}")

def get_refund_record_details(refund_id):
    """
    获取退充记录详情
    
    Args:
        refund_id (int): 退充记录ID
        
    Returns:
        dict: 退充记录详情
    """
    try:
        from models import RechargeRefund
        
        refund_record = RechargeRefund.query.get(refund_id)
        if not refund_record:
            return None
        
        # 获取原充值记录
        original_recharge = RechargeRecord.query.get(refund_record.original_recharge_id)
        
        # 获取客户信息
        customer = Customer.query.get(refund_record.customer_id)
        
        return {
            'refund_id': refund_record.id,
            'original_recharge_id': refund_record.original_recharge_id,
            'customer_id': refund_record.customer_id,
            'customer_name': customer.name if customer else '未知',
            'customer_phone': customer.phone if customer else '未知',
            'refund_amount': refund_record.refund_amount,
            'refund_gift_amount': refund_record.refund_gift_amount,
            'refund_reason': refund_record.refund_reason,
            'operator': refund_record.operator,
            'approved_by': refund_record.approved_by,
            'status': refund_record.status,
            'created_at': refund_record.created_at.isoformat(),
            'original_recharge': {
                'amount': original_recharge.amount if original_recharge else 0.0,
                'gift_amount': original_recharge.gift_amount if original_recharge else 0.0,
                'payment_method': original_recharge.payment_method if original_recharge else '未知',
                'created_at': original_recharge.created_at.isoformat() if original_recharge else None
            }
        }
        
    except Exception as e:
        print(f"获取退充记录详情失败: {e}")
        return None

def cancel_recharge_refund(refund_id, operator, cancel_reason):
    """
    取消退充操作（仅限未完成的退充）
    
    Args:
        refund_id (int): 退充记录ID
        operator (str): 操作员
        cancel_reason (str): 取消原因
        
    Returns:
        dict: 操作结果
    """
    try:
        db.session.begin()
        
        from models import RechargeRefund
        refund_record = RechargeRefund.query.get(refund_id)
        
        if not refund_record:
            db.session.rollback()
            return {
                'success': False,
                'message': '退充记录不存在'
            }
        
        if refund_record.status == 'cancelled':
            db.session.rollback()
            return {
                'success': False,
                'message': '退充记录已经被取消'
            }
        
        if refund_record.status == 'completed':
            # 如果已完成，需要恢复客户余额
            customer = Customer.query.get(refund_record.customer_id)
            customer.balance = (customer.balance or 0.0) + refund_record.refund_amount
            customer.gift_balance = (customer.gift_balance or 0.0) + refund_record.refund_gift_amount
        
        # 更新退充记录状态
        refund_record.status = 'cancelled'
        
        # 记录取消操作日志
        log_recharge_refund_operation(
            refund_id,
            'refund_cancelled',
            operator,
            {
                'cancel_reason': cancel_reason,
                'refund_amount': refund_record.refund_amount,
                'gift_amount': refund_record.refund_gift_amount
            }
        )
        
        db.session.commit()
        
        return {
            'success': True,
            'message': '退充操作已取消'
        }
        
    except Exception as e:
        db.session.rollback()
        print(f"取消退充操作失败: {e}")
        return {
            'success': False,
            'message': f'取消退充操作失败: {str(e)}'
        }

def check_refund_permission(staff_name, refund_amount=0.0):
    """
    检查员工是否有退充权限
    
    Args:
        staff_name (str): 员工姓名
        refund_amount (float): 退充金额
        
    Returns:
        dict: 权限检查结果
        {
            'has_permission': bool,
            'max_refund_amount': float,
            'message': str,
            'requires_approval': bool
        }
    """
    try:
        from models import Staff
        
        staff = Staff.query.filter_by(name=staff_name).first()
        if not staff:
            return {
                'has_permission': False,
                'max_refund_amount': 0.0,
                'message': '员工不存在',
                'requires_approval': False
            }
        
        # 检查是否有退充权限
        if not staff.can_refund_recharge:
            return {
                'has_permission': False,
                'max_refund_amount': 0.0,
                'message': '您没有退充权限，请联系管理员',
                'requires_approval': False
            }
        
        # 检查金额限制
        max_amount = staff.max_refund_amount or 0.0
        
        if refund_amount > 0:
            if refund_amount > max_amount:
                # 检查是否需要审批
                if max_amount > 0:
                    return {
                        'has_permission': True,
                        'max_refund_amount': max_amount,
                        'message': f'退充金额超过您的权限限制（¥{max_amount:.2f}），需要管理员审批',
                        'requires_approval': True
                    }
                else:
                    return {
                        'has_permission': False,
                        'max_refund_amount': max_amount,
                        'message': '您没有退充权限',
                        'requires_approval': False
                    }
        
        return {
            'has_permission': True,
            'max_refund_amount': max_amount,
            'message': '有退充权限',
            'requires_approval': False
        }
        
    except Exception as e:
        print(f"检查退充权限失败: {e}")
        return {
            'has_permission': False,
            'max_refund_amount': 0.0,
            'message': f'权限检查失败: {str(e)}',
            'requires_approval': False
        }

def check_area_permission(staff_name, customer_id):
    """
    检查员工是否有权限操作指定客户的退充
    
    Args:
        staff_name (str): 员工姓名
        customer_id (int): 客户ID
        
    Returns:
        dict: 权限检查结果
    """
    try:
        from models import Staff, Customer, Order
        
        staff = Staff.query.filter_by(name=staff_name).first()
        if not staff:
            return {
                'has_permission': False,
                'message': '员工不存在'
            }
        
        # 管理员和总部员工有全部权限
        if staff.role == 'manager' and (not staff.area or staff.area == '总部'):
            return {
                'has_permission': True,
                'message': '管理员权限'
            }
        
        # 区域管理员只能操作本区域客户
        if staff.role == 'manager' and staff.area:
            # 通过客户的订单查找客户所属区域
            customer_orders = Order.query.filter_by(customer_id=customer_id).first()
            if customer_orders and customer_orders.area == staff.area:
                return {
                    'has_permission': True,
                    'message': '区域管理员权限'
                }
            else:
                return {
                    'has_permission': False,
                    'message': '您只能操作本区域客户的退充'
                }
        
        # 普通员工只能操作自己服务的客户
        if staff.role == 'staff':
            # 检查是否有该客户的充值记录
            customer_recharge = RechargeRecord.query.filter_by(
                customer_id=customer_id,
                operator=staff_name
            ).first()
            
            if customer_recharge:
                return {
                    'has_permission': True,
                    'message': '员工权限'
                }
            else:
                return {
                    'has_permission': False,
                    'message': '您只能操作自己服务的客户退充'
                }
        
        return {
            'has_permission': False,
            'message': '权限不足'
        }
        
    except Exception as e:
        print(f"检查区域权限失败: {e}")
        return {
            'has_permission': False,
            'message': f'权限检查失败: {str(e)}'
        }

def validate_refund_operation(staff_name, recharge_record_id, refund_amount):
    """
    综合验证退充操作权限
    
    Args:
        staff_name (str): 员工姓名
        recharge_record_id (int): 充值记录ID
        refund_amount (float): 退充金额
        
    Returns:
        dict: 验证结果
        {
            'valid': bool,
            'message': str,
            'requires_approval': bool,
            'max_refund_amount': float
        }
    """
    try:
        # 1. 检查充值记录是否存在
        recharge_record = RechargeRecord.query.get(recharge_record_id)
        if not recharge_record:
            return {
                'valid': False,
                'message': '充值记录不存在',
                'requires_approval': False,
                'max_refund_amount': 0.0
            }
        
        # 2. 检查基本退充权限
        permission_check = check_refund_permission(staff_name, refund_amount)
        if not permission_check['has_permission']:
            return {
                'valid': False,
                'message': permission_check['message'],
                'requires_approval': False,
                'max_refund_amount': permission_check['max_refund_amount']
            }
        
        # 3. 检查区域权限
        area_check = check_area_permission(staff_name, recharge_record.customer_id)
        if not area_check['has_permission']:
            return {
                'valid': False,
                'message': area_check['message'],
                'requires_approval': False,
                'max_refund_amount': permission_check['max_refund_amount']
            }
        
        # 4. 检查退充金额是否有效
        amount_check = validate_refund_amount(recharge_record_id, refund_amount)
        if not amount_check['valid']:
            return {
                'valid': False,
                'message': amount_check['message'],
                'requires_approval': False,
                'max_refund_amount': amount_check['max_refundable']
            }
        
        return {
            'valid': True,
            'message': '验证通过',
            'requires_approval': permission_check['requires_approval'],
            'max_refund_amount': permission_check['max_refund_amount']
        }
        
    except Exception as e:
        print(f"验证退充操作失败: {e}")
        return {
            'valid': False,
            'message': f'验证失败: {str(e)}',
            'requires_approval': False,
            'max_refund_amount': 0.0
        }

def get_staff_refund_permissions(staff_name):
    """
    获取员工的退充权限详情
    
    Args:
        staff_name (str): 员工姓名
        
    Returns:
        dict: 权限详情
    """
    try:
        from models import Staff
        
        staff = Staff.query.filter_by(name=staff_name).first()
        if not staff:
            return {
                'exists': False,
                'can_refund': False,
                'max_amount': 0.0,
                'role': None,
                'area': None
            }
        
        return {
            'exists': True,
            'can_refund': staff.can_refund_recharge or False,
            'max_amount': staff.max_refund_amount or 0.0,
            'role': staff.role,
            'area': staff.area,
            'is_active': staff.is_active
        }
        
    except Exception as e:
        print(f"获取员工退充权限失败: {e}")
        return {
            'exists': False,
            'can_refund': False,
            'max_amount': 0.0,
            'role': None,
            'area': None
        }