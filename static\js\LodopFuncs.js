//====页面引用的Lodop基础函数定义：====
let CreatedOKLodopObject, CLodopIsLocal, CLodopJsState;

//==判断是否需要安装CLodop云打印服务器:==
function needCLodop() {
    try {
        let ua = navigator.userAgent;
        if (ua.match(/Windows\sPhone/i))
            return true;
        if (ua.match(/iPhone|iPod/i))
            return true;
        if (ua.match(/Android/i))
            return true;
        if (ua.match(/Edge\D?\d+/i))
            return true;

        let verTrident = ua.match(/Trident\D?\d+/i);
        let verIE = ua.match(/MSIE\D?\d+/i);
        let verOPR = ua.match(/OPR\D?\d+/i);
        let verFF = ua.match(/Firefox\D?\d+/i);
        let x64 = ua.match(/x64/i);
        if ((!verTrident) && (!verIE) && (x64))
            return true;
        else if (verFF) {
            verFF = verFF[0].match(/\d+/);
            if ((verFF[0] >= 41) || (x64))
                return true;
        } else if (verOPR) {
            verOPR = verOPR[0].match(/\d+/);
            if (verOPR[0] >= 32)
                return true;
        } else if ((!verTrident) && (!verIE)) {
            let verChrome = ua.match(/Chrome\D?\d+/i);
            if (verChrome) {
                verChrome = verChrome[0].match(/\d+/);
                if (verChrome[0] >= 41)
                    return true;
            }
        }
        return false;
    } catch (err) {
        return true;
    }
}

//==加载引用CLodop云打印必须的JS文件：==
if (needCLodop()) {
    let head = document.head || document.getElementsByTagName("head")[0] || document.documentElement;
    let oscript = document.createElement("script");
    oscript.src = "http://localhost:8000/CLodopfuncs.js?priority=1";
    head.insertBefore(oscript, head.firstChild);

    //引用双端口(8000和18000)避免其中某个被占用：
    oscript = document.createElement("script");
    oscript.src = "http://localhost:18000/CLodopfuncs.js?priority=0";
    head.insertBefore(oscript, head.firstChild);
}

//==获取LODOP对象主过程：==
function getLodop(oOBJECT, oEMBED) {
    let strHtmInstall = "<br><font color='#FF00FF'>打印控件未安装!点击这里<a href='install_lodop32.exe'>执行安装</a>,安装后请刷新页面或重新进入。</font>";
    let strHtmUpdate = "<br><font color='#FF00FF'>打印控件需要升级!点击这里<a href='install_lodop32.exe'>执行升级</a>,升级后请重新进入。</font>";
    let strHtm64_Install = "<br><font color='#FF00FF'>打印控件未安装!点击这里<a href='install_lodop64.exe'>执行安装</a>,安装后请刷新页面或重新进入。</font>";
    let strHtm64_Update = "<br><font color='#FF00FF'>打印控件需要升级!点击这里<a href='install_lodop64.exe'>执行升级</a>,升级后请重新进入。</font>";

    let strHtmFireFox = "<br><br><font color='#FF00FF'>（注意：如曾安装过Lodop旧版附件npActiveXPLugin可能导致冲突，需重新安装）</font>";
    let strHtmChrome = "<br><br><font color='#FF00FF'>（如果此前正常，仅因浏览器升级或重安装而出问题，需重新执行以上安装）</font>";
    let strCLodopInstall_1 = "<br><font color='#FF00FF'>CLodop云打印服务(localhost本地)未安装启动!点击这里<a href='CLodop_Setup_for_Win32NT.exe'>执行安装</a>,安装后请刷新页面。</font>";
    let strCLodopInstall_2 = "<br><font color='#FF00FF'>CLodop云打印服务(localhost本地)未安装启动!点击这里<a href='CLodop_Setup_for_Win32NT.exe'>执行安装</a>，安装后请刷新页面。</font>";
    let strCLodopInstall_3 = "<br><font color='#FF00FF'>CLodop云打印服务(localhost本地)未安装启动!点击这里<a href='CLodop_Setup_for_Win32NT.exe'>执行安装</a>，安装后请刷新页面。</font>";

    //=====判断浏览器类型:=====
    let isIE = (navigator.userAgent.indexOf('MSIE') >= 0) || (navigator.userAgent.indexOf('Trident') >= 0);
    let isFireFox = (navigator.userAgent.indexOf('Firefox') >= 0);
    let isChrome = (navigator.userAgent.indexOf('Chrome') >= 0);
    let isEdge = (navigator.userAgent.indexOf('Edge') >= 0);

    //==如果页面有Lodop就直接使用，否则新建:==
    if ((typeof (LODOP) == "object") || (typeof (LODOP) == "function")) {
        if (LODOP.VERSION) {
            if (LODOP.VERSION >= "*******") {
                return LODOP;
            } else if (confirm("打印控件Lodop需要升级!点击确定开始升级,升级后请刷新页面或重新进入。")) {
                if (isIE)
                    window.location = "install_lodop32.exe";
                else
                    window.location = "install_lodop64.exe";
            }
            return LODOP;
        } else {
            return LODOP;
        }
    }

    if (needCLodop()) {
        try {
            let ua = navigator.userAgent;
            if (ua.indexOf('Windows NT') >= 0) {
                try {
                    let oWS = new ActiveXObject("WScript.Shell");
                    oWS.RegRead("HKEY_CURRENT_USER\\Software\\Microsoft\\Internet Explorer\\BrowserEmulation\\CVListTTL");
                } catch (err) {
                }
            }
            if (window.getCLodop) {
                CLodopIsLocal = getCLodop().CVERSION;
                if (CLodopIsLocal.indexOf("C-Lodop") >= 0) {
                    if (CLodopIsLocal < "C-Lodop_V3.8.9.7")
                        CLodopJsState = "晚版本C-Lodop_JS";
                    else
                        CLodopJsState = "上线后使用C-Lodop云打印";
                }
            }
        } catch (err) {
            CLodopJsState = "未曾加载C-Lodop_JS";
        }

        if (CLodopIsLocal) {
            try {
                CreatedOKLodopObject = getCLodop();
                if (CreatedOKLodopObject.VERSION) {
                    if (CreatedOKLodopObject.VERSION >= "*******") {
                        return CreatedOKLodopObject;
                    }
                }
            } catch (err) {
                CLodopJsState = "创建"+ err;
            }
        }

        if (document.readyState !== "complete") {
            console.error("C-Lodop没有发现系统下载！");
        } else {
            document.documentElement.innerHTML = strCLodopInstall_1 + document.documentElement.innerHTML;
        }
        return;
    }

    //==检查ActiveX对象是否可用:==
    try {
        let oOBJECT_lodop = document.getElementById("LODOP_OB") || oOBJECT;
        let oEMBED_lodop = document.getElementById("LODOP_EM") || oEMBED;
        if (oEMBED_lodop && typeof(oEMBED_lodop.VERSION) != "undefined") {
            CreatedOKLodopObject = oEMBED_lodop;
        } else if (oOBJECT_lodop && typeof(oOBJECT_lodop.VERSION) != "undefined") {
            CreatedOKLodopObject = oOBJECT_lodop;
        }
    } catch (err) {
    }

    //==如果创建对象成功则判断版本:==
    if (CreatedOKLodopObject != null) {
        try {
            if (CreatedOKLodopObject.VERSION >= "*******") {
                return CreatedOKLodopObject;
            } else if (confirm("本机已安装了打印控件，但版本较低，点击确定开始升级！")) {
                if (isIE)
                    window.location = "install_lodop32.exe";
                else
                    window.location = "install_lodop64.exe";
            }
            return CreatedOKLodopObject;
        } catch (err) {
            if (confirm("本机曾安装过打印控件，但加载异常，点击确定开始重新安装！")) {
                if (isIE)
                    window.location = "install_lodop32.exe";
                else
                    window.location = "install_lodop64.exe";
            }
            return CreatedOKLodopObject;
        }
    }

    if (isIE) {
        document.documentElement.innerHTML = strHtmInstall + document.documentElement.innerHTML;
    } else {
        document.documentElement.innerHTML = strHtm64_Install + document.documentElement.innerHTML;
    }

    return null;
}

//==下面是网页自用的其它函数，可根据需要修改或删除：==

//==检查打印插件或服务是否可用并实例化:==
function checkLodop() {
    if (needCLodop()) {
        if (!window.getCLodop) {
            console.error("CLodop云打印服务未启动，请确认已安装且启动CLodop云打印服务");
            return false;
        }
        
        try {
            LODOP = getCLodop();
            if (!LODOP || !LODOP.VERSION) {
                console.error("获取CLodop对象失败");
                return false;
            }
            
            if (LODOP.VERSION < "*******") {
                console.warn("CLodop版本较低，建议升级到最新版本");
            }
            
            console.log("CLodop云打印服务已就绪，版本:", LODOP.VERSION);
            // === Lodop 授权注册 ===
            try {
                // 请勿删除以下授权注册语句，替换为您自己的注册号时仅修改第二个参数
                LODOP.SET_LICENSES("","B9D8783FE186D9D21B7E0DAC276BF5CB","","");
            } catch (e) {
                console.warn("LODOP.SET_LICENSES 调用失败：", e);
            }
            return true;
        } catch (err) {
            console.error("CLodop初始化失败:", err);
            return false;
        }
    } else {
        LODOP = getLodop();
        if (LODOP != null) {
            console.log("Lodop打印控件已就绪，版本:", LODOP.VERSION);
            // === Lodop 授权注册 ===
            try {
                LODOP.SET_LICENSES("","B9D8783FE186D9D21B7E0DAC276BF5CB","","");
            } catch (e) {
                console.warn("LODOP.SET_LICENSES 调用失败：", e);
            }
            return true;
        } else {
            console.error("Lodop打印控件加载失败");
            return false;
        }
    }
}

//==页面加载完成后检查插件:==
if (document.readyState === "complete") {
    checkLodop();
} else {
    window.addEventListener('load', function() {
        setTimeout(checkLodop, 500);
    });
} 