# 充值退充和小票补打印功能需求文档

## 介绍

本功能旨在为现有的充值系统增加退充功能和小票补打印功能，以提供更完善的充值管理体验。退充功能允许营业员在特定情况下对客户的充值进行退款操作，小票补打印功能允许重新打印历史充值记录的小票。

## 需求

### 需求1：充值退充功能

**用户故事：** 作为营业员，我希望能够对客户的充值记录进行退充操作，以便在客户要求退款或操作错误时能够及时处理。

#### 验收标准

1. WHEN 营业员在充值记录列表中选择一条充值记录 THEN 系统应显示"退充"操作按钮
2. WHEN 营业员点击"退充"按钮 THEN 系统应弹出退充确认对话框，显示充值详情和退充金额
3. WHEN 营业员确认退充操作 THEN 系统应创建退充记录并更新客户余额
4. WHEN 退充操作完成 THEN 系统应自动打印退充小票
5. IF 充值记录已经被部分或全部使用 THEN 系统应显示可退充金额和限制说明
6. IF 充值记录包含赠送金额 THEN 系统应按比例扣除赠送金额
7. WHEN 营业员没有退充权限 THEN 系统应隐藏退充操作按钮
8. WHEN 充值记录已经退充过 THEN 系统应显示退充状态并禁用再次退充

### 需求2：退充权限管理

**用户故事：** 作为管理员，我希望能够控制哪些员工可以执行退充操作，以确保资金安全。

#### 验收标准

1. WHEN 系统初始化时 THEN 应为员工角色添加退充权限字段
2. WHEN 管理员编辑员工信息时 THEN 应能够设置员工的退充权限
3. IF 员工没有退充权限 THEN 系统不应显示任何退充相关功能
4. WHEN 员工尝试执行退充操作但没有权限时 THEN 系统应返回权限错误

### 需求3：退充记录管理

**用户故事：** 作为营业员，我希望能够查看和管理退充记录，以便跟踪退充操作的历史。

#### 验收标准

1. WHEN 营业员访问充值记录页面 THEN 系统应显示退充记录标签页
2. WHEN 营业员查看退充记录 THEN 系统应显示退充时间、金额、原因、操作员等信息
3. WHEN 营业员筛选退充记录 THEN 系统应支持按日期、客户、操作员等条件筛选
4. WHEN 营业员导出退充记录 THEN 系统应生成Excel格式的退充明细报表
5. IF 营业员是普通员工 THEN 只能查看自己操作的退充记录
6. IF 营业员是区域管理员 THEN 只能查看本区域的退充记录

### 需求4：小票补打印功能

**用户故事：** 作为营业员，我希望能够重新打印历史充值记录的小票，以便在客户需要时提供小票副本。

#### 验收标准

1. WHEN 营业员在充值记录列表中选择一条记录 THEN 系统应显示"补打印"操作按钮
2. WHEN 营业员点击"补打印"按钮 THEN 系统应立即打印该充值记录的小票
3. WHEN 补打印操作执行时 THEN 小票应标注"补打印"字样和补打印时间
4. WHEN 补打印完成 THEN 系统应记录补打印操作日志
5. IF 打印机不可用 THEN 系统应提供网页打印备选方案
6. WHEN 营业员补打印退充记录 THEN 系统应打印退充小票格式

### 需求5：退充小票打印

**用户故事：** 作为营业员，我希望退充操作完成后能够自动打印退充小票，以便为客户提供退充凭证。

#### 验收标准

1. WHEN 退充操作完成 THEN 系统应自动打印退充小票
2. WHEN 打印退充小票 THEN 小票应包含退充金额、原充值信息、退充时间、操作员等信息
3. WHEN 退充小票打印 THEN 应使用与充值小票相同的打印机和格式
4. IF 退充包含赠送金额扣除 THEN 小票应详细显示扣除明细
5. WHEN 退充小票打印失败 THEN 系统应提供重新打印选项

### 需求6：客户余额处理

**用户故事：** 作为系统，我需要正确处理退充时的客户余额变动，确保账务准确。

#### 验收标准

1. WHEN 执行退充操作 THEN 系统应从客户余额中扣除退充金额
2. IF 客户余额不足 THEN 系统应显示余额不足提示并阻止退充
3. WHEN 退充包含赠送金额 THEN 系统应按比例从赠送余额中扣除
4. WHEN 退充操作完成 THEN 系统应更新客户的充值余额和赠送余额
5. IF 退充金额超过可用余额 THEN 系统应计算并显示最大可退充金额

### 需求7：操作日志和审计

**用户故事：** 作为管理员，我希望系统能够详细记录所有退充和补打印操作，以便进行审计和问题追踪。

#### 验收标准

1. WHEN 执行退充操作 THEN 系统应记录详细的操作日志
2. WHEN 执行补打印操作 THEN 系统应记录补打印日志
3. WHEN 记录操作日志 THEN 应包含操作时间、操作员、客户信息、金额、IP地址等
4. WHEN 管理员查看日志 THEN 系统应提供日志查询和导出功能
5. WHEN 发生异常操作 THEN 系统应记录错误日志并发送告警

### 需求8：数据完整性和安全性

**用户故事：** 作为系统管理员，我希望确保退充操作的数据完整性和安全性，防止恶意操作。

#### 验收标准

1. WHEN 执行退充操作 THEN 系统应使用数据库事务确保操作原子性
2. WHEN 退充操作失败 THEN 系统应回滚所有相关数据变更
3. WHEN 检测到异常退充模式 THEN 系统应触发安全告警
4. WHEN 执行大额退充 THEN 系统应要求额外的管理员确认
5. IF 同一客户短时间内多次退充 THEN 系统应标记为可疑操作并记录