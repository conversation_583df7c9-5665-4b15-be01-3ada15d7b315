/**
 * 充值退充小票打印模块
 * 包含退充小票打印和补打印功能
 * 版本: 1.0.0
 * 创建日期: 2025-01-15
 */

console.log('充值退充小票打印模块已加载');

// ========================================================================
// Lodop基础函数 - 依赖全局lodop-print.js
// ========================================================================

// 检查Lodop是否可用
function isLodopAvailable() {
    // 正确的检查方式是看 getLodop 是否存在并且能返回有效的LODOP对象
    if (typeof getLodop === 'function') {
        var LODOP = getLodop();
        if (LODOP && LODOP.VERSION) {
            return true;
        }
    }
    console.error('Lodop打印控件未安装或未正确加载。');
    return false;
}



// ========================================================================
// 退充小票打印功能
// ========================================================================

/**
 * 退充小票打印 (80mm 热敏纸)
 * @param {Object} refundData 退充数据
 *   - refund_id: number 退充记录ID
 *   - customer_name: string 客户姓名
 *   - customer_phone: string 手机号码
 *   - refund_amount: number 退充金额
 *   - refund_gift_amount: number 扣除的赠送金额
 *   - refund_reason: string 退充原因
 *   - operator: string 操作员
 *   - refund_time: string 退充时间
 *   - original_recharge: object 原充值信息
 *   - balance_info: object 余额变动信息
 */
function printRefundReceiptLodop(refundData) {
    if (!isLodopAvailable()) return;

    try {
        const LODOP = getLodop();
        LODOP.PRINT_INIT("充值退充小票打印");

        // 80mm 宽热敏纸，高度初始 200mm，自适应后再调整
        LODOP.SET_PRINT_PAGESIZE(1, "80mm", "200mm", "");
        LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
        LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREVIEW", true);

        let currentTop = 5; // 当前打印位置 (mm)

        // ======== 标题 ========
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "8mm", "Soulweave改衣坊");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 18);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
        currentTop += 10;

        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", "充值退充小票");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 14);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
        currentTop += 8;

        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;

        // ======== 基本信息 ========
        const refundTime = refundData.refund_time || new Date().toLocaleString('zh-CN', {
            year: 'numeric', month: '2-digit', day: '2-digit',
            hour: '2-digit', minute: '2-digit', second: '2-digit'
        });

        const baseInfo = [
            `客户姓名: ${refundData.customer_name || '未知'}`,
            `手机号码: ${refundData.customer_phone || '未知'}`,
            `退充时间: ${refundTime}`,
            `操作员: ${refundData.operator || '系统'}`
        ];

        baseInfo.forEach(info => {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", info);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;
        });

        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;

        // ======== 原充值信息 ========
        if (refundData.original_recharge) {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", "原充值信息:");
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;

            const originalInfo = [
                `充值时间: ${refundData.original_recharge.created_at ? new Date(refundData.original_recharge.created_at).toLocaleString('zh-CN') : '未知'}`,
                `充值金额: ¥${(refundData.original_recharge.amount || 0).toFixed(2)}`,
                `赠送金额: ¥${(refundData.original_recharge.gift_amount || 0).toFixed(2)}`
            ];

            originalInfo.forEach(info => {
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "10mm", "65mm", "5mm", info);
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                currentTop += 5;
            });

            // 分隔线
            LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
            currentTop += 3;
        }

        // ======== 退充详情 ========
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", "退充详情:");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        currentTop += 6;

        // 退充金额
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "10mm", "65mm", "5mm", `退充金额: ¥${(refundData.refund_amount || 0).toFixed(2)}`);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        currentTop += 5;

        // 扣除赠送金额（如果有）
        if (refundData.refund_gift_amount && refundData.refund_gift_amount > 0) {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "10mm", "65mm", "5mm", `扣除赠送: ¥${refundData.refund_gift_amount.toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 5;

            const actualRefund = (refundData.refund_amount || 0) - (refundData.refund_gift_amount || 0);
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "10mm", "65mm", "5mm", `实际退充: ¥${actualRefund.toFixed(2)}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 5;
        }

        // 退充原因
        if (refundData.refund_reason) {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "10mm", "65mm", "6mm", `退充原因: ${refundData.refund_reason}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 7;
        }

        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;

        // ======== 余额变动信息 ========
        if (refundData.balance_info) {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", "余额变动:");
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            currentTop += 6;

            const balanceInfo = [
                `退充前余额: ¥${(refundData.balance_info.balance_before || 0).toFixed(2)}`,
                `退充后余额: ¥${(refundData.balance_info.balance_after || 0).toFixed(2)}`
            ];

            balanceInfo.forEach(info => {
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "10mm", "65mm", "5mm", info);
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                currentTop += 5;
            });

            currentTop += 3;
        }

        // ======== 尾部致谢 ========
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "5mm", "感谢您的理解，欢迎再次光临！");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 11);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
        currentTop += 8;

        // 重新设置页面高度，避免留白
        LODOP.SET_PRINT_PAGESIZE(1, "80mm", `${currentTop + 5}mm`, "");

        // 指定打印机（与充值小票相同）
        try {
            const result = LODOP.SET_PRINTER_INDEXA("xp");
            if (result) {
                console.log("成功设置退充小票打印机: xp");
            } else {
                console.error("未找到指定的退充小票打印机，取消打印操作");
                return;
            }
        } catch (e) {
            console.error("设置退充小票打印机时发生错误，取消打印操作");
            return;
        }

        // 静默打印
        LODOP.PRINT();

    } catch (error) {
        console.error('退充小票打印失败:', error);
    }
}

// ========================================================================
// 补打印功能
// ========================================================================

/**
 * 获取充值记录数据 - 统一数据获取接口
 * @param {number} rechargeId 充值记录ID
 * @returns {Promise<Object>} 充值记录数据
 */
async function fetchRechargeData(rechargeId) {
    try {
        console.log(`获取充值记录数据，ID: ${rechargeId}`);
        
        const response = await fetch(`/api/recharge_details?recharge_id=${rechargeId}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error || '获取充值记录详情失败');
        }
        
        console.log('充值记录数据获取成功:', result);
        return result;
        
    } catch (error) {
        console.error('获取充值记录数据失败:', error);
        throw error;
    }
}

/**
 * 统一的充值小票补打印接口 - 带模式选择
 * @param {number} rechargeId 充值记录ID
 * @param {string} reason 补打印原因
 */
function reprintRechargeReceiptWithModeSelection(rechargeId, reason = '客户要求补打印') {
    console.log(`充值小票补打印模式选择，ID: ${rechargeId}, 原因: ${reason}`);
    
    // 检查Lodop是否可用，如果可用则直接静默打印
    if (isLodopAvailable()) {
        console.log('Lodop可用，执行静默补打印');
        executeRechargeReprintLodop(rechargeId, reason);
        return;
    }
    
    // Lodop不可用，提供选择对话框
    console.log('Lodop不可用，显示选择对话框');
    const mode = confirm(
        '未检测到 Lodop 打印组件\n\n' +
        '确定 = 使用网页打印 (备选方案)\n' +
        '取消 = 安装 Lodop 后再试'
    );
    
    if (mode) {
        // 用户选择网页打印
        console.log('用户选择网页打印');
        executeRechargeReprintBrowser(rechargeId, reason);
    } else {
        console.log('用户取消打印，建议安装Lodop');
        alert('已取消打印，请安装 Lodop 打印控件后重试');
    }
}

/**
 * 执行Lodop充值小票补打印
 * @param {number} rechargeId 充值记录ID
 * @param {string} reason 补打印原因
 */
async function executeRechargeReprintLodop(rechargeId, reason) {
    try {
        console.log(`执行Lodop充值小票补打印，ID: ${rechargeId}`);
        
        // 1. 创建补打印记录
        const reprintResponse = await fetch(`/api/recharge/${rechargeId}/reprint`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reprint_reason: reason
            })
        });

        const reprintResult = await reprintResponse.json();
        if (!reprintResult.success) {
            throw new Error(reprintResult.error || '创建补打印记录失败');
        }
        console.log('补打印记录创建成功:', reprintResult);

        // 2. 获取充值记录详情
        const rechargeData = await fetchRechargeData(rechargeId);
        
        // 3. 准备打印数据（复用充值时的数据格式，添加补打印标识）
        const printData = {
            customer_name: rechargeData.customer_name,
            phone: rechargeData.customer_phone,
            amount: rechargeData.amount,
            giftAmount: rechargeData.gift_amount,
            paymentMethod: rechargeData.payment_method,
            newBalance: rechargeData.current_balance,
            operator: rechargeData.operator,
            rechargeTime: rechargeData.created_at,
            // 补打印标识
            isReprint: true,
            reprintTime: new Date().toLocaleString('zh-CN'),
            reprintReason: reason
        };
        
        console.log('准备的补打印数据:', printData);

        // 4. 直接调用充值时的自动打印函数
        if (typeof window.autoPrintRechargeReceipt === 'function') {
            console.log('使用充值自动打印函数进行补打印');
            window.autoPrintRechargeReceipt(printData);
        } else {
            throw new Error('充值小票打印函数不可用');
        }

        console.log('Lodop充值小票补打印完成');
        
    } catch (error) {
        console.error('Lodop充值小票补打印失败:', error);
        alert(`Lodop补打印失败: ${error.message}`);
    }
}

/**
 * 执行浏览器充值小票补打印
 * @param {number} rechargeId 充值记录ID
 * @param {string} reason 补打印原因
 */
async function executeRechargeReprintBrowser(rechargeId, reason) {
    try {
        console.log(`执行浏览器充值小票补打印，ID: ${rechargeId}`);
        
        // 1. 创建补打印记录
        const reprintResponse = await fetch(`/api/recharge/${rechargeId}/reprint`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reprint_reason: reason
            })
        });

        const reprintResult = await reprintResponse.json();
        if (!reprintResult.success) {
            throw new Error(reprintResult.error || '创建补打印记录失败');
        }
        console.log('补打印记录创建成功:', reprintResult);

        // 2. 打开专门的充值小票补打印页面（保留现有实现）
        const printUrl = `/recharge_receipt_reprint?recharge_id=${rechargeId}&reason=${encodeURIComponent(reason)}`;
        console.log('打开网页补打印窗口:', printUrl);
        window.open(printUrl, '_blank');
        
    } catch (error) {
        console.error('浏览器充值小票补打印失败:', error);
        alert(`网页补打印失败: ${error.message}`);
    }
}

/**
 * 补打印充值小票 - 兼容旧接口
 * @param {number} rechargeId 充值记录ID
 * @param {string} reason 补打印原因
 */
async function reprintRechargeReceipt(rechargeId, reason = '客户要求补打印') {
    console.log(`充值小票补打印（兼容接口），ID: ${rechargeId}`);
    
    // 直接调用新的模式选择接口
    reprintRechargeReceiptWithModeSelection(rechargeId, reason);
    
    // 返回成功状态（用于兼容性）
    return { success: true, message: '补打印请求已处理' };
}

/**
 * 获取退充记录数据 - 统一数据获取接口
 * @param {number} refundId 退充记录ID
 * @returns {Promise<Object>} 退充记录数据
 */
async function fetchRefundData(refundId) {
    try {
        console.log(`获取退充记录数据，ID: ${refundId}`);
        
        const response = await fetch(`/api/recharge/refunds?refund_id=${refundId}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        if (!result.success || !result.refunds.length) {
            throw new Error('获取退充记录详情失败');
        }
        
        console.log('退充记录数据获取成功:', result.refunds[0]);
        return result.refunds[0];
        
    } catch (error) {
        console.error('获取退充记录数据失败:', error);
        throw error;
    }
}

/**
 * 统一的退充小票补打印接口 - 带模式选择
 * @param {number} refundId 退充记录ID
 * @param {string} reason 补打印原因
 */
function reprintRefundReceiptWithModeSelection(refundId, reason = '客户要求补打印') {
    console.log(`退充小票补打印模式选择，ID: ${refundId}, 原因: ${reason}`);
    
    // 检查Lodop是否可用，如果可用则直接静默打印
    if (isLodopAvailable()) {
        console.log('Lodop可用，执行静默补打印');
        executeRefundReprintLodop(refundId, reason);
        return;
    }
    
    // Lodop不可用，提供选择对话框
    console.log('Lodop不可用，显示选择对话框');
    const mode = confirm(
        '未检测到 Lodop 打印组件\n\n' +
        '确定 = 使用网页打印 (备选方案)\n' +
        '取消 = 安装 Lodop 后再试'
    );
    
    if (mode) {
        // 用户选择网页打印
        console.log('用户选择网页打印');
        executeRefundReprintBrowser(refundId, reason);
    } else {
        console.log('用户取消打印，建议安装Lodop');
        alert('已取消打印，请安装 Lodop 打印控件后重试');
    }
}

/**
 * 执行Lodop退充小票补打印
 * @param {number} refundId 退充记录ID
 * @param {string} reason 补打印原因
 */
async function executeRefundReprintLodop(refundId, reason) {
    try {
        console.log(`执行Lodop退充小票补打印，ID: ${refundId}`);
        
        // 1. 创建补打印记录
        const reprintResponse = await fetch(`/api/recharge/refund/${refundId}/reprint`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reprint_reason: reason
            })
        });

        const reprintResult = await reprintResponse.json();
        if (!reprintResult.success) {
            throw new Error(reprintResult.error || '创建补打印记录失败');
        }
        console.log('补打印记录创建成功:', reprintResult);

        // 2. 获取退充记录详情
        const refundRecord = await fetchRefundData(refundId);
        
        // 3. 准备打印数据
        const printData = {
            refund_id: refundRecord.id,
            customer_name: refundRecord.customer_name,
            customer_phone: refundRecord.customer_phone,
            refund_amount: refundRecord.refund_amount,
            refund_gift_amount: refundRecord.refund_gift_amount || 0,
            refund_reason: refundRecord.refund_reason,
            operator: refundRecord.operator,
            refund_time: refundRecord.created_at,
            original_recharge: refundRecord.original_recharge,
            isReprint: true,
            reprintTime: new Date().toLocaleString('zh-CN'),
            reprintReason: reason
        };
        
        console.log('准备的Lodop退充打印数据:', printData);

        // 4. 调用Lodop退充小票打印函数
        printRefundReceiptLodop(printData);

        console.log('Lodop退充小票补打印完成');
        
    } catch (error) {
        console.error('Lodop退充小票补打印失败:', error);
        alert(`Lodop补打印失败: ${error.message}`);
    }
}

/**
 * 执行浏览器退充小票补打印
 * @param {number} refundId 退充记录ID
 * @param {string} reason 补打印原因
 */
async function executeRefundReprintBrowser(refundId, reason) {
    try {
        console.log(`执行浏览器退充小票补打印，ID: ${refundId}`);
        
        // 1. 创建补打印记录
        const reprintResponse = await fetch(`/api/recharge/refund/${refundId}/reprint`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reprint_reason: reason
            })
        });

        const reprintResult = await reprintResponse.json();
        if (!reprintResult.success) {
            throw new Error(reprintResult.error || '创建补打印记录失败');
        }
        console.log('补打印记录创建成功:', reprintResult);

        // 2. 打开新窗口进行网页打印
        const printUrl = `/refund_receipt_reprint?refund_id=${refundId}&reason=${encodeURIComponent(reason)}`;
        console.log('打开网页打印窗口:', printUrl);
        window.open(printUrl, '_blank');
        
    } catch (error) {
        console.error('浏览器退充小票补打印失败:', error);
        alert(`网页补打印失败: ${error.message}`);
    }
}

/**
 * 补打印退充小票 - 兼容旧接口
 * @param {number} refundId 退充记录ID
 * @param {string} reason 补打印原因
 */
async function reprintRefundReceipt(refundId, reason = '客户要求补打印') {
    console.log(`退充小票补打印（兼容接口），ID: ${refundId}`);
    
    // 直接调用新的模式选择接口
    reprintRefundReceiptWithModeSelection(refundId, reason);
    
    // 返回成功状态（用于兼容性）
    return { success: true, message: '补打印请求已处理' };
}

// ========================================================================
// 修改现有充值小票打印函数支持补打印标识
// ========================================================================

/**
 * 修改充值小票打印函数，支持补打印标识
 * @param {Object} rechargeData 充值数据
 */
function printRechargeReceiptWithReprintSupport(rechargeData) {
    if (!isLodopAvailable()) return;

    try {
        const LODOP = getLodop();
        LODOP.PRINT_INIT("充值小票打印");

        // 80mm 宽热敏纸，高度初始 200mm，自适应后再调整
        LODOP.SET_PRINT_PAGESIZE(1, "80mm", "200mm", "");
        LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
        LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREVIEW", true);

        let currentTop = 5;

        // ======== 标题 ========
        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "8mm", "Soulweave改衣坊");
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 18);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
        currentTop += 10;

        // 小票类型标题（支持补打印标识）
        let receiptTitle = "账户充值小票";
        if (rechargeData.isReprint) {
            receiptTitle += " [补打印]";
        }

        LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "6mm", receiptTitle);
        LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
        LODOP.SET_PRINT_STYLEA(0, "FontSize", 14);
        LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
        LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
        currentTop += 8;

        // 补打印信息
        if (rechargeData.isReprint) {
            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "4mm", `补打印时间: ${rechargeData.reprintTime || new Date().toLocaleString('zh-CN')}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
            currentTop += 5;

            if (rechargeData.reprintReason) {
                LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "4mm", `补打印原因: ${rechargeData.reprintReason}`);
                LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
                LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
                LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
                currentTop += 5;
            }

            LODOP.ADD_PRINT_TEXT(`${currentTop}mm`, "5mm", "70mm", "4mm", `原小票时间: ${rechargeData.rechargeTime || '未知'}`);
            LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
            LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
            LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
            LODOP.SET_PRINT_STYLEA(0, "Alignment", 2);
            currentTop += 6;
        }

        // 分隔线
        LODOP.ADD_PRINT_LINE(`${currentTop}mm`, "5mm", `${currentTop}mm`, "75mm", 0, 1);
        currentTop += 3;

        // 其余内容与原充值小票打印相同...
        // 这里可以调用原有的充值小票打印逻辑

        // 指定打印机
        try {
            const result = LODOP.SET_PRINTER_INDEXA("xp");
            if (result) {
                console.log("成功设置充值小票打印机: xp");
            } else {
                console.error("未找到指定的充值小票打印机，取消打印操作");
                return;
            }
        } catch (e) {
            console.error("设置充值小票打印机时发生错误，取消打印操作");
            return;
        }

        LODOP.PRINT();

    } catch (error) {
        console.error('充值小票打印失败:', error);
    }
}

// ========================================================================
// 全局函数导出
// ========================================================================

// ========================================================================
// 全局函数导出
// ========================================================================

// 导出主要打印函数
window.printRefundReceiptLodop = printRefundReceiptLodop;
window.printRechargeReceiptWithReprintSupport = printRechargeReceiptWithReprintSupport;

// 导出新的模式选择接口
window.reprintRechargeReceiptWithModeSelection = reprintRechargeReceiptWithModeSelection;
window.reprintRefundReceiptWithModeSelection = reprintRefundReceiptWithModeSelection;

// 导出兼容接口
window.reprintRechargeReceipt = reprintRechargeReceipt;
window.reprintRefundReceipt = reprintRefundReceipt;

// 导出数据获取函数
window.fetchRechargeData = fetchRechargeData;
window.fetchRefundData = fetchRefundData;

// ========================================================================
// 调试和测试函数
// ========================================================================



// 验证函数是否正常工作
try {
    const lodopAvailable = isLodopAvailable();
    console.log('Lodop可用性检查结果:', lodopAvailable);
    
    const lodopInstance = getLodop();
    console.log('Lodop实例获取结果:', lodopInstance ? '成功' : '失败');
    
    console.log('充值退充小票打印功能已就绪 - 支持Lodop和浏览器双模式打印');
} catch (error) {
    console.error('充值退充小票打印功能初始化失败:', error);
}