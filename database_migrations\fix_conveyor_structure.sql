-- 修复输送线相关表结构
-- 执行时间：立即执行以修复格架管理功能

-- =====================================================
-- 1. 修复 conveyor_log 表结构
-- =====================================================

-- 添加缺失的字段
ALTER TABLE conveyor_log 
ADD COLUMN action VARCHAR(50) NOT NULL DEFAULT '入架' AFTER lane;

-- 修改字段以匹配数据模型
ALTER TABLE conveyor_log 
CHANGE COLUMN remark remarks VARCHAR(255);

-- 添加created_at字段映射
ALTER TABLE conveyor_log 
ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP AFTER remarks;

-- 更新existing records的created_at
UPDATE conveyor_log SET created_at = send_time WHERE created_at IS NULL;

-- =====================================================
-- 2. 创建新的 conveyor_config 表结构
-- =====================================================

-- 备份原表
CREATE TABLE conveyor_config_backup AS SELECT * FROM conveyor_config;

-- 重新创建表以匹配数据模型
DROP TABLE conveyor_config;

CREATE TABLE conveyor_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    slot_no INT NOT NULL,
    lane CHAR(1) NOT NULL DEFAULT 'A',
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_slot_lane (slot_no, lane)
);

-- 插入默认配置 (A侧和B侧各700个格架)
INSERT INTO conveyor_config (slot_no, lane, is_active) 
SELECT slot_no, 'A', TRUE FROM (
    SELECT 1 as slot_no UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
    SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
    SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
    SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20
) AS numbers;

-- 为避免插入太多数据，这里只插入前20个作为示例
-- 实际部署时可以根据需要插入完整的1-700格架数据

-- 插入B侧格架
INSERT INTO conveyor_config (slot_no, lane, is_active) 
SELECT slot_no, 'B', TRUE FROM (
    SELECT 1 as slot_no UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
    SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
    SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
    SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20
) AS numbers;

-- =====================================================
-- 3. 创建设备配置表
-- =====================================================

-- 创建新的设备配置表来存储原有的IP配置信息
CREATE TABLE conveyor_device_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    store_name VARCHAR(100) NOT NULL UNIQUE,
    ip VARCHAR(45) NOT NULL,
    port INT NOT NULL DEFAULT 8080,
    max_slots INT NOT NULL DEFAULT 700,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 从备份表恢复原有配置
INSERT INTO conveyor_device_config (store_name, ip, port, max_slots, is_active, created_at, updated_at)
SELECT store_name, ip, port, max_slots, is_active, created_at, updated_at 
FROM conveyor_config_backup;

-- =====================================================
-- 4. 验证修复结果
-- =====================================================

SELECT '=== 修复结果验证 ===' AS info;

-- 检查conveyor_log表结构
SELECT 'conveyor_log表结构:' AS table_info;
DESCRIBE conveyor_log;

-- 检查conveyor_config表结构
SELECT 'conveyor_config表结构:' AS table_info;
DESCRIBE conveyor_config;

-- 检查conveyor_device_config表结构
SELECT 'conveyor_device_config表结构:' AS table_info;
DESCRIBE conveyor_device_config;

-- 统计数据
SELECT 'conveyor_config记录数:' AS info, COUNT(*) as count FROM conveyor_config;
SELECT 'conveyor_device_config记录数:' AS info, COUNT(*) as count FROM conveyor_device_config;

SELECT '=== 数据库结构修复完成 ===' AS final_status;