<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退充记录管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .page-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .search-card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 2rem;
        }
        
        .table-card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }
        
        .refund-amount {
            color: #dc3545;
            font-weight: bold;
        }
        
        .original-amount {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .status-completed {
            background-color: #28a745;
        }
        
        .status-cancelled {
            background-color: #6c757d;
        }
        
        .btn-reprint {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }
        
        .btn-reprint:hover {
            background-color: #218838;
            border-color: #1e7e34;
            color: white;
        }
    </style>
</head><body
>
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-undo me-2"></i>退充记录管理</h1>
                    <p class="mb-0">查看和管理所有充值退充记录</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                    <a href="/recharge/management" class="btn btn-outline-light ms-2">
                        <i class="fas fa-credit-card me-1"></i>充值记录
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 搜索筛选区域 -->
        <div class="card search-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-search me-2"></i>搜索筛选</h5>
            </div>
            <div class="card-body">
                <form id="searchForm">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">客户手机</label>
                            <input type="text" class="form-control" id="customerPhone" placeholder="输入手机号">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">操作员</label>
                            <input type="text" class="form-control" id="operator" placeholder="操作员">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">状态</label>
                            <select class="form-control" id="status">
                                <option value="">全部</option>
                                <option value="completed">已完成</option>
                                <option value="cancelled">已取消</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-primary w-100" onclick="searchRefundRecords()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="button" class="btn btn-secondary" onclick="resetSearch()">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                            <button type="button" class="btn btn-success ms-2" onclick="exportRecords()">
                                <i class="fas fa-download me-1"></i>导出
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary">总退充次数</h5>
                        <h3 class="text-primary" id="totalRefunds">0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-danger">总退充金额</h5>
                        <h3 class="text-danger" id="totalRefundAmount">¥0.00</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success">今日退充</h5>
                        <h3 class="text-success" id="todayRefunds">0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning">本月退充</h5>
                        <h3 class="text-warning" id="monthRefunds">0</h3>
                    </div>
                </div>
            </div>
        </div>  
      <!-- 退充记录表格 -->
        <div class="card table-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>退充记录</h5>
                <div>
                    <span class="badge bg-info" id="totalCount">总计: 0 条</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>退充ID</th>
                                <th>原充值ID</th>
                                <th>客户信息</th>
                                <th>退充金额</th>
                                <th>原充值金额</th>
                                <th>退充原因</th>
                                <th>退充时间</th>
                                <th>操作员</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="refundTableBody">
                            <tr>
                                <td colspan="10" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载数据...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="分页导航" id="paginationNav" style="display: none;">
                    <ul class="pagination justify-content-center" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 退充详情模态框 -->
    <div class="modal fade" id="refundDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>退充详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="refundDetailContent">
                    <!-- 详情内容将在这里显示 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 补打印确认模态框 -->
    <div class="modal fade" id="reprintModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-print me-2 text-success"></i>补打印退充小票
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="reprintInfo" class="mb-3">
                        <!-- 补打印信息将在这里显示 -->
                    </div>
                    <form id="reprintForm">
                        <input type="hidden" id="reprintRefundId">
                        <div class="mb-3">
                            <label class="form-label">补打印原因</label>
                            <input type="text" class="form-control" id="reprintReason" 
                                   placeholder="请输入补打印原因" value="客户要求补打印">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="confirmReprintBtn" onclick="executeReprint()">
                        <i class="fas fa-print me-1"></i>确认补打印
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/lodop-print.js"></script>
    <script src="/static/js/refund-print.js"></script>  
  <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let currentSearchParams = {};

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期范围（最近30天）
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            
            // 加载退充记录
            searchRefundRecords();
        });

        // 搜索退充记录
        async function searchRefundRecords(page = 1) {
            try {
                currentPage = page;
                
                // 构建搜索参数
                const params = new URLSearchParams({
                    page: page,
                    per_page: 20
                });
                
                const customerPhone = document.getElementById('customerPhone').value.trim();
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                const operator = document.getElementById('operator').value.trim();
                const status = document.getElementById('status').value;
                
                if (customerPhone) params.append('customer_phone', customerPhone);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                if (operator) params.append('operator', operator);
                if (status) params.append('status', status);
                
                currentSearchParams = Object.fromEntries(params);
                
                // 显示加载状态
                showLoading();
                
                const response = await fetch(`/api/recharge/refunds?${params}`);
                const result = await response.json();
                
                if (result.success) {
                    displayRefundRecords(result.refunds);
                    updatePagination(result.current_page, result.total_pages, result.total);
                    updateStatistics(result.refunds);
                } else {
                    showError('加载退充记录失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 显示退充记录
        function displayRefundRecords(records) {
            const tbody = document.getElementById('refundTableBody');
            
            if (!records || records.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p>暂无退充记录</p>
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = records.map(record => `
                <tr>
                    <td><strong>${record.id}</strong></td>
                    <td><a href="#" onclick="showOriginalRecharge(${record.original_recharge_id})">${record.original_recharge_id}</a></td>
                    <td>
                        <div><strong>${record.customer_name}</strong></div>
                        <small class="text-muted">${record.customer_phone}</small>
                    </td>
                    <td><span class="refund-amount">¥${record.refund_amount.toFixed(2)}</span></td>
                    <td>
                        <div class="original-amount">¥${record.original_recharge.amount.toFixed(2)}</div>
                        ${record.original_recharge.gift_amount > 0 ? 
                            `<small class="text-success">赠送: ¥${record.original_recharge.gift_amount.toFixed(2)}</small>` : ''}
                    </td>
                    <td>
                        <small title="${record.refund_reason || '无'}">
                            ${(record.refund_reason || '无').length > 20 ? 
                                (record.refund_reason || '无').substring(0, 20) + '...' : 
                                (record.refund_reason || '无')}
                        </small>
                    </td>
                    <td><small>${new Date(record.created_at).toLocaleString()}</small></td>
                    <td><span class="badge bg-secondary">${record.operator}</span></td>
                    <td>
                        <span class="badge status-${record.status}">
                            ${record.status === 'completed' ? '已完成' : '已取消'}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info btn-sm" onclick="showRefundDetail(${record.id})" 
                                    title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-reprint btn-sm" onclick="showReprintModal(${record.id})" 
                                    title="补打印">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        } 
       // 更新统计信息
        function updateStatistics(records) {
            const totalRefunds = records.length;
            const totalAmount = records.reduce((sum, record) => sum + record.refund_amount, 0);
            
            const today = new Date().toDateString();
            const todayRefunds = records.filter(record => 
                new Date(record.created_at).toDateString() === today
            ).length;
            
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();
            const monthRefunds = records.filter(record => {
                const recordDate = new Date(record.created_at);
                return recordDate.getMonth() === currentMonth && recordDate.getFullYear() === currentYear;
            }).length;
            
            document.getElementById('totalRefunds').textContent = totalRefunds;
            document.getElementById('totalRefundAmount').textContent = `¥${totalAmount.toFixed(2)}`;
            document.getElementById('todayRefunds').textContent = todayRefunds;
            document.getElementById('monthRefunds').textContent = monthRefunds;
        }

        // 显示退充详情
        async function showRefundDetail(refundId) {
            try {
                // 这里可以调用API获取详细信息，暂时使用现有数据
                const response = await fetch(`/api/recharge/refunds?refund_id=${refundId}`);
                const result = await response.json();
                
                if (result.success && result.refunds.length > 0) {
                    const refund = result.refunds[0];
                    
                    const content = document.getElementById('refundDetailContent');
                    content.innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>退充信息</h6>
                                <table class="table table-sm">
                                    <tr><td>退充ID:</td><td><strong>${refund.id}</strong></td></tr>
                                    <tr><td>原充值ID:</td><td>${refund.original_recharge_id}</td></tr>
                                    <tr><td>退充金额:</td><td class="refund-amount">¥${refund.refund_amount.toFixed(2)}</td></tr>
                                    <tr><td>退充时间:</td><td>${new Date(refund.created_at).toLocaleString()}</td></tr>
                                    <tr><td>操作员:</td><td>${refund.operator}</td></tr>
                                    <tr><td>状态:</td><td><span class="badge status-${refund.status}">${refund.status === 'completed' ? '已完成' : '已取消'}</span></td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>客户信息</h6>
                                <table class="table table-sm">
                                    <tr><td>客户姓名:</td><td>${refund.customer_name}</td></tr>
                                    <tr><td>手机号码:</td><td>${refund.customer_phone}</td></tr>
                                </table>
                                
                                <h6>原充值信息</h6>
                                <table class="table table-sm">
                                    <tr><td>充值金额:</td><td>¥${refund.original_recharge.amount.toFixed(2)}</td></tr>
                                    <tr><td>赠送金额:</td><td>¥${refund.original_recharge.gift_amount.toFixed(2)}</td></tr>
                                    <tr><td>充值时间:</td><td>${new Date(refund.original_recharge.created_at).toLocaleString()}</td></tr>
                                </table>
                            </div>
                        </div>
                        ${refund.refund_reason ? `
                            <div class="mt-3">
                                <h6>退充原因</h6>
                                <div class="alert alert-light">${refund.refund_reason}</div>
                            </div>
                        ` : ''}
                    `;
                    
                    const modal = new bootstrap.Modal(document.getElementById('refundDetailModal'));
                    modal.show();
                } else {
                    showAlert('获取退充详情失败', 'danger');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'danger');
            }
        }

        // 显示补打印模态框
        function showReprintModal(refundId) {
            document.getElementById('reprintRefundId').value = refundId;
            
            const reprintInfo = document.getElementById('reprintInfo');
            reprintInfo.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>补打印信息</h6>
                    <p class="mb-0">
                        记录类型: <span class="badge bg-danger">退充小票</span><br>
                        退充ID: <strong>${refundId}</strong>
                    </p>
                </div>
            `;
            
            // 重置表单
            document.getElementById('reprintForm').reset();
            document.getElementById('reprintReason').value = '客户要求补打印';
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('reprintModal'));
            modal.show();
        }

        // 执行补打印操作
        async function executeReprint() {
            try {
                const refundId = document.getElementById('reprintRefundId').value;
                const reprintReason = document.getElementById('reprintReason').value.trim();
                
                // 禁用按钮，显示加载状态
                const btn = document.getElementById('confirmReprintBtn');
                const originalText = btn.innerHTML;
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
                
                const response = await fetch(`/api/recharge/refund/${refundId}/reprint`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        reprint_reason: reprintReason
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(`补打印成功！记录ID: ${result.reprint_id}`, 'success');
                    
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('reprintModal'));
                    modal.hide();
                    
                    // 调用打印功能 - 使用新的模式选择接口
                    if (typeof window.reprintRefundReceiptWithModeSelection === 'function') {
                        console.log('使用新的退充小票补打印接口');
                        window.reprintRefundReceiptWithModeSelection(refundId, reprintReason);
                    } else if (typeof window.reprintRefundReceipt === 'function') {
                        console.log('使用兼容的退充小票补打印接口');
                        window.reprintRefundReceipt(refundId, reprintReason);
                    } else {
                        console.error('退充小票补打印函数不可用');
                        alert('退充小票补打印函数不可用，请检查页面是否正确加载');
                    }
                } else {
                    showAlert('补打印失败: ' + result.error, 'danger');
                }
                
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'danger');
            } finally {
                // 恢复按钮状态
                const btn = document.getElementById('confirmReprintBtn');
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-print me-1"></i>确认补打印';
            }
        }  
      // 更新分页
        function updatePagination(currentPage, totalPages, totalCount) {
            document.getElementById('totalCount').textContent = `总计: ${totalCount} 条`;
            
            const paginationNav = document.getElementById('paginationNav');
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                paginationNav.style.display = 'none';
                return;
            }
            
            paginationNav.style.display = 'block';
            
            let paginationHTML = '';
            
            // 上一页
            if (currentPage > 1) {
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="searchRefundRecords(${currentPage - 1})">上一页</a>
                    </li>
                `;
            }
            
            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="searchRefundRecords(${i})">${i}</a>
                    </li>
                `;
            }
            
            // 下一页
            if (currentPage < totalPages) {
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="searchRefundRecords(${currentPage + 1})">下一页</a>
                    </li>
                `;
            }
            
            pagination.innerHTML = paginationHTML;
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchForm').reset();
            
            // 重新设置默认日期
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            
            // 重新搜索
            searchRefundRecords(1);
        }

        // 导出记录
        function exportRecords() {
            const params = new URLSearchParams(currentSearchParams);
            params.delete('page');
            params.delete('per_page');
            
            const url = `/api/recharge/refunds/export?${params}`;
            window.open(url, '_blank');
        }

        // 显示原充值记录
        function showOriginalRecharge(rechargeId) {
            // 跳转到充值记录管理页面并高亮显示该记录
            window.open(`/recharge/management?highlight=${rechargeId}`, '_blank');
        }

        // 显示加载状态
        function showLoading() {
            const tbody = document.getElementById('refundTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载数据...</p>
                    </td>
                </tr>
            `;
        }

        // 显示错误信息
        function showError(message) {
            const tbody = document.getElementById('refundTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>${message}</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="searchRefundRecords(currentPage)">
                            <i class="fas fa-redo me-1"></i>重试
                        </button>
                    </td>
                </tr>
            `;
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>