from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for, flash
from models import *
from blueprints.auth import login_required
import datetime
# 新增：导入格架管理相关函数
from utils import send_conveyor_command, get_conveyor_config

status_bp = Blueprint('status', __name__)
bp = status_bp  # 为了与自动注册系统兼容

def auto_release_rack_slots(order):
    """自动释放订单中衣物的格架
    
    当订单状态变为"自提完成"或"配送完成"时自动调用
    先发送UDP指令让输送线移动到对应位置，然后释放格架
    
    Args:
        order: 订单对象
        
    Returns:
        dict: 释放结果 {'success': bool, 'released_count': int, 'errors': list}
    """
    try:
        staff_name = session.get('staff_name', '系统')
        released_count = 0
        errors = []
        
        # 查找已分配格架的衣物
        assigned_clothes = [c for c in order.clothes if c.slot_no]
        
        if not assigned_clothes:
            return {
                'success': True,
                'released_count': 0,
                'errors': ['没有需要释放的格架']
            }
        
        # 获取输送线配置
        config = get_conveyor_config(staff_name)
        if not config:
            # 如果没有配置，仍然执行数据库清理，但记录警告
            errors.append('未找到输送线配置，跳过UDP指令发送')
            config = None
        
        for clothing in assigned_clothes:
            try:
                # 记录释放前的信息
                old_slot_no = clothing.slot_no
                old_lane = clothing.lane
                
                # 先发送UDP指令让输送线移动到该位置（方便员工取衣服）
                result = None
                if config:
                    try:
                        result = send_conveyor_command(
                            ip=config['ip'],
                            port=config['port'],
                            side=old_lane,
                            slot_no=old_slot_no,
                            timeout=3.0
                        )
                        
                        if result['success']:
                            print(f"输送线移动成功: {old_lane}-{old_slot_no}, 准备取衣服")
                        else:
                            errors.append(f'衣物{clothing.barcode}输送线控制失败: {result["message"]}')
                            print(f"输送线控制失败: {result['message']}")
                    except Exception as e:
                        errors.append(f'衣物{clothing.barcode}输送线控制异常: {str(e)}')
                        print(f"输送线控制异常: {str(e)}")
                        result = {'success': False, 'command': 'ERROR', 'message': str(e)}
                
                # 清空格架信息
                clothing.slot_no = None
                clothing.lane = 'A'  # 重置为默认值
                clothing.slot_time = None
                
                # 记录日志
                log = ConveyorLog(
                    store='默认门店',
                    clothing_id=clothing.id,
                    slot_no=old_slot_no,
                    lane=old_lane,
                    barcode=clothing.barcode,
                    hex_cmd=result.get('command', 'NO_CMD') if result else 'NO_CONFIG',
                    action='自动出架',
                    operator=staff_name,
                    remarks=f'订单完成自动释放格架 {old_lane}-{old_slot_no}' + (f', UDP指令: {result["command"]}' if result and result.get('success') else ''),
                    remark=f'订单完成自动释放格架 {old_lane}-{old_slot_no}',
                    result='success' if result and result.get('success') else 'error'
                )
                db.session.add(log)
                released_count += 1
                
                print(f"自动释放格架: 衣物{clothing.barcode} 从 {old_lane}-{old_slot_no} 释放")
                
            except Exception as e:
                errors.append(f'衣物{clothing.barcode}释放失败: {str(e)}')
                print(f"格架释放异常: {str(e)}")
        
        return {
            'success': released_count > 0 or len(errors) == 0,
            'released_count': released_count,
            'errors': errors
        }
        
    except Exception as e:
        print(f"自动释放格架异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'released_count': 0,
            'errors': [f'系统异常: {str(e)}']
        }

def auto_assign_rack_slots(order):
    """自动为订单中的衣物分配格架
    
    当订单状态变为"已上架"时自动调用
    
    Args:
        order: 订单对象
        
    Returns:
        dict: 分配结果 {'success': bool, 'assigned_count': int, 'errors': list}
    """
    try:
        staff_name = session.get('staff_name', '系统')
        assigned_count = 0
        errors = []
        
        # 获取输送线配置
        config = get_conveyor_config(staff_name)
        if not config:
            return {
                'success': False,
                'assigned_count': 0,
                'errors': ['未找到输送线配置']
            }
        
        # 查找未分配格架的衣物
        unassigned_clothes = [c for c in order.clothes if not c.slot_no]
        
        if not unassigned_clothes:
            return {
                'success': True,
                'assigned_count': 0,
                'errors': ['所有衣物已分配格架']
            }
        
        # 查找空闲格架
        # 优先使用A侧，再使用B侧
        for lane in ['A', 'B']:
            if not unassigned_clothes:  # 所有衣物已分配完
                break
                
            # 查找该侧已占用的格架号
            occupied_slots = db.session.query(Clothing.slot_no).filter(
                Clothing.lane == lane,
                Clothing.slot_no.isnot(None)
            ).all()
            occupied_slots = {slot[0] for slot in occupied_slots}
            
            # 找到空闲格架（从1开始）
            for slot_no in range(1, 701):
                if slot_no not in occupied_slots and unassigned_clothes:
                    clothing = unassigned_clothes.pop(0)
                    
                    try:
                        # 发送UDP指令控制输送线
                        result = send_conveyor_command(
                            ip=config['ip'],
                            port=config['port'],
                            side=lane,
                            slot_no=slot_no,
                            timeout=3.0  # 自动分配时使用较短超时
                        )
                        
                        if result['success']:
                            # 更新衣物格架信息
                            clothing.slot_no = slot_no
                            clothing.lane = lane
                            clothing.slot_time = datetime.datetime.now()
                            
                            # 记录日志
                            log = ConveyorLog(
                                store='默认门店',
                                clothing_id=clothing.id,
                                slot_no=slot_no,
                                lane=lane,
                                barcode=clothing.barcode,
                                hex_cmd=result.get('command', 'NO_CMD'),
                                action='自动入架',
                                operator=staff_name,
                                remarks=f'订单上架自动分配, UDP指令: {result["command"]}',
                                remark=f'订单上架自动分配格架 {lane}-{slot_no}',
                                result='success'
                            )
                            db.session.add(log)
                            assigned_count += 1
                            
                            print(f"自动分配格架成功: 衣物{clothing.barcode} -> {lane}-{slot_no}")
                        else:
                            errors.append(f'衣物{clothing.barcode}输送线控制失败: {result["message"]}')
                            print(f"输送线控制失败: {result['message']}")
                            
                    except Exception as e:
                        errors.append(f'衣物{clothing.barcode}分配失败: {str(e)}')
                        print(f"格架分配异常: {str(e)}")
        
        # 如果还有未分配的衣物
        if unassigned_clothes:
            errors.append(f'格架已满，{len(unassigned_clothes)}件衣物未能分配格架')
        
        return {
            'success': assigned_count > 0 or len(errors) == 0,
            'assigned_count': assigned_count,
            'errors': errors
        }
        
    except Exception as e:
        print(f"自动分配格架异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'assigned_count': 0,
            'errors': [f'系统异常: {str(e)}']
        }

def validate_status_change(old_status, new_status):
    """状态流转校验已取消，所有变更直接放行"""
    return True, '状态流转校验已取消'

@status_bp.route('/update_order_status', methods=['POST'])
@login_required
def update_order_status():
    """更新订单状态"""
    try:
        data = request.json
        order_id = data.get('order_id')
        # 同时兼容前端可能传来的字段名称
        new_status = data.get('new_status') or data.get('status') or data.get('target_status')
        payment_status = data.get('payment_status')

        # 基本参数校验：订单ID必须存在，状态和支付状态不能同时为空
        if not order_id:
            return jsonify({'error': '订单ID不能为空'}), 400
        if new_status is None and payment_status is None:
            return jsonify({'error': '新状态和支付状态不能同时为空'}), 400
        
        order = Order.query.get(order_id)
        if not order:
            return jsonify({'error': '订单不存在'}), 404
        
        # 验证权限：检查用户是否有权限查看此订单
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        
        if staff_role not in ['admin'] and staff_area != '总部':
            if order.area and order.area != staff_area:
                return jsonify({'error': '您没有权限修改此区域的订单'}), 403
        
        # 如需更新订单状态，验证合法性
        if new_status is not None:
            is_valid, message = validate_status_change(order.status, new_status)
            if not is_valid:
                return jsonify({'error': message}), 400

            # 更新订单状态
            old_status = order.status
            order.status = new_status
            
            # 根据状态更新时间戳
            now = datetime.datetime.now()
            if new_status == '送往工厂':
                order.to_factory_time = now
            elif new_status == '工厂处理中':
                order.factory_in_time = now
            elif new_status == '工厂完成':
                order.factory_out_time = now
            elif new_status == '已上架':
                order.on_shelf_time = now
                # 新增：自动分配格架
                rack_result = auto_assign_rack_slots(order)
                if rack_result['assigned_count'] > 0:
                    print(f"订单{order.order_number}自动分配了{rack_result['assigned_count']}个格架")
                if rack_result['errors']:
                    print(f"订单{order.order_number}格架分配警告: {rack_result['errors']}")
            elif new_status in ['自提完成', '配送完成']:
                order.pickup_time = now
                # 新增：自动释放格架
                release_result = auto_release_rack_slots(order)
                if release_result['released_count'] > 0:
                    print(f"订单{order.order_number}自动释放了{release_result['released_count']}个格架")
                if release_result['errors']:
                    print(f"订单{order.order_number}格架释放警告: {release_result['errors']}")
            
            # 更新衣物状态
            for clothing in order.clothes:
                clothing.status = new_status
            
            # 记录状态变更日志
            status_log = OrderStatusLog(
                order_id=order.id,
                old_status=old_status,
                new_status=new_status,
                changed_by=session.get('staff_name', '未知'),
                remarks=data.get('remarks', '')
            )
            db.session.add(status_log)
        
        # 更新支付状态（可独立更新）
        if payment_status is not None and payment_status != order.payment_status:
            order.payment_status = payment_status
            if payment_status == '已付款':
                order.payment_time = datetime.datetime.now()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'order_number': order.order_number,
            'customer_name': order.customer.name if order.customer else '',
            'old_status': old_status if 'old_status' in locals() else order.status,
            'new_status': new_status if new_status is not None else order.status,
            'payment_status': order.payment_status,
            'clothing_name': clothing.name if 'clothing' in locals() else None
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@status_bp.route('/batch_update_order_status', methods=['POST'])
@login_required
def batch_update_order_status():
    """批量更新订单状态"""
    try:
        data = request.json
        order_ids = data.get('order_ids', [])
        new_status = data.get('new_status') or data.get('status') or data.get('target_status')
        payment_status = data.get('payment_status')
        
        if not order_ids:
            return jsonify({'error': '订单ID列表不能为空'}), 400
        if new_status is None and payment_status is None:
            return jsonify({'error': '新状态和支付状态不能同时为空'}), 400
        
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        
        updated_count = 0
        invalid_count = 0
        unauthorized_count = 0
        
        now = datetime.datetime.now()
        
        for order_id in order_ids:
            order = Order.query.get(order_id)
            if not order:
                continue
            
            # 验证权限
            if staff_role not in ['admin'] and staff_area != '总部':
                if order.area and order.area != staff_area:
                    unauthorized_count += 1
                    continue
            
            # 如果需要更新订单状态，先验证合法性
            if new_status is not None:
                is_valid, message = validate_status_change(order.status, new_status)
                if not is_valid:
                    invalid_count += 1
                    continue

                old_status = order.status
                order.status = new_status
                
                # 根据状态更新时间戳
                if new_status == '送往工厂':
                    order.to_factory_time = now
                elif new_status == '工厂处理中':
                    order.factory_in_time = now
                elif new_status == '工厂完成':
                    order.factory_out_time = now
                elif new_status == '已上架':
                    order.on_shelf_time = now
                    # 新增：自动分配格架
                    try:
                        rack_result = auto_assign_rack_slots(order)
                        if rack_result['assigned_count'] > 0:
                            print(f"批量处理-订单{order.order_number}自动分配了{rack_result['assigned_count']}个格架")
                        if rack_result['errors']:
                            print(f"批量处理-订单{order.order_number}格架分配警告: {rack_result['errors']}")
                    except Exception as e:
                        print(f"批量处理-订单{order.order_number}自动格架分配失败: {str(e)}")
                elif new_status in ['自提完成', '配送完成']:
                    order.pickup_time = now
                    # 新增：自动释放格架
                    try:
                        release_result = auto_release_rack_slots(order)
                        if release_result['released_count'] > 0:
                            print(f"批量处理-订单{order.order_number}自动释放了{release_result['released_count']}个格架")
                        if release_result['errors']:
                            print(f"批量处理-订单{order.order_number}格架释放警告: {release_result['errors']}")
                    except Exception as e:
                        print(f"批量处理-订单{order.order_number}自动格架释放失败: {str(e)}")

                # 更新衣物状态
                for clothing in order.clothes:
                    clothing.status = new_status

                # 记录状态变更日志
                status_log = OrderStatusLog(
                    order_id=order.id,
                    old_status=old_status,
                    new_status=new_status,
                    changed_by=session.get('staff_name', '未知'),
                    remarks=data.get('remarks', '')
                )
                db.session.add(status_log)
            
            # 更新支付状态
            if payment_status is not None and payment_status != order.payment_status:
                order.payment_status = payment_status
                if payment_status == '已付款':
                    order.payment_time = now
            
            updated_count += 1

        db.session.commit()

        result_message = f'成功更新{updated_count}个订单'
        if invalid_count > 0:
            result_message += f', {invalid_count}个订单因状态流转限制未更新'
        if unauthorized_count > 0:
            result_message += f', {unauthorized_count}个订单因权限不足无法更新'

        return jsonify({
            'success': True,
            'updated_count': updated_count,
            'invalid_count': invalid_count,
            'unauthorized_count': unauthorized_count,
            'message': result_message
        })

    except Exception as e:
        db.session.rollback()
        print(f"批量更新订单状态出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'批量更新订单状态失败: {str(e)}'}), 500

# 状态页面路由
@status_bp.route('/status/to_factory')
@login_required
def to_factory_page():
    """送往工厂页面"""
    staff_name = session.get('staff_name', '未登录')
    staff_area = session.get('staff_area', '')
    template_name = 'status_update_mobile.html' if request.args.get('m') == '1' else 'status_update.html'
    return render_template(template_name,
                          title='送至工厂',
                          icon='fa-truck',
                          target_status='送至工厂中',
                          update_endpoint='/api/update_status_by_number',
                          history_endpoint='/api/status_history/送至工厂中',
                          staff_name=staff_name,
                          staff_area=staff_area)

@status_bp.route('/status/factory_in')
@login_required
def factory_in_page():
    """工厂处理中页面"""
    staff_name = session.get('staff_name', '未登录')
    staff_area = session.get('staff_area', '')
    template_name = 'status_update_mobile.html' if request.args.get('m') == '1' else 'status_update.html'
    return render_template(template_name,
                          title='入厂',
                          icon='fa-industry',
                          target_status='入厂',
                          update_endpoint='/api/update_status_by_number',
                          history_endpoint='/api/status_history/入厂',
                          staff_name=staff_name,
                          staff_area=staff_area)

@status_bp.route('/status/factory_out')
@login_required
def factory_out_page():
    """工厂完成页面"""
    staff_name = session.get('staff_name', '未登录')
    staff_area = session.get('staff_area', '')
    template_name = 'status_update_mobile.html' if request.args.get('m') == '1' else 'status_update.html'
    return render_template(template_name,
                          title='出厂',
                          icon='fa-check-circle',
                          target_status='出厂',
                          update_endpoint='/api/update_status_by_number',
                          history_endpoint='/api/status_history/出厂',
                          staff_name=staff_name,
                          staff_area=staff_area)

@status_bp.route('/status/on_shelf')
@login_required
def on_shelf_page():
    """已上架页面"""
    staff_name = session.get('staff_name', '未登录')
    staff_area = session.get('staff_area', '')
    template_name = 'status_update_mobile.html' if request.args.get('m') == '1' else 'status_update.html'
    return render_template(template_name,
                          title='已上架',
                          icon='fa-check-circle',
                          target_status='已上架',
                          update_endpoint='/api/update_status_by_number',
                          history_endpoint='/api/status_history/已上架',
                          staff_name=staff_name,
                          staff_area=staff_area)

@status_bp.route('/status/self_pickup')
@login_required
def self_pickup_page():
    """自提页面"""
    staff_name = session.get('staff_name', '未登录')
    staff_area = session.get('staff_area', '')
    template_name = 'status_update_mobile.html' if request.args.get('m') == '1' else 'status_update.html'
    return render_template(template_name,
                          title='已自取',
                          icon='fa-check-circle',
                          target_status='已自取',
                          update_endpoint='/api/update_status_by_number',
                          history_endpoint='/api/status_history/已自取',
                          staff_name=staff_name,
                          staff_area=staff_area)

# 状态管理API
@status_bp.route('/api/update_status_by_number', methods=['POST'])
@login_required
def update_status_by_number():
    """根据订单号更新状态"""
    try:
        data = request.json
        order_number = data.get('order_number')
        new_status = data.get('new_status') or data.get('target_status')
        
        if not order_number or not new_status:
            return jsonify({'error': '订单号和新状态不能为空'}), 400
        
        order = Order.query.filter_by(order_number=order_number).first()
        if not order:
            return jsonify({'error': f'找不到订单号为 {order_number} 的订单'}), 404
        
        # 验证权限
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        
        if staff_role not in ['admin'] and staff_area != '总部':
            if order.area and order.area != staff_area:
                return jsonify({'error': '您没有权限修改此区域的订单'}), 403
        
        # 验证状态流转
        is_valid, message = validate_status_change(order.status, new_status)
        if not is_valid:
            return jsonify({'error': message}), 400
        
        # 更新状态
        old_status = order.status
        order.status = new_status
        
        # 如果进入出厂状态，重置出库打印标记
        if new_status == '出厂':
            order.outbound_printed = False
        
        # 更新时间戳
        now = datetime.datetime.now()
        if new_status == '送往工厂':
            order.to_factory_time = now
        elif new_status == '工厂处理中':
            order.factory_in_time = now
        elif new_status == '工厂完成':
            order.factory_out_time = now
        elif new_status == '已上架':
            order.on_shelf_time = now
        elif new_status in ['自提完成', '配送完成']:
            order.pickup_time = now
        
        # 更新衣物状态
        for clothing in order.clothes:
            clothing.status = new_status
        
        # 记录状态变更日志
        status_log = OrderStatusLog(
            order_id=order.id,
            old_status=old_status,
            new_status=new_status,
            changed_by=session.get('staff_name', '未知'),
            remarks=data.get('remarks', '')
        )
        db.session.add(status_log)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'order_number': order.order_number,
            'customer_name': order.customer.name if order.customer else '',
            'old_status': old_status,
            'new_status': new_status,
            'clothing_name': clothing.name if 'clothing' in locals() else None
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@status_bp.route('/api/update_clothing_status', methods=['POST'])
@login_required
def update_clothing_status():
    """更新单个衣物状态"""
    try:
        data = request.json
        clothing_id = data.get('clothing_id')
        order_number = data.get('order_number')
        clothing_index = data.get('clothing_index')
        new_status = data.get('new_status') or data.get('target_status')

        # 验证参数
        if not new_status:
            return jsonify({'error': '缺少目标状态 new_status'}), 400

        clothing = None
        if clothing_id:
            clothing = Clothing.query.get(clothing_id)
            if clothing:
                order = clothing.order
        else:
            if order_number and clothing_index is not None:
                order = Order.query.filter_by(order_number=order_number).first()
                if not order:
                    return jsonify({'error': '订单不存在'}), 404
                clothes_sorted = sorted(order.clothes, key=lambda x: x.id)
                idx = int(clothing_index) - 1
                if idx < 0 or idx >= len(clothes_sorted):
                    return jsonify({'error': '衣物序号超出范围'}), 400
                clothing = clothes_sorted[idx]
                clothing_id = clothing.id
                # 覆盖 order 变量，后续代码复用
                order = clothing.order

        if not clothing:
            return jsonify({'error': '未找到对应的衣物记录'}), 404
        
        # 验证权限
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        
        if staff_role not in ['admin'] and staff_area != '总部':
            if order.area and order.area != staff_area:
                return jsonify({'error': '您没有权限修改此区域的衣物状态'}), 403
        
        # 验证状态流转
        is_valid, message = validate_status_change(clothing.status, new_status)
        if not is_valid:
            return jsonify({'error': message}), 400
        
        # 更新衣物状态
        old_status = clothing.status
        clothing.status = new_status
        
        # 检查是否需要更新订单状态（当所有衣物都达到相同状态时）
        all_clothes_status = [c.status for c in order.clothes]
        if all(status == new_status for status in all_clothes_status):
            order.status = new_status
            
            # 如果进入出厂状态，重置出库打印标记
            if new_status == '出厂':
                order.outbound_printed = False
            
            # 更新订单时间戳
            now = datetime.datetime.now()
            if new_status == '送往工厂':
                order.to_factory_time = now
            elif new_status == '工厂处理中':
                order.factory_in_time = now
            elif new_status == '工厂完成':
                order.factory_out_time = now
            elif new_status == '已上架':
                order.on_shelf_time = now
            elif new_status in ['自提完成', '配送完成']:
                order.pickup_time = now
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'order_number': order.order_number,
            'customer_name': order.customer.name if order.customer else '',
            'old_status': old_status,
            'new_status': new_status,
            'clothing_name': clothing.name
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@status_bp.route('/api/status_history/<status>', methods=['GET'])
@login_required
def get_status_history(status):
    """获取指定状态的订单历史"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        
        # 构建查询
        query = Order.query.filter_by(status=status)
        
        # 添加区域权限控制
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')
        
        if staff_role not in ['admin'] and staff_area != '总部' and staff_area:
            query = query.filter(Order.area == staff_area)
        
        # 应用搜索过滤
        if search:
            query = query.join(Customer).filter(
                db.or_(
                    Order.order_number.like(f'%{search}%'),
                    Customer.name.like(f'%{search}%'),
                    Customer.phone.like(f'%{search}%')
                )
            )
        
        # 分页
        paginated_orders = query.order_by(Order.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        result = []
        for order in paginated_orders.items:
            result.append({
                'id': order.id,
                'order_number': order.order_number,
                'customer_name': order.customer.name if order.customer else '',
                'customer_phone': order.customer.phone if order.customer else '',
                'status': order.status,
                'total_amount': order.total_amount,
                'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'clothing_count': len(order.clothes),
                'operator': order.operator,
                'clothing_name': ','.join([c.name for c in order.clothes]),
                'old_status': order.status,
                'new_status': order.status
            })
        
        return jsonify({
            'history': result,
            'total': paginated_orders.total,
            'pages': paginated_orders.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# === 新增：出库单待打印&标记接口 ===
@status_bp.route('/api/outbound_pending', methods=['GET'])
@login_required
def get_outbound_pending():
    """查询待打印出库单订单列表（状态为出厂且未打印）"""
    try:
        # 权限过滤
        staff_role = session.get('staff_role')
        staff_name = session.get('staff_name')
        staff_area = session.get('staff_area')

        query = Order.query.filter_by(status='出厂', outbound_printed=False)

        if staff_role != 'manager':
            # 普通营业员：只打印自己操作的订单
            query = query.filter(Order.operator == staff_name)
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            # 区域管理员：只看本区域
            area_staff = Staff.query.filter_by(area=staff_area).all()
            names = [u.name for u in area_staff]
            query = query.filter(Order.operator.in_(names))
        # else 总部或超级管理员查看全部

        orders = query.order_by(Order.created_at.asc()).all()

        result = []
        for order in orders:
            clothing_names = ','.join([c.name for c in order.clothes])
            result.append({
                'id': order.id,
                'order_number': order.order_number,
                'operator': order.operator or '',
                'clothing_name': clothing_names
            })

        return jsonify({'success': True, 'orders': result})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@status_bp.route('/api/outbound_mark_printed', methods=['POST'])
@login_required
def mark_outbound_printed():
    """将指定订单标记为已打印出库单"""
    try:
        data = request.json or {}
        ids = data.get('order_ids', [])
        if not ids:
            return jsonify({'success': False, 'error': 'order_ids 不能为空'}), 400

        Order.query.filter(Order.id.in_(ids)).update({'outbound_printed': True}, synchronize_session=False)
        db.session.commit()
        return jsonify({'success': True, 'updated': len(ids)})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500