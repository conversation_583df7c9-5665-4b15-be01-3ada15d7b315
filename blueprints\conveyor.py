from flask import Blueprint, request, jsonify, session, render_template
from blueprints.auth import login_required
from models import db, Clothing, Order, Customer, ConveyorLog
from utils import send_conveyor_command, get_conveyor_config
import datetime

bp = Blueprint('conveyor', __name__)

@bp.route('/rack_management')
@login_required
def rack_management():
    """格架管理页面"""
    staff_name = session.get('staff_name', '未登录')
    staff_role = session.get('staff_role', '')
    
    # 检查是否是移动端
    is_mobile = request.args.get('m') == '1'
    template_name = 'rack_management_mobile.html' if is_mobile else 'rack_management.html'
    
    return render_template(template_name, 
                         staff_name=staff_name, 
                         staff_role=staff_role)

@bp.route('/api/slots', methods=['GET'])
@login_required
def get_slots_status():
    """获取格架状态API
    
    返回所有格架的占用情况
    """
    try:
        staff_name = session.get('staff_name')
        staff_role = session.get('staff_role')
        store_name = request.args.get('store')  # 管理员可以选择门店
        
        # 查询所有已分配格架的衣物
        query = db.session.query(Clothing).filter(Clothing.slot_no.isnot(None))
        
        # 权限控制：普通营业员只能看自己门店的
        if staff_role != 'manager':
            # 通过订单表关联，筛选操作员
            query = query.join(Order).filter(Order.operator == staff_name)
        elif staff_role == 'manager' and store_name:
            # 管理员选择了特定门店，按门店过滤
            # 这里需要根据实际的门店-员工关联逻辑来过滤
            # 暂时简化处理，可以根据实际需求完善
            pass
        
        occupied_slots = query.all()
        
        # 构建格架状态数据
        slots_data = {}
        for clothing in occupied_slots:
            key = f"{clothing.lane}-{clothing.slot_no}"
            order = clothing.order
            customer = order.customer if order else None
            
            slots_data[key] = {
                'clothing_id': clothing.id,
                'barcode': clothing.barcode,
                'clothing_name': clothing.name,
                'clothing_color': clothing.color,
                'order_number': order.order_number if order else '',
                'customer_name': customer.name if customer else '',
                'customer_phone': customer.phone if customer else '',
                'slot_time': clothing.slot_time.isoformat() if clothing.slot_time else '',
                'operator': order.operator if order else '',
                'status': 'occupied'
            }
        
        # 生成完整的格架列表（1-700，A/B侧）
        all_slots = []
        for lane in ['A', 'B']:
            for slot_no in range(1, 701):
                key = f"{lane}-{slot_no}"
                if key in slots_data:
                    slot_info = slots_data[key]
                    slot_info.update({
                        'lane': lane,
                        'slot_no': slot_no
                    })
                else:
                    slot_info = {
                        'lane': lane,
                        'slot_no': slot_no,
                        'status': 'free',
                        'clothing_id': None,
                        'barcode': '',
                        'clothing_name': '',
                        'clothing_color': '',
                        'order_number': '',
                        'customer_name': '',
                        'customer_phone': '',
                        'slot_time': '',
                        'operator': ''
                    }
                all_slots.append(slot_info)
        
        # 统计信息
        total_slots = 1400  # 700 * 2 (A/B侧)
        occupied_count = len(occupied_slots)
        free_count = total_slots - occupied_count
        usage_rate = round(occupied_count / total_slots * 100, 2)
        
        return jsonify({
            'success': True,
            'slots': all_slots,
            'summary': {
                'total_slots': total_slots,
                'occupied_count': occupied_count,
                'free_count': free_count,
                'usage_rate': usage_rate
            }
        })
        
    except Exception as e:
        print(f"获取格架状态失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@bp.route('/api/slots/assign', methods=['POST'])
@login_required
def assign_slot():
    """分配格架API
    
    为指定衣物分配格架并控制输送线
    """
    try:
        data = request.json
        clothing_id = data.get('clothing_id')
        slot_no = data.get('slot_no')
        lane = data.get('lane', 'A')
        
        if not clothing_id or not slot_no:
            return jsonify({'error': '缺少必要参数'}), 400
            
        if not (1 <= slot_no <= 700):
            return jsonify({'error': '格架号必须在1-700之间'}), 400
            
        if lane not in ['A', 'B']:
            return jsonify({'error': '输送线侧别必须是A或B'}), 400
        
        # 查找衣物
        clothing = Clothing.query.get(clothing_id)
        if not clothing:
            return jsonify({'error': '衣物不存在'}), 404
            
        # 权限检查：普通营业员只能操作自己的订单
        staff_name = session.get('staff_name')
        staff_role = session.get('staff_role')
        if staff_role != 'manager':
            order = clothing.order
            if not order or order.operator != staff_name:
                return jsonify({'error': '您没有权限操作此衣物'}), 403
        
        # 检查格架是否已被占用
        existing = Clothing.query.filter_by(lane=lane, slot_no=slot_no).first()
        if existing and existing.id != clothing_id:
            return jsonify({'error': f'格架 {lane}-{slot_no} 已被占用'}), 400
            
        # 检查衣物是否已分配格架
        if clothing.slot_no:
            return jsonify({'error': f'衣物已分配到格架 {clothing.lane}-{clothing.slot_no}'}), 400
        
        # 获取输送线配置
        config = get_conveyor_config(staff_name)
        if not config:
            return jsonify({'error': '未找到输送线配置'}), 500
            
        # 发送UDP指令控制输送线
        result = send_conveyor_command(
            ip=config['ip'],
            port=config['port'],
            side=lane,
            slot_no=slot_no
        )
        
        if not result['success']:
            return jsonify({'error': f'输送线控制失败: {result["message"]}'}), 500
        
        # 更新衣物格架信息
        clothing.slot_no = slot_no
        clothing.lane = lane
        clothing.slot_time = datetime.datetime.now()
        
        # 记录日志
        log = ConveyorLog(
            store='默认门店',
            clothing_id=clothing_id,
            slot_no=slot_no,
            lane=lane,
            barcode=clothing.barcode,
            hex_cmd=result["command"],
            action='入架',
            operator=staff_name,
            remarks=f'UDP指令: {result["command"]}',
            remark=f'分配格架 {lane}-{slot_no}'
        )
        db.session.add(log)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'成功分配格架 {lane}-{slot_no}',
            'slot_info': {
                'lane': lane,
                'slot_no': slot_no,
                'barcode': clothing.barcode,
                'clothing_name': clothing.name,
                'slot_time': clothing.slot_time.isoformat()
            },
            'conveyor_result': result
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"分配格架失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@bp.route('/api/slots/release', methods=['POST'])
@login_required
def release_slot():
    """释放格架API
    
    释放指定衣物的格架分配
    """
    try:
        data = request.json
        clothing_id = data.get('clothing_id')
        
        if not clothing_id:
            return jsonify({'error': '缺少衣物ID'}), 400
        
        # 查找衣物
        clothing = Clothing.query.get(clothing_id)
        if not clothing:
            return jsonify({'error': '衣物不存在'}), 404
            
        # 权限检查
        staff_name = session.get('staff_name')
        staff_role = session.get('staff_role')
        if staff_role != 'manager':
            order = clothing.order
            if not order or order.operator != staff_name:
                return jsonify({'error': '您没有权限操作此衣物'}), 403
        
        if not clothing.slot_no:
            return jsonify({'error': '衣物未分配格架'}), 400
            
        # 记录释放前的信息
        old_slot_no = clothing.slot_no
        old_lane = clothing.lane
        
        # 清空格架信息
        clothing.slot_no = None
        clothing.lane = 'A'  # 重置为默认值
        clothing.slot_time = None
        
        # 记录日志
        log = ConveyorLog(
            store='默认门店',
            clothing_id=clothing_id,
            slot_no=old_slot_no,
            lane=old_lane,
            barcode=clothing.barcode,
            hex_cmd='RELEASE',
            action='出架',
            operator=staff_name,
            remarks='手动释放格架',
            remark=f'释放格架 {old_lane}-{old_slot_no}'
        )
        db.session.add(log)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'成功释放格架 {old_lane}-{old_slot_no}',
            'released_slot': {
                'lane': old_lane,
                'slot_no': old_slot_no
            }
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"释放格架失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/api/slots/move', methods=['POST'])
@login_required
def move_conveyor():
    """手动移动输送线API
    
    直接控制输送线移动到指定位置（无衣物绑定）
    """
    try:
        data = request.json
        slot_no = data.get('slot_no')
        lane = data.get('lane', 'A')
        
        if not slot_no:
            return jsonify({'error': '缺少格架号'}), 400
            
        if not (1 <= slot_no <= 700):
            return jsonify({'error': '格架号必须在1-700之间'}), 400
            
        if lane not in ['A', 'B']:
            return jsonify({'error': '输送线侧别必须是A或B'}), 400
        
        # 检查权限：只有管理员可以手动移动
        staff_role = session.get('staff_role')
        if staff_role != 'manager':
            return jsonify({'error': '只有管理员可以手动移动输送线'}), 403
        
        # 获取输送线配置
        staff_name = session.get('staff_name')
        config = get_conveyor_config(staff_name)
        if not config:
            return jsonify({'error': '未找到输送线配置'}), 500
            
        # 发送UDP指令
        result = send_conveyor_command(
            ip=config['ip'],
            port=config['port'],
            side=lane,
            slot_no=slot_no
        )
        
        if not result['success']:
            return jsonify({'error': f'输送线控制失败: {result["message"]}'}), 500
        
        # 记录操作日志
        log = ConveyorLog(
            store='默认门店',
            clothing_id=None,  # 手动移动无关联衣物
            slot_no=slot_no,
            lane=lane,
            barcode=None,
            hex_cmd=result["command"],
            action='手动移动',
            operator=staff_name,
            remarks=f'手动移动到格架 {lane}-{slot_no}, UDP指令: {result["command"]}',
            remark=f'手动移动到格架 {lane}-{slot_no}'
        )
        db.session.add(log)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'输送线已移动到格架 {lane}-{slot_no}',
            'conveyor_result': result
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"移动输送线失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/api/slots/search', methods=['GET'])
@login_required
def search_slots():
    """搜索格架API
    
    根据条件搜索格架和衣物
    """
    try:
        # 获取搜索参数
        barcode = request.args.get('barcode', '').strip()
        order_number = request.args.get('order_number', '').strip()
        slot_no = request.args.get('slot_no', '').strip()
        lane = request.args.get('lane', '').strip()
        
        if not any([barcode, order_number, slot_no]):
            return jsonify({'error': '请提供搜索条件'}), 400
        
        # 构建查询
        query = db.session.query(Clothing).filter(Clothing.slot_no.isnot(None))
        
        if barcode:
            query = query.filter(Clothing.barcode.like(f'%{barcode}%'))
            
        if order_number:
            query = query.join(Order).filter(Order.order_number.like(f'%{order_number}%'))
            
        if slot_no:
            try:
                slot_no_int = int(slot_no)
                query = query.filter(Clothing.slot_no == slot_no_int)
            except ValueError:
                return jsonify({'error': '格架号必须是数字'}), 400
                
        if lane:
            query = query.filter(Clothing.lane == lane.upper())
        
        # 权限控制
        staff_name = session.get('staff_name')
        staff_role = session.get('staff_role')
        if staff_role != 'manager':
            if not query.join(Order, isouter=True).filter(Order.operator == staff_name):
                query = query.join(Order).filter(Order.operator == staff_name)
        
        results = query.all()
        
        # 构建结果数据
        search_results = []
        for clothing in results:
            order = clothing.order
            customer = order.customer if order else None
            
            search_results.append({
                'clothing_id': clothing.id,
                'barcode': clothing.barcode,
                'clothing_name': clothing.name,
                'clothing_color': clothing.color,
                'lane': clothing.lane,
                'slot_no': clothing.slot_no,
                'slot_time': clothing.slot_time.isoformat() if clothing.slot_time else '',
                'order_number': order.order_number if order else '',
                'customer_name': customer.name if customer else '',
                'customer_phone': customer.phone if customer else '',
                'operator': order.operator if order else ''
            })
        
        return jsonify({
            'success': True,
            'results': search_results,
            'count': len(search_results)
        })
        
    except Exception as e:
        print(f"搜索格架失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/api/conveyor/logs', methods=['GET'])
@login_required
def get_conveyor_logs():
    """获取输送线操作日志"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        # 构建查询
        query = ConveyorLog.query.order_by(ConveyorLog.created_at.desc())
        
        # 权限控制：普通营业员只能看自己的操作日志
        staff_name = session.get('staff_name')
        staff_role = session.get('staff_role')
        if staff_role != 'manager':
            query = query.filter(ConveyorLog.operator == staff_name)
        
        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        logs_data = []
        for log in pagination.items:
            clothing = log.clothing
            logs_data.append({
                'id': log.id,
                'clothing_id': log.clothing_id,
                'barcode': clothing.barcode if clothing else '',
                'clothing_name': clothing.name if clothing else '',
                'slot_no': log.slot_no,
                'lane': log.lane,
                'action': log.action,
                'operator': log.operator,
                'remarks': log.remarks,
                'created_at': log.created_at.isoformat()
            })
        
        return jsonify({
            'success': True,
            'logs': logs_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })
        
    except Exception as e:
        print(f"获取操作日志失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/api/slots/statistics', methods=['GET'])
@login_required
def get_slots_statistics():
    """获取格架使用统计"""
    try:
        staff_name = session.get('staff_name')
        staff_role = session.get('staff_role')
        
        # 基础统计查询
        base_query = db.session.query(Clothing).filter(Clothing.slot_no.isnot(None))
        
        # 权限控制
        if staff_role != 'manager':
            base_query = base_query.join(Order).filter(Order.operator == staff_name)
        
        # 总占用数
        total_occupied = base_query.count()
        
        # 按侧别统计
        lane_stats = {}
        for lane in ['A', 'B']:
            lane_count = base_query.filter(Clothing.lane == lane).count()
            lane_stats[lane] = {
                'occupied': lane_count,
                'free': 700 - lane_count,
                'usage_rate': round(lane_count / 700 * 100, 2)
            }
        
        # 按时间段统计（今日、本周、本月入架数量）
        from datetime import datetime, timedelta
        now = datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today_start - timedelta(days=now.weekday())
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        time_stats = {
            'today': base_query.filter(Clothing.slot_time >= today_start).count(),
            'this_week': base_query.filter(Clothing.slot_time >= week_start).count(),
            'this_month': base_query.filter(Clothing.slot_time >= month_start).count()
        }
        
        # 按操作员统计（仅管理员可见）
        operator_stats = {}
        if staff_role == 'manager':
            operator_query = db.session.query(
                Order.operator,
                db.func.count(Clothing.id).label('count')
            ).join(Clothing, Order.id == Clothing.order_id).filter(
                Clothing.slot_no.isnot(None)
            ).group_by(Order.operator).all()
            
            for operator, count in operator_query:
                operator_stats[operator or '未知'] = count
        
        # 格架周转率（最近7天的入架/出架次数）
        week_ago = now - timedelta(days=7)
        log_query = ConveyorLog.query.filter(ConveyorLog.created_at >= week_ago)
        
        if staff_role != 'manager':
            log_query = log_query.filter(ConveyorLog.operator == staff_name)
            
        weekly_operations = {
            'assignments': log_query.filter(ConveyorLog.action.in_(['入架', '自动入架'])).count(),
            'releases': log_query.filter(ConveyorLog.action.in_(['出架', '自动出架'])).count()
        }
        
        return jsonify({
            'success': True,
            'statistics': {
                'total_slots': 1400,
                'total_occupied': total_occupied,
                'total_free': 1400 - total_occupied,
                'overall_usage_rate': round(total_occupied / 1400 * 100, 2),
                'lane_stats': lane_stats,
                'time_stats': time_stats,
                'operator_stats': operator_stats,
                'weekly_operations': weekly_operations
            }
        })
        
    except Exception as e:
        print(f"获取格架统计失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/api/slots/batch_release', methods=['POST'])
@login_required
def batch_release_slots():
    """批量释放格架"""
    try:
        data = request.json
        clothing_ids = data.get('clothing_ids', [])
        
        if not clothing_ids:
            return jsonify({'error': '请选择要释放的格架'}), 400
        
        staff_name = session.get('staff_name')
        staff_role = session.get('staff_role')
        
        released_count = 0
        failed_count = 0
        errors = []
        
        for clothing_id in clothing_ids:
            try:
                clothing = Clothing.query.get(clothing_id)
                if not clothing:
                    failed_count += 1
                    continue
                
                # 权限检查
                if staff_role != 'manager':
                    order = clothing.order
                    if not order or order.operator != staff_name:
                        failed_count += 1
                        errors.append(f'衣物{clothing.barcode}：权限不足')
                        continue
                
                if not clothing.slot_no:
                    failed_count += 1
                    errors.append(f'衣物{clothing.barcode}：未分配格架')
                    continue
                
                # 记录释放前信息
                old_slot_no = clothing.slot_no
                old_lane = clothing.lane
                
                # 清空格架信息
                clothing.slot_no = None
                clothing.lane = 'A'
                clothing.slot_time = None
                
                # 记录日志
                log = ConveyorLog(
                    store='默认门店',
                    clothing_id=clothing_id,
                    slot_no=old_slot_no,
                    lane=old_lane,
                    barcode=clothing.barcode,
                    hex_cmd='BATCH_RELEASE',
                    action='批量出架',
                    operator=staff_name,
                    remarks=f'批量释放格架 {old_lane}-{old_slot_no}',
                    remark=f'批量释放格架 {old_lane}-{old_slot_no}'
                )
                db.session.add(log)
                released_count += 1
                
            except Exception as e:
                failed_count += 1
                errors.append(f'衣物ID{clothing_id}：{str(e)}')
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'成功释放{released_count}个格架，失败{failed_count}个',
            'released_count': released_count,
            'failed_count': failed_count,
            'errors': errors
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"批量释放格架失败: {str(e)}")
        return jsonify({'error': str(e)}), 500 