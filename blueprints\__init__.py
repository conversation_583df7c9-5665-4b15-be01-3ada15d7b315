from importlib import import_module
from typing import List

# 可在此列表中添加需要自动注册的蓝图模块名（无需扩展名）
_BLUEPRINT_MODULES: List[str] = [
    'auth',
    'customer',
    'order',
    'query',
    'member',
    'mall_customer',
    'product',
    'status',
    'print',
    'admin',
    'recharge',
    'conveyor',
]


def register_blueprints(app):
    """批量导入并注册 blueprints 下的各业务模块。"""
    for name in _BLUEPRINT_MODULES:
        module = import_module(f'blueprints.{name}')
        if hasattr(module, 'bp'):
            app.register_blueprint(module.bp)
    
 