@media (max-width: 768px) {
    /* General body adjustments */
    body {
        margin: 10px !important;
        font-size: 14px !important;
        padding-bottom: 70px !important;
    }

    /* Header arranged vertically */
    .header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
    }

    /* Section spacing */
    .section {
        padding: 15px !important;
        margin-bottom: 15px !important;
    }

    /* Section title number and heading inline shrink */
    .section-number {
        width: 24px !important;
        height: 24px !important;
        font-size: 14px !important;
    }

    .section-title h2 {
        font-size: 16px !important;
    }

    /* Form controls full width */
    input,
    select,
    textarea,
    button {
        font-size: 14px !important;
    }

    button {
        width: 100% !important;
        padding: 10px !important;
    }

    /* Service type checkboxes stacked */
    .service-types {
        flex-direction: column !important;
        gap: 6px !important;
    }

    /* Collapsible clothing items layout tweaks */
    .clothing-item-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px !important;
        margin-right: 0 !important;
    }

    .remove-item {
        right: 5px !important;
        top: 5px !important;
        width: 24px !important;
        height: 24px !important;
    }

    /* Tab navigation scrollable */
    .tab-navigation {
        overflow-x: auto !important;
    }

    .tab-button {
        flex: none !important;
        font-size: 14px !important;
        padding: 8px 12px !important;
    }

    /* Next-step button full width */
    .next-step {
        width: 100% !important;
    }

    /* Camera feed responsive */
    #cameraFeed {
        max-width: 100% !important;
    }

    /* Hide header controls on mobile */
    .header-controls {
        display: none !important;
    }

    /* Bottom navigation bar */
    .mobile-bottom-nav {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 56px !important;
        background: #ffffff !important;
        border-top: 1px solid #ddd !important;
        display: flex !important;
        justify-content: space-around !important;
        align-items: center !important;
        z-index: 1000 !important;
    }

    .mobile-bottom-nav .nav-item {
        flex: 1 1 auto !important;
        text-align: center !important;
        color: #666 !important;
        font-size: 12px !important;
        text-decoration: none !important;
    }

    .mobile-bottom-nav .nav-item .nav-icon {
        font-size: 20px !important;
        display: block !important;
        line-height: 20px !important;
        margin-bottom: 2px !important;
    }

    .mobile-bottom-nav .nav-item.active,
    .mobile-bottom-nav .nav-item:active {
        color: #007BFF !important;
    }

    /* Hide top step tabs and pagination on mobile */
    .tab-navigation,
    .pagination-controls {
        display: none !important;
    }
} 