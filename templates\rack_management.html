<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>格架管理 - 洗衣店管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .slot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 2px;
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 10px;
            background-color: #f8f9fa;
        }
        
        .slot-item {
            width: 60px;
            height: 40px;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .slot-free {
            background-color: #d4edda;
            color: #155724;
        }
        
        .slot-occupied {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .slot-selected {
            background-color: #fff3cd;
            border-color: #ffc107;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }
        
        .lane-section {
            margin-bottom: 20px;
        }
        
        .lane-header {
            background-color: #495057;
            color: white;
            padding: 8px 15px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        
        .search-panel {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-panel {
            background-color: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .slot-tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">格架管理</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <select id="storeSelect" class="form-select me-2" style="width: 200px; display: none;">
                    <option value="">选择门店...</option>
                </select>
                <button type="button" class="btn btn-info me-2" id="storeConfigBtn" onclick="showStoreConfigModal()" style="display: none;">
                    <i class="bi bi-gear"></i> 门店配置
                </button>
                <button type="button" class="btn btn-primary me-2" onclick="refreshSlots()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
                <button type="button" class="btn btn-success me-2" onclick="showAssignModal()">
                    <i class="bi bi-plus-circle"></i> 分配格架
                </button>
                <button type="button" class="btn btn-warning" onclick="showMoveModal()">
                    <i class="bi bi-arrow-left-right"></i> 手动移动
                </button>
            </div>
        </div>

        <!-- 搜索面板 -->
        <div class="search-panel">
            <h5><i class="bi bi-search"></i> 搜索格架</h5>
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">条码</label>
                    <input type="text" class="form-control" id="searchBarcode" placeholder="扫码或输入条码">
                </div>
                <div class="col-md-3">
                    <label class="form-label">订单号</label>
                    <input type="text" class="form-control" id="searchOrderNumber" placeholder="输入订单号">
                </div>
                <div class="col-md-2">
                    <label class="form-label">格架号</label>
                    <input type="number" class="form-control" id="searchSlotNo" placeholder="1-700" min="1" max="700">
                </div>
                <div class="col-md-2">
                    <label class="form-label">侧别</label>
                    <select class="form-select" id="searchLane">
                        <option value="">全部</option>
                        <option value="A">A侧</option>
                        <option value="B">B侧</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button class="btn btn-primary" onclick="searchSlots()">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计面板 -->
        <div class="stats-panel">
            <div class="row text-center">
                <div class="col-md-3">
                    <h5 class="text-primary" id="totalSlots">-</h5>
                    <small>总格架数</small>
                </div>
                <div class="col-md-3">
                    <h5 class="text-success" id="freeSlots">-</h5>
                    <small>空闲格架</small>
                </div>
                <div class="col-md-3">
                    <h5 class="text-danger" id="occupiedSlots">-</h5>
                    <small>已占用</small>
                </div>
                <div class="col-md-3">
                    <h5 class="text-info" id="usageRate">-</h5>
                    <small>使用率</small>
                </div>
            </div>
            <div class="row mt-2" id="currentStoreInfo" style="display: none;">
                <div class="col-12 text-center">
                    <small class="text-muted">当前门店：<span id="currentStoreName" class="fw-bold">-</span></small>
                </div>
            </div>
        </div>

        <!-- 格架显示区域 -->
        <div class="row">
            <div class="col-md-6">
                <div class="lane-section">
                    <div class="lane-header">
                        <h5 class="mb-0"><i class="bi bi-arrow-left"></i> A侧输送线 (1-700)</h5>
                    </div>
                    <div class="slot-grid" id="slotsA"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="lane-section">
                    <div class="lane-header">
                        <h5 class="mb-0"><i class="bi bi-arrow-right"></i> B侧输送线 (1-700)</h5>
                    </div>
                    <div class="slot-grid" id="slotsB"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分配格架模态框 -->
    <div class="modal fade" id="assignModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">分配格架</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">衣物条码</label>
                        <input type="text" class="form-control" id="assignBarcode" placeholder="扫码或输入条码">
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">格架号</label>
                            <input type="number" class="form-control" id="assignSlotNo" placeholder="1-700" min="1" max="700">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">输送线侧别</label>
                            <select class="form-select" id="assignLane">
                                <option value="A">A侧</option>
                                <option value="B">B侧</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3" id="clothingInfo" style="display: none;">
                        <div class="alert alert-info">
                            <strong>衣物信息：</strong><br>
                            <span id="clothingDetails"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="assignSlot()">确认分配</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 手动移动模态框 -->
    <div class="modal fade" id="moveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">手动移动输送线</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        此操作将直接控制输送线移动，请确保目标位置安全。
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">目标格架号</label>
                            <input type="number" class="form-control" id="moveSlotNo" placeholder="1-700" min="1" max="700">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">输送线侧别</label>
                            <select class="form-select" id="moveLane">
                                <option value="A">A侧</option>
                                <option value="B">B侧</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="moveConveyor()">确认移动</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷键提示 -->
    <div class="alert alert-info mt-3">
        <strong>💡 快捷键提示：</strong>
        <kbd>Ctrl+R</kbd> 刷新 | 
        <kbd>Ctrl+A</kbd> 分配格架 | 
        <kbd>Ctrl+F</kbd> 搜索焦点 | 
        <kbd>ESC</kbd> 关闭弹窗 |
        <kbd>Enter</kbd> 确认操作
    </div>

    <!-- 门店配置模态框 -->
    <div class="modal fade" id="storeConfigModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">门店配置管理</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6>门店营业员管理</h6>
                        <button class="btn btn-primary btn-sm" onclick="showAddStoreModal()">
                            <i class="bi bi-plus"></i> 添加营业员
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>营业员姓名</th>
                                    <th>用户名</th>
                                    <th>区域</th>
                                    <th>电话</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="storeConfigTableBody">
                                <tr>
                                    <td colspan="6" class="text-center">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加营业员模态框 -->
    <div class="modal fade" id="addStoreModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加营业员</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">营业员姓名</label>
                        <input type="text" class="form-control" id="newStaffName" placeholder="请输入营业员姓名">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" id="newStaffUsername" placeholder="登录用户名">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-control" id="newStaffPassword" placeholder="登录密码">
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">所属区域</label>
                            <input type="text" class="form-control" id="newStaffArea" placeholder="例: 分店1">
                        </div>
                        <div class="col-6">
                            <label class="form-label">联系电话</label>
                            <input type="text" class="form-control" id="newStaffPhone" placeholder="手机号码">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addStore()">确认添加</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 工具提示 -->
    <div class="slot-tooltip" id="slotTooltip" style="display: none;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let slotsData = [];
        let selectedSlot = null;
        let tooltip = document.getElementById('slotTooltip');

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否是管理员，如果是则显示门店选择功能
            checkManagerPermissions();
            
            refreshSlots();
            
            // 绑定条码输入框自动查询
            document.getElementById('assignBarcode').addEventListener('input', function() {
                const barcode = this.value.trim();
                if (barcode.length >= 6) {
                    checkClothingByBarcode(barcode);
                }
            });
            
            // 门店选择change事件
            document.getElementById('storeSelect').addEventListener('change', function() {
                const selectedStore = this.value;
                refreshSlots(selectedStore);
                updateCurrentStoreInfo(selectedStore);
            });
            
            // 绑定Enter键快速分配
            document.getElementById('assignBarcode').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const barcode = this.value.trim();
                    if (barcode && document.getElementById('assignSlotNo').value) {
                        assignSlot();
                    }
                }
            });
            
            // 绑定搜索输入框的Enter键
            document.getElementById('searchBarcode').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchSlots();
                }
            });
            
            // 键盘快捷键支持
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'r':
                        case 'R':
                            e.preventDefault();
                            refreshSlots();
                            break;
                        case 'a':
                        case 'A':
                            e.preventDefault();
                            showAssignModal();
                            break;
                        case 'f':
                        case 'F':
                            e.preventDefault();
                            document.getElementById('searchBarcode').focus();
                            break;
                    }
                }
                
                // ESC键关闭模态框
                if (e.key === 'Escape') {
                    const modals = document.querySelectorAll('.modal.show');
                    modals.forEach(modal => {
                        bootstrap.Modal.getInstance(modal)?.hide();
                    });
                }
            });
        });

        // 刷新格架状态
        function refreshSlots(storeName = '') {
            let url = '/api/slots';
            if (storeName) {
                url += `?store=${encodeURIComponent(storeName)}`;
            }
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        slotsData = data.slots;
                        updateSlotsDisplay();
                        updateStats(data.summary);
                    } else {
                        alert('获取格架状态失败: ' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
        }

        // 更新格架显示
        function updateSlotsDisplay() {
            const slotsA = document.getElementById('slotsA');
            const slotsB = document.getElementById('slotsB');
            
            slotsA.innerHTML = '';
            slotsB.innerHTML = '';
            
            slotsData.forEach(slot => {
                const slotElement = createSlotElement(slot);
                if (slot.lane === 'A') {
                    slotsA.appendChild(slotElement);
                } else {
                    slotsB.appendChild(slotElement);
                }
            });
        }

        // 创建格架元素
        function createSlotElement(slot) {
            const div = document.createElement('div');
            div.className = `slot-item slot-${slot.status}`;
            div.textContent = slot.slot_no;
            div.dataset.lane = slot.lane;
            div.dataset.slotNo = slot.slot_no;
            
            // 鼠标事件
            div.addEventListener('click', function() {
                selectSlot(slot);
            });
            
            div.addEventListener('mouseenter', function(e) {
                showTooltip(e, slot);
            });
            
            div.addEventListener('mouseleave', function() {
                hideTooltip();
            });
            
            return div;
        }

        // 选择格架
        function selectSlot(slot) {
            // 清除之前的选择
            document.querySelectorAll('.slot-selected').forEach(el => {
                el.classList.remove('slot-selected');
            });
            
            // 标记新选择
            const slotElement = document.querySelector(`[data-lane="${slot.lane}"][data-slot-no="${slot.slot_no}"]`);
            if (slotElement) {
                slotElement.classList.add('slot-selected');
                selectedSlot = slot;
                
                if (slot.status === 'occupied') {
                    // 已占用的格架，显示详情或释放选项
                    if (confirm(`格架 ${slot.lane}-${slot.slot_no} 已被占用\n\n衣物: ${slot.clothing_name} (${slot.clothing_color})\n条码: ${slot.barcode}\n客户: ${slot.customer_name}\n\n是否释放此格架？`)) {
                        releaseSlot(slot.clothing_id);
                    }
                } else {
                    // 空闲格架，填入分配表单
                    document.getElementById('assignSlotNo').value = slot.slot_no;
                    document.getElementById('assignLane').value = slot.lane;
                }
            }
        }

        // 显示提示
        function showTooltip(event, slot) {
            if (slot.status === 'occupied') {
                tooltip.innerHTML = `
                    <strong>格架 ${slot.lane}-${slot.slot_no}</strong><br>
                    条码: ${slot.barcode}<br>
                    衣物: ${slot.clothing_name} (${slot.clothing_color})<br>
                    订单: ${slot.order_number}<br>
                    客户: ${slot.customer_name}<br>
                    入架时间: ${slot.slot_time}
                `;
            } else {
                tooltip.innerHTML = `<strong>格架 ${slot.lane}-${slot.slot_no}</strong><br>空闲`;
            }
            
            tooltip.style.display = 'block';
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 10 + 'px';
        }

        // 隐藏提示
        function hideTooltip() {
            tooltip.style.display = 'none';
        }

        // 更新统计信息
        function updateStats(summary) {
            document.getElementById('totalSlots').textContent = summary.total_slots;
            document.getElementById('freeSlots').textContent = summary.free_count;
            document.getElementById('occupiedSlots').textContent = summary.occupied_count;
            document.getElementById('usageRate').textContent = summary.usage_rate + '%';
        }

        // 显示分配模态框
        function showAssignModal() {
            new bootstrap.Modal(document.getElementById('assignModal')).show();
            // 聚焦到条码输入框，方便扫码
            setTimeout(() => {
                document.getElementById('assignBarcode').focus();
            }, 500);
        }

        // 显示移动模态框
        function showMoveModal() {
            new bootstrap.Modal(document.getElementById('moveModal')).show();
        }

        // 根据条码检查衣物
        function checkClothingByBarcode(barcode) {
            fetch(`/api/slots/search?barcode=${encodeURIComponent(barcode)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.results.length > 0) {
                        const clothing = data.results[0];
                        // 检查衣物是否已分配格架
                        if (clothing.slot_no) {
                            document.getElementById('clothingDetails').innerHTML = `
                                <div class="alert alert-warning">
                                    <strong>注意：</strong>此衣物已分配格架 ${clothing.lane}-${clothing.slot_no}<br>
                                    衣物: ${clothing.clothing_name} (${clothing.clothing_color})<br>
                                    订单: ${clothing.order_number}<br>
                                    客户: ${clothing.customer_name}
                                </div>
                            `;
                        } else {
                        document.getElementById('clothingDetails').innerHTML = `
                            衣物: ${clothing.clothing_name} (${clothing.clothing_color})<br>
                            订单: ${clothing.order_number}<br>
                            客户: ${clothing.customer_name}
                        `;
                        }
                        document.getElementById('clothingInfo').style.display = 'block';
                    } else {
                        document.getElementById('clothingInfo').style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('clothingInfo').style.display = 'none';
                });
        }

        // 分配格架
        function assignSlot() {
            const barcode = document.getElementById('assignBarcode').value.trim();
            const slotNo = document.getElementById('assignSlotNo').value;
            const lane = document.getElementById('assignLane').value;
            
            if (!barcode || !slotNo) {
                alert('请填写完整信息');
                return;
            }
            
            // 先查找衣物ID
            fetch(`/api/slots/search?barcode=${encodeURIComponent(barcode)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.results.length > 0) {
                        const clothingId = data.results[0].clothing_id;
                        
                        // 发送分配请求
                        return fetch('/api/slots/assign', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                clothing_id: clothingId,
                                slot_no: parseInt(slotNo),
                                lane: lane
                            })
                        });
                    } else {
                        throw new Error('找不到对应的衣物');
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('格架分配成功！');
                        bootstrap.Modal.getInstance(document.getElementById('assignModal')).hide();
                        refreshSlots();
                        
                        // 清空表单
                        document.getElementById('assignBarcode').value = '';
                        document.getElementById('assignSlotNo').value = '';
                        document.getElementById('clothingInfo').style.display = 'none';
                    } else {
                        alert('分配失败: ' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败: ' + error.message);
                });
        }

        // 释放格架
        function releaseSlot(clothingId) {
            fetch('/api/slots/release', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    clothing_id: clothingId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('格架释放成功！');
                    refreshSlots();
                } else {
                    alert('释放失败: ' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 手动移动输送线
        function moveConveyor() {
            const slotNo = document.getElementById('moveSlotNo').value;
            const lane = document.getElementById('moveLane').value;
            
            if (!slotNo) {
                alert('请输入目标格架号');
                return;
            }
            
            fetch('/api/slots/move', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    slot_no: parseInt(slotNo),
                    lane: lane
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('输送线移动成功！');
                    bootstrap.Modal.getInstance(document.getElementById('moveModal')).hide();
                    document.getElementById('moveSlotNo').value = '';
                } else {
                    alert('移动失败: ' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 搜索格架
        function searchSlots() {
            const params = new URLSearchParams();
            
            const barcode = document.getElementById('searchBarcode').value.trim();
            const orderNumber = document.getElementById('searchOrderNumber').value.trim();
            const slotNo = document.getElementById('searchSlotNo').value.trim();
            const lane = document.getElementById('searchLane').value.trim();
            
            if (barcode) params.append('barcode', barcode);
            if (orderNumber) params.append('order_number', orderNumber);
            if (slotNo) params.append('slot_no', slotNo);
            if (lane) params.append('lane', lane);
            
            if (params.toString() === '') {
                alert('请输入搜索条件');
                return;
            }
            
            fetch(`/api/slots/search?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.results.length > 0) {
                            // 高亮搜索结果
                            highlightSearchResults(data.results);
                            alert(`找到 ${data.results.length} 个结果`);
                        } else {
                            alert('未找到匹配的结果');
                        }
                    } else {
                        alert('搜索失败: ' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
        }

        // 高亮搜索结果
        function highlightSearchResults(results) {
            // 先清除之前的高亮
            document.querySelectorAll('.slot-selected').forEach(el => {
                el.classList.remove('slot-selected');
            });
            
            // 高亮搜索结果
            results.forEach(result => {
                const slotElement = document.querySelector(`[data-lane="${result.lane}"][data-slot-no="${result.slot_no}"]`);
                if (slotElement) {
                    slotElement.classList.add('slot-selected');
                }
            });
        }
        
        // 检查管理员权限
        function checkManagerPermissions() {
            fetch('/api/admin/stores')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 是管理员，显示门店选择和配置功能
                        document.getElementById('storeSelect').style.display = 'block';
                        document.getElementById('storeConfigBtn').style.display = 'block';
                        
                        // 填充门店选项
                        const select = document.getElementById('storeSelect');
                        select.innerHTML = '<option value="">所有门店</option>';
                        data.stores.forEach(store => {
                            select.innerHTML += `<option value="${store.store_name}">${store.store_name}</option>`;
                        });
                    }
                })
                .catch(error => {
                    // 不是管理员或网络错误，隐藏管理员功能
                    console.log('非管理员用户或网络错误');
                });
        }
        
        // 更新当前门店信息显示
        function updateCurrentStoreInfo(storeName) {
            const currentStoreInfo = document.getElementById('currentStoreInfo');
            const currentStoreName = document.getElementById('currentStoreName');
            
            if (storeName) {
                currentStoreName.textContent = storeName;
                currentStoreInfo.style.display = 'block';
            } else {
                currentStoreName.textContent = '所有门店';
                currentStoreInfo.style.display = 'block';
            }
        }
        
        // 显示门店配置模态框
        function showStoreConfigModal() {
            const modal = new bootstrap.Modal(document.getElementById('storeConfigModal'));
            modal.show();
            loadStoreConfigs();
        }
        
        // 显示添加门店模态框
        function showAddStoreModal() {
            const modal = new bootstrap.Modal(document.getElementById('addStoreModal'));
            modal.show();
        }
        
        // 加载门店配置（营业员列表）
        function loadStoreConfigs() {
            fetch('/api/admin/store-configs')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const tbody = document.getElementById('storeConfigTableBody');
                        if (data.configs.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无营业员</td></tr>';
                            return;
                        }
                        
                        let html = '';
                        data.configs.forEach(staff => {
                            html += `
                                <tr>
                                    <td>${staff.staff_name}</td>
                                    <td>${staff.staff_username}</td>
                                    <td>${staff.area || ''}</td>
                                    <td>${staff.phone || ''}</td>
                                    <td>
                                        <span class="badge ${staff.is_active ? 'bg-success' : 'bg-secondary'}">
                                            ${staff.is_active ? '启用' : '禁用'}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editStoreConfig(${staff.id})" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm ${staff.is_active ? 'btn-outline-danger' : 'btn-outline-success'}" onclick="toggleStoreStatus(${staff.id}, ${!staff.is_active})" title="${staff.is_active ? '禁用' : '启用'}">
                                            <i class="bi bi-${staff.is_active ? 'pause' : 'play'}"></i>
                                        </button>
                                    </td>
                                </tr>
                            `;
                        });
                        tbody.innerHTML = html;
                    } else {
                        console.error('加载营业员失败:', data.error);
                    }
                })
                .catch(error => {
                    console.error('加载营业员失败:', error);
                });
        }
        
        // 添加营业员
        function addStore() {
            const staffName = document.getElementById('newStaffName').value.trim();
            const username = document.getElementById('newStaffUsername').value.trim();
            const password = document.getElementById('newStaffPassword').value.trim();
            const area = document.getElementById('newStaffArea').value.trim();
            const phone = document.getElementById('newStaffPhone').value.trim();
            
            if (!staffName || !username || !password) {
                alert('请填写营业员姓名、用户名和密码');
                return;
            }
            
            fetch('/api/admin/store-configs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    staff_name: staffName,
                    username: username,
                    password: password,
                    area: area,
                    phone: phone
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('营业员添加成功');
                    bootstrap.Modal.getInstance(document.getElementById('addStoreModal')).hide();
                    loadStoreConfigs();
                    checkManagerPermissions(); // 重新加载门店选项
                    
                    // 清空表单
                    document.getElementById('newStaffName').value = '';
                    document.getElementById('newStaffUsername').value = '';
                    document.getElementById('newStaffPassword').value = '';
                    document.getElementById('newStaffArea').value = '';
                    document.getElementById('newStaffPhone').value = '';
                } else {
                    alert(`添加失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('添加门店配置失败:', error);
                alert('添加门店配置时出错，请重试');
            });
        }
        
        // 编辑门店配置
        function editStoreConfig(configId) {
            alert('编辑功能开发中...');
        }
        
        // 切换门店状态
        function toggleStoreStatus(configId, newStatus) {
            fetch(`/api/admin/store-configs/${configId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    is_active: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadStoreConfigs();
                    alert(`门店状态已${newStatus ? '启用' : '禁用'}`);
                } else {
                    alert(`操作失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('切换门店状态失败:', error);
                alert('操作失败，请重试');
            });
        }
    </script>
</body>
</html> 