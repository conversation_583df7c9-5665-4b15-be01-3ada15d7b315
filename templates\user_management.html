<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soulweave改衣坊 - 用户管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #007BFF;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
        }
        .staff-info {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }
        .staff-info span {
            margin-right: 15px;
            color: white;
        }
        .logout-btn {
            color: #fff;
            text-decoration: none;
            font-weight: 500;
        }
        .logout-btn:hover {
            color: #f8f9fa;
            text-decoration: underline;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        .page-title {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .page-title h2 {
            margin: 0;
            color: #333;
        }
        .search-bar {
            display: flex;
            align-items: center;
        }
        .search-bar input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        .search-bar button {
            padding: 8px 15px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .user-list {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 4px;
        }
        .user-list th, .user-list td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .user-list th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .user-list tbody tr:hover {
            background-color: #f9f9f9;
        }
        .user-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: bold;
            display: inline-block;
        }
        .status-active {
            background-color: #e6fff0;
            color: #52c41a;
        }
        .status-inactive {
            background-color: #fff1f0;
            color: #f5222d;
        }
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
            font-size: 0.85rem;
        }
        .edit-btn {
            background-color: #fff8e6;
            color: #f5a623;
        }
        .delete-btn {
            background-color: #fff1f0;
            color: #f5222d;
        }
        .modal-header {
            background-color: #007BFF;
            color: white;
        }
        .modal-footer {
            border-top: 1px solid #eee;
            padding: 15px;
        }
        .form-group label {
            font-weight: bold;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .pagination button {
            padding: 8px 15px;
            margin: 0 5px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .pagination button.active {
            background-color: #007BFF;
            color: white;
            border-color: #007BFF;
        }
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .nav-links {
            display: flex;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .nav-links a {
            padding: 15px 20px;
            color: #333;
            text-decoration: none;
            border-right: 1px solid #eee;
        }
        .nav-links a:last-child {
            border-right: none;
        }
        .nav-links a.active {
            background-color: #007BFF;
            color: white;
        }
        .nav-links a:hover:not(.active) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Soulweave改衣坊管理系统</h1>
        <div class="header-controls">
            <div class="staff-info">
                <span>欢迎，{{ staff_name }}</span>
                <a href="/logout" class="logout-btn">退出</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="nav-links">
            <a href="/">首页</a>
            <a href="/history">订单管理</a>
            <a href="/data_summary">数据统计</a>
            <a href="/rack" style="color: #ff6b35;">📦 格架管理</a>
            {% if session.get('staff_role') == 'admin' %}
            <a href="/user_management" class="active">用户管理</a>
            {% endif %}
        </div>

        <div class="page-title">
            <h2>用户管理</h2>
            <div class="search-bar">
                <select id="area-filter" class="form-control mr-2" style="width: auto;">
                    <option value="">所有区域</option>
                    <!-- 区域选项将通过JavaScript动态加载 -->
                </select>
                <input type="text" id="search-input" placeholder="搜索用户...">
                <button id="search-btn" class="btn btn-primary">搜索</button>
                <button id="add-user-btn" class="btn btn-success ml-2">添加用户</button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="user-list">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>姓名</th>
                        <th>电话</th>
                        <th>角色</th>
                        <th>区域</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>最后登录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="user-list-body">
                    <!-- 用户列表将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
        
        <div id="pagination" class="pagination">
            <!-- 分页控件将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 添加/编辑用户模态框 -->
    <div class="modal fade" id="userModal" tabindex="-1" role="dialog" aria-labelledby="userModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalLabel">添加用户</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="user-id">
                        <div class="form-group">
                            <label for="username">用户名</label>
                            <input type="text" class="form-control" id="username" required>
                        </div>
                        <div class="form-group">
                            <label for="name">姓名</label>
                            <input type="text" class="form-control" id="name" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">电话</label>
                            <input type="text" class="form-control" id="phone">
                        </div>
                        <div class="form-group">
                            <label for="password">密码</label>
                            <input type="password" class="form-control" id="password">
                            <small class="form-text text-muted">编辑用户时，如不修改密码请留空</small>
                        </div>
                        <div class="form-group">
                            <label for="role">角色</label>
                            <select class="form-control" id="role" required>
                                <option value="staff">营业员</option>
                                <option value="manager">管理员</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="area">区域</label>
                            <select class="form-control" id="area">
                                <option value="">无区域</option>
                                <option value="陕西">陕西</option>
                                <option value="哈尔滨">哈尔滨</option>
                                <option value="太原">太原</option>
                                <option value="银川">银川</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="is-active">状态</label>
                            <select class="form-control" id="is-active" required>
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="save-user-btn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除此用户吗？此操作无法撤销。</p>
                    <input type="hidden" id="delete-user-id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn">删除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let searchKeyword = '';
        
        document.addEventListener('DOMContentLoaded', function() {
            // 加载区域列表
            loadAreas();
            
            // 加载用户列表
            loadUsers();
            
            // 搜索按钮点击事件
            document.getElementById('search-btn').addEventListener('click', function() {
                searchKeyword = document.getElementById('search-input').value.trim();
                currentPage = 1;
                loadUsers();
            });
            
            // 区域筛选下拉框变更事件
            document.getElementById('area-filter').addEventListener('change', function() {
                currentPage = 1;
                loadUsers();
            });
            
            // 添加用户按钮点击事件
            document.getElementById('add-user-btn').addEventListener('click', function() {
                resetUserForm();
                document.getElementById('userModalLabel').textContent = '添加用户';
                $('#userModal').modal('show');
            });
            
            // 保存用户按钮点击事件
            document.getElementById('save-user-btn').addEventListener('click', saveUser);
            
            // 确认删除按钮点击事件
            document.getElementById('confirm-delete-btn').addEventListener('click', deleteUser);
        });
        
        // 加载区域列表
        function loadAreas() {
            fetch('/api/areas')
                .then(response => {
                    if (!response.ok) {
                        if (response.status === 401 || response.status === 403) {
                            // 权限不足或未登录，忽略错误
                            return;
                        }
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        const areaSelect = document.getElementById('area-filter');
                        data.areas.forEach(area => {
                            const option = document.createElement('option');
                            option.value = area;
                            option.textContent = area;
                            areaSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载区域列表出错:', error);
                });
        }
        
        // 加载用户列表
        function loadUsers() {
            const area = document.getElementById('area-filter').value;
            fetch(`/api/users?page=${currentPage}&search=${searchKeyword}&area=${area}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        renderUsers(data.users);
                        renderPagination(data.pagination);
                    } else {
                        alert('加载用户列表失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('获取用户列表出错:', error);
                    alert('获取用户列表出错: ' + error.message);
                });
        }
        
        // 渲染用户列表
        function renderUsers(users) {
            const tbody = document.getElementById('user-list-body');
            tbody.innerHTML = '';
            
            if (users.length === 0) {
                const tr = document.createElement('tr');
                tr.innerHTML = '<td colspan="9" class="text-center">没有找到用户</td>';
                tbody.appendChild(tr);
                return;
            }
            
            users.forEach(user => {
                const tr = document.createElement('tr');
                
                const statusClass = user.is_active ? 'status-active' : 'status-inactive';
                const statusText = user.is_active ? '启用' : '禁用';
                
                const roleText = user.role === 'manager' ? '管理员' : '营业员';
                
                tr.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.name}</td>
                    <td>${user.phone || '-'}</td>
                    <td>${roleText}</td>
                    <td>${user.area || '-'}</td>
                    <td><span class="user-status ${statusClass}">${statusText}</span></td>
                    <td>${formatDate(user.created_at)}</td>
                    <td>${user.last_login ? formatDate(user.last_login) : '-'}</td>
                    <td>
                        <button class="action-btn edit-btn" onclick="editUser(${user.id})">编辑</button>
                        <button class="action-btn delete-btn" onclick="showDeleteModal(${user.id})">删除</button>
                    </td>
                `;
                
                tbody.appendChild(tr);
            });
        }
        
        // 渲染分页控件
        function renderPagination(pagination) {
            const paginationDiv = document.getElementById('pagination');
            paginationDiv.innerHTML = '';
            
            if (pagination.pages <= 1) return;
            
            // 上一页按钮
            const prevButton = document.createElement('button');
            prevButton.textContent = '上一页';
            prevButton.disabled = pagination.page <= 1;
            prevButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    loadUsers();
                }
            });
            paginationDiv.appendChild(prevButton);
            
            // 页码按钮
            for (let i = 1; i <= pagination.pages; i++) {
                const pageButton = document.createElement('button');
                pageButton.textContent = i;
                pageButton.className = pagination.page === i ? 'active' : '';
                pageButton.addEventListener('click', () => {
                    currentPage = i;
                    loadUsers();
                });
                paginationDiv.appendChild(pageButton);
            }
            
            // 下一页按钮
            const nextButton = document.createElement('button');
            nextButton.textContent = '下一页';
            nextButton.disabled = pagination.page >= pagination.pages;
            nextButton.addEventListener('click', () => {
                if (currentPage < pagination.pages) {
                    currentPage++;
                    loadUsers();
                }
            });
            paginationDiv.appendChild(nextButton);
        }
        
        // 编辑用户
        function editUser(userId) {
            fetch(`/api/users/${userId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        const user = data.user;
                        document.getElementById('user-id').value = user.id;
                        document.getElementById('username').value = user.username;
                        document.getElementById('name').value = user.name;
                        document.getElementById('phone').value = user.phone || '';
                        document.getElementById('password').value = '';
                        document.getElementById('role').value = user.role;
                        document.getElementById('area').value = user.area || '';
                        document.getElementById('is-active').value = user.is_active.toString();
                        
                        document.getElementById('userModalLabel').textContent = '编辑用户';
                        $('#userModal').modal('show');
                    } else {
                        alert('获取用户信息失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('获取用户信息出错:', error);
                    alert('获取用户信息出错: ' + error.message);
                });
        }
        
        // 保存用户
        function saveUser() {
            const userId = document.getElementById('user-id').value;
            const isEdit = userId !== '';
            
            const userData = {
                username: document.getElementById('username').value,
                name: document.getElementById('name').value,
                phone: document.getElementById('phone').value,
                role: document.getElementById('role').value,
                area: document.getElementById('area').value,
                is_active: document.getElementById('is-active').value === 'true'
            };
            
            // 如果设置了密码，则添加到数据中
            const password = document.getElementById('password').value;
            if (password || !isEdit) {
                userData.password = password;
            } else if (!isEdit) {
                // 如果是新建用户且没有设置密码，提示错误
                alert('新建用户必须设置密码');
                return;
            }
            
            const url = isEdit ? `/api/users/${userId}` : '/api/users';
            const method = isEdit ? 'PUT' : 'POST';
            
            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || '操作失败');
                        }).catch(() => {
                            // 如果无法解析JSON，则抛出一般错误
                            throw new Error('服务器响应异常');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert(isEdit ? '用户更新成功' : '用户创建成功');
                        $('#userModal').modal('hide');
                        loadUsers();
                    } else {
                        alert(isEdit ? '更新用户失败: ' : '创建用户失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('保存用户出错:', error);
                    alert('保存用户出错: ' + error.message);
                });
        }
        
        // 显示删除确认模态框
        function showDeleteModal(userId) {
            document.getElementById('delete-user-id').value = userId;
            $('#deleteModal').modal('show');
        }
        
        // 删除用户
        function deleteUser() {
            const userId = document.getElementById('delete-user-id').value;
            
            fetch(`/api/users/${userId}`, {
                method: 'DELETE'
            })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || '删除失败');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert('用户删除成功');
                        $('#deleteModal').modal('hide');
                        loadUsers();
                    } else {
                        alert('删除用户失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('删除用户出错:', error);
                    alert('删除用户出错: ' + error.message);
                });
        }
        
        // 重置用户表单
        function resetUserForm() {
            document.getElementById('user-id').value = '';
            document.getElementById('username').value = '';
            document.getElementById('name').value = '';
            document.getElementById('phone').value = '';
            document.getElementById('password').value = '';
            document.getElementById('role').value = 'staff';
            document.getElementById('area').value = '';
            document.getElementById('is-active').value = 'true';
        }
        
        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            
            const date = new Date(dateString);
            return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
        }
        
        // 补零
        function padZero(num) {
            return num < 10 ? '0' + num : num;
        }
    </script>
</body>
</html>
