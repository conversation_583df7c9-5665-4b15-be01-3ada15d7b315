@media (max-width: 768px) {
    body {
        font-size: 15px !important;
        padding-bottom: 70px !important;
    }

    .container {
        max-width: 100% !important;
        padding: 15px !important;
    }

    .header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px !important;
    }

    .header .btn,
    .search-controls .btn {
        width: 100% !important;
        margin-top: 8px !important;
    }

    .search-controls {
        display: flex !important;
        flex-direction: column !important;
        gap: 10px !important;
    }

    .table-container {
        padding: 10px !important;
    }

    .data-table th,
    .data-table td {
        font-size: 12px !important;
        padding: 8px 10px !important;
    }

    /* Bottom nav */
    .mobile-bottom-nav {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 64px !important;
        background: #ffffff !important;
        border-top: 1px solid #ddd !important;
        display: flex !important;
        justify-content: space-around !important;
        align-items: center !important;
        z-index: 1000 !important;
    }

    .mobile-bottom-nav .nav-item {
        flex: 1 1 auto !important;
        text-align: center !important;
        color: #666 !important;
        font-size: 13px !important;
        text-decoration: none !important;
    }

    .mobile-bottom-nav .nav-item .nav-icon {
        font-size: 22px !important;
        display: block !important;
        line-height: 20px !important;
        margin-bottom: 2px !important;
    }

    .mobile-bottom-nav .nav-item.active {
        color: #007BFF !important;
    }

    /* ----------------------- 追加优化样式 ----------------------- */
    /* 使订单表格可横向滚动并减小整体宽度 */
    .order-table {
        display: block !important;
        overflow-x: auto !important;
    }

    /* 在移动端隐藏不那么重要的列（衣物名称、数量、支付时间、营业员） */
    .order-table th:nth-child(6),
    .order-table td:nth-child(6), /* 衣物名称 */
    .order-table th:nth-child(7),
    .order-table td:nth-child(7), /* 数量 */
    .order-table th:nth-child(11),
    .order-table td:nth-child(11), /* 支付时间 */
    .order-table th:nth-child(12),
    .order-table td:nth-child(12)  /* 营业员 */
    {
        display: none !important;
    }

    /* 单元格字体和内边距微调，按钮/复选框更易点击 */
    .order-table td {
        white-space: nowrap !important;
        font-size: 13px !important;
        padding: 6px 8px !important;
    }

    .toggle-details {
        width: 100% !important;
        padding: 6px 12px !important;
        font-size: 13px !important;
    }

    /* 使操作列在移动端始终可见 */
    .order-table th:last-child,
    .order-table td:last-child {
        position: sticky !important;
        right: 0 !important;
        background: #fff !important;
        z-index: 2 !important; /* 确保浮在其他单元格之上 */
    }

    /* 底部导航触摸友好度提升 */
    .mobile-bottom-nav {
        height: 64px !important;
    }

    .mobile-bottom-nav .nav-item .nav-icon {
        font-size: 22px !important;
    }

    .mobile-bottom-nav .nav-item {
        font-size: 13px !important;
    }
    /* ---------------------------------------------------------- */

    /* 订单详情弹窗 - 移动端优化 */
    #orderDetailModal .modal-content {
        width: 94vw !important;
        max-height: 90vh !important;
        margin: 5vh auto !important;
        padding: 16px 12px !important;
        border-radius: 8px !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }

    #orderDetailModal h3 {
        font-size: 18px !important;
        margin-bottom: 12px !important;
        text-align: center !important;
    }

    /* 基本信息网格改为两列 */
    #orderDetailModal .modal-info-grid {
        display: grid !important;
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        gap: 4px 10px !important;
    }

    #orderDetailModal .modal-info-item {
        font-size: 14px !important;
    }

    /* 状态表单栅格纵向排列 */
    #orderDetailModal .order-detail-status-controls {
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 8px !important;
    }

    /* 每个表单组宽度 48%，按钮占满整行 */
    #orderDetailModal .status-form-group {
        flex: 1 1 48% !important;
        min-width: 140px !important;
    }

    #orderDetailModal .update-status-btn {
        flex: 1 1 100% !important; /* 按钮单独占一行 */
        margin-top: 4px !important;
    }

    /* 衣物卡片纵向排列、增加间距 */
    #orderDetailModal .clothing-item-card {
        flex-direction: column !important;
    }

    #orderDetailModal .clothing-item-photos {
        overflow-x: auto !important;
        white-space: nowrap !important;
        padding: 10px 0 !important;
    }

    #orderDetailModal .clothing-photo-container {
        flex: 0 0 auto !important;
        margin-right: 8px !important;
    }

    /* ---------------------------------------------------------- */
    /* 筛选面板折叠样式 */
    .filter-summary {
        display: flex !important;
        align-items: center !important;
        gap: 6px !important;
        padding: 10px 14px !important;
        background: #f5f5f5 !important;
        border-radius: 6px !important;
        font-weight: 600 !important;
        cursor: pointer !important;
    }

    .filter-summary::before {
        content: '\1F50D'; /* 🔍 */
        font-size: 16px !important;
    }

    .filter-summary::after {
        content: '▼';
        margin-left: auto !important;
        transition: transform 0.3s !important;
    }

    .filter-panel[open] .filter-summary::after {
        transform: rotate(180deg) !important;
    }
    /* ---------------------------------------------------------- */

    /* 隐藏移动端的"订单查询结果"大标题 */
    #customerData h2 {
        display: none !important;
    }

    /* 批量操作条进一步压缩 */
    #batchOperations {
        border-left: 2px solid #007BFF !important;
    }

    #batchOperations h3 {
        margin-right: 6px !important;
        font-size: 12px !important;
    }

    /* ---------------------------------------------------------- */
    /* 隐藏移动端 header 内"返回主页"链接 */
    .header-controls a[href="/"] {
        display: none !important;
    }

    /* 隐藏管理员权限提示绿框，仅移动端 */
    #customerData > div[style*="background-color: #d4edda"] {
        display: none !important;
    }

    /* 批量操作区域两行布局 */
    #batchOperations > div {
        flex-wrap: wrap !important;
        row-gap: 6px !important;
    }

    #batchOperations .form-group {
        flex: 1 1 48% !important;
        min-width: 130px !important;
    }

    #batchOperations .form-group:nth-of-type(3) { /* 备注输入占满一行 */
        flex: 1 1 100% !important;
    }

    /* 批量操作按钮单独一行 */
    #batchOperations button#applyBatchUpdate {
        flex: 1 1 100% !important;
        font-size: 11px !important;
        padding: 6px 0 !important;
    }

    #batchOperations .form-group label,
    #batchOperations .status-select,
    #batchOperations input[type="text"] {
        font-size: 11px !important;
    }
    /* ---------------------------------------------------------- */

    /* 隐藏批量操作条（移动端） */
    #batchOperations {
        display: none !important;
    }
    /* ---------------------------------------------------------- */
} 