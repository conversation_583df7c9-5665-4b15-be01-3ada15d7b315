from flask import Blueprint, request, jsonify, session, render_template, redirect
from models import *
from blueprints.auth import login_required

product_bp = Blueprint('product', __name__)
bp = product_bp  # 为了与自动注册系统兼容

@product_bp.route('/api/product_types', methods=['GET'])
@login_required
def get_product_types():
    """获取系统中所有衣物/产品类型"""
    try:
        # 获取所有活跃商品
        products = Product.query.filter_by(is_active=True).all()

        # 准备返回数据（不包含已废弃的基础价格）
        product_list = []
        for product in products:
            product_list.append({
                'id': product.id,
                'name': product.name,
                'category': product.category,
                'fine_wash_price': product.fine_wash_price,
                'luxury_wash_price': product.luxury_wash_price,
                'mend_price': product.mend_price,
                'alter_price': product.alter_price,
                'other_price': product.other_price
            })

        return jsonify({
            'success': True,
            'products': product_list
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@product_bp.route('/api/service_prices', methods=['GET'])
@login_required
def get_service_prices():
    """获取默认服务价格配置"""
    try:
        # 从数据库获取第一个商品的服务价格作为默认值
        # 如果没有商品，使用硬编码的默认值
        first_product = Product.query.filter_by(is_active=True).first()

        if first_product:
            default_prices = {
                '精洗': first_product.fine_wash_price,
                '奢洗': first_product.luxury_wash_price,
                '织补': first_product.mend_price,
                '改衣': first_product.alter_price,
                '其他': first_product.other_price
            }
        else:
            # 如果数据库中没有商品，使用默认值
            default_prices = {
                '精洗': 25.0,
                '奢洗': 40.0,
                '织补': 20.0,
                '改衣': 30.0,
                '其他': 20.0
            }

        return jsonify({
            'success': True,
            'service_prices': default_prices
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@product_bp.route('/api/product_prices/<product_name>', methods=['GET'])
@login_required
def get_product_prices(product_name):
    """根据商品名称获取具体商品的服务价格"""
    try:
        # 查找指定名称的商品
        product = Product.query.filter_by(name=product_name, is_active=True).first()

        if product:
            product_prices = {
                '精洗': product.fine_wash_price,
                '奢洗': product.luxury_wash_price,
                '织补': product.mend_price,
                '改衣': product.alter_price,
                '其他': product.other_price
            }
            return jsonify({
                'success': True,
                'product_name': product_name,
                'service_prices': product_prices
            })
        else:
            # 如果找不到商品，返回默认价格
            default_prices = {
                '精洗': 25.0,
                '奢洗': 40.0,
                '织补': 20.0,
                '改衣': 30.0,
                '其他': 20.0
            }
            return jsonify({
                'success': True,
                'product_name': product_name,
                'service_prices': default_prices,
                'is_default': True
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 商品管理路由和API
@product_bp.route('/product_management')
@login_required
def product_management():
    """商品管理页面"""
    # 只允许管理员或经理访问
    if session.get('staff_role') not in ['admin', 'manager']:
        return redirect('/')

    return render_template('product_management.html')

@product_bp.route('/api/products', methods=['GET'])
@login_required
def get_products():
    """获取商品列表"""
    try:
        # 获取查询参数
        search = request.args.get('search', '')
        category = request.args.get('category', '')
        is_active = request.args.get('is_active', '')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))

        # 构建查询
        query = Product.query

        # 应用搜索过滤
        if search:
            query = query.filter(Product.name.like(f'%{search}%'))

        # 应用分类过滤
        if category:
            query = query.filter(Product.category == category)

        # 应用状态过滤
        if is_active == 'true':
            query = query.filter(Product.is_active == True)
        elif is_active == 'false':
            query = query.filter(Product.is_active == False)

        # 计算总记录数
        total_count = query.count()

        # 应用分页
        products = query.order_by(Product.id).offset((page - 1) * per_page).limit(per_page).all()

        # 构建结果（不包含已废弃的基础价格）
        products_list = []
        for product in products:
            products_list.append({
                'id': product.id,
                'name': product.name,
                'category': product.category,
                'fine_wash_price': product.fine_wash_price,
                'luxury_wash_price': product.luxury_wash_price,
                'mend_price': product.mend_price,
                'alter_price': product.alter_price,
                'other_price': product.other_price,
                'description': product.description,
                'is_active': product.is_active
            })

        return jsonify({
            'products': products_list,
            'total': total_count,
            'page': page,
            'per_page': per_page,
            'total_pages': (total_count + per_page - 1) // per_page
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@product_bp.route('/api/products/<int:product_id>', methods=['GET'])
@login_required
def get_product(product_id):
    """获取单个商品详情"""
    try:
        product = Product.query.get(product_id)

        if not product:
            return jsonify({'error': '商品不存在'}), 404

        # 手动构建商品数据字典（不包含已废弃的基础价格）
        product_data = {
            'id': product.id,
            'name': product.name,
            'category': product.category,
            'fine_wash_price': product.fine_wash_price,
            'luxury_wash_price': product.luxury_wash_price,
            'mend_price': product.mend_price,
            'alter_price': product.alter_price,
            'other_price': product.other_price,
            'description': product.description,
            'is_active': product.is_active
        }

        return jsonify(product_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@product_bp.route('/api/products', methods=['POST'])
@login_required
def add_product():
    """添加新商品"""
    # 权限检查
    if session.get('staff_role') not in ['admin', 'manager']:
        return jsonify({'error': '没有权限执行此操作'}), 403

    try:
        data = request.json
        
        # 验证必填字段
        if not data.get('name'):
            return jsonify({'error': '商品名称不能为空'}), 400
        
        # 检查商品名称是否已存在
        existing_product = Product.query.filter_by(name=data['name']).first()
        if existing_product:
            return jsonify({'error': '商品名称已存在'}), 400
        
        # 创建新商品（不包含已废弃的基础价格）
        new_product = Product(
            name=data['name'],
            category=data.get('category', ''),
            fine_wash_price=float(data.get('fine_wash_price', 25.0)),
            luxury_wash_price=float(data.get('luxury_wash_price', 40.0)),
            mend_price=float(data.get('mend_price', 20.0)),
            alter_price=float(data.get('alter_price', 30.0)),
            other_price=float(data.get('other_price', 20.0)),
            description=data.get('description', ''),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(new_product)
        db.session.commit()
        
        return jsonify({
            'message': '商品添加成功',
            'product_id': new_product.id
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@product_bp.route('/api/products/<int:product_id>', methods=['PUT'])
@login_required
def update_product(product_id):
    """更新商品信息"""
    # 权限检查
    if session.get('staff_role') not in ['admin', 'manager']:
        return jsonify({'error': '没有权限执行此操作'}), 403

    try:
        product = Product.query.get(product_id)
        if not product:
            return jsonify({'error': '商品不存在'}), 404

        data = request.json
        
        # 如果更新名称，检查是否与其他商品重复
        if 'name' in data and data['name'] != product.name:
            existing_product = Product.query.filter_by(name=data['name']).first()
            if existing_product:
                return jsonify({'error': '商品名称已存在'}), 400
        
        # 更新商品信息（不包含已废弃的基础价格）
        if 'name' in data:
            product.name = data['name']
        if 'category' in data:
            product.category = data['category']
        if 'fine_wash_price' in data:
            product.fine_wash_price = float(data['fine_wash_price'])
        if 'luxury_wash_price' in data:
            product.luxury_wash_price = float(data['luxury_wash_price'])
        if 'mend_price' in data:
            product.mend_price = float(data['mend_price'])
        if 'alter_price' in data:
            product.alter_price = float(data['alter_price'])
        if 'other_price' in data:
            product.other_price = float(data['other_price'])
        if 'description' in data:
            product.description = data['description']
        if 'is_active' in data:
            product.is_active = data['is_active']
        
        db.session.commit()
        
        return jsonify({'message': '商品信息更新成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@product_bp.route('/api/products/<int:product_id>', methods=['DELETE'])
@login_required
def delete_product(product_id):
    """删除商品"""
    # 权限检查
    if session.get('staff_role') not in ['admin', 'manager']:
        return jsonify({'error': '没有权限执行此操作'}), 403

    try:
        product = Product.query.get(product_id)
        if not product:
            return jsonify({'error': '商品不存在'}), 404

        # 软删除：将商品设置为不活跃状态
        product.is_active = False
        db.session.commit()
        
        return jsonify({'message': '商品已删除'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500 