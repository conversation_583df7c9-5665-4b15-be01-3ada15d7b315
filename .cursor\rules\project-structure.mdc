---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

本项目基于 Flask Web 框架构建，核心文件与目录如下所示（点击文件名以跳转至源文件）：

- 主入口：[app.py](mdc:app.py)：包含 `create_app` 工厂函数并注册绝大多数路由，是程序的启动点。
- 数据模型：[models.py](mdc:models.py)：定义 SQLAlchemy ORM 模型及 `init_db` 方法，负责数据库结构与初始化。
- 配置文件：[config.py](mdc:config.py)：定义不同环境下的配置类，并通过 `config` 字典对外暴露。
- 工具函数：[utils.py](mdc:utils.py)：封装图片处理、订单号生成、会员余额更新等通用辅助方法。
- 前端模板目录：`templates/` 存放所有 Jinja2 HTML 模板文件。
- 静态资源目录：`static/` 用于存放 CSS、JavaScript 与图片等静态文件。
- 数据库迁移脚本目录：`database_migrations/` 用于存放 Alembic 迁移脚本，管理数据库版本。

## 运行方式

1. 安装依赖：`pip install -r requirements.txt`
2. 启动应用：`python app.py` 或者在生产环境中通过 WSGI 服务启动。

> 若需新增规则，请遵循项目根目录下 `.cursor/rules` 文件夹的 `.mdc` 规范进行编写。

