@media (max-width: 768px) {
    body {
        padding-bottom: 70px !important;
        font-size: 14px !important;
    }

    .container {
        max-width: 100% !important;
        padding: 15px !important;
    }

    .header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
        border-radius: 0 !important;
    }

    .header-controls {
        flex-wrap: wrap !important;
        gap: 10px !important;
    }

    .date-range {
        flex-direction: row !important;
        flex-wrap: nowrap !important;
        align-items: center !important;
        overflow-x: auto !important;
    }

    .date-range > div {
        display: flex !important;
        align-items: center !important;
        gap: 4px !important;
    }

    .date-input {
        width: 120px !important;
    }

    #applyDateRange {
        flex-shrink: 0 !important;
    }

    .dashboard {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)) !important;
    }

    .table-container {
        padding: 10px !important;
    }

    /* Bottom nav reuse */
    .mobile-bottom-nav {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 56px !important;
        background: #ffffff !important;
        border-top: 1px solid #ddd !important;
        display: flex !important;
        justify-content: space-around !important;
        align-items: center !important;
        z-index: 1000 !important;
    }

    .mobile-bottom-nav .nav-item {
        flex: 1 1 auto !important;
        text-align: center !important;
        color: #666 !important;
        font-size: 12px !important;
        text-decoration: none !important;
    }

    .mobile-bottom-nav .nav-item .nav-icon {
        font-size: 20px !important;
        display: block !important;
        line-height: 20px !important;
        margin-bottom: 2px !important;
    }

    .mobile-bottom-nav .nav-item.active {
        color: #007BFF !important;
    }

    .mobile-hide {
        display: none !important;
    }

    .date-range-label {
        display: inline-block !important;
    }

    #printReport {
        display: none !important;
    }

    h1 {
        font-size: 20px !important;
    }

    .dashboard-item h3 {
        font-size: 14px !important;
    }

    .dashboard-item-content span,
    .dashboard-value,
    .dashboard-item-content {
        font-size: 18px !important;
    }

    .data-table th,
    .data-table td {
        font-size: 12px !important;
        padding: 8px 10px !important;
    }

    .btn {
        font-size: 14px !important;
        padding: 6px 12px !important;
    }
} 