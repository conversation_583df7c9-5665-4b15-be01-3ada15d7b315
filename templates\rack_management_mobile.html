<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>格架管理 - 洗衣店管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            padding-top: 56px;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background-color: #007bff !important;
        }
        
        .slot-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 1px;
            padding: 5px;
            background-color: white;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .slot-item {
            aspect-ratio: 1;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            cursor: pointer;
            border-radius: 2px;
        }
        
        .slot-free {
            background-color: #d4edda;
            color: #155724;
        }
        
        .slot-occupied {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .slot-selected {
            background-color: #fff3cd;
            border-color: #ffc107;
            box-shadow: 0 0 0 1px #ffc107;
        }
        
        .lane-section {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .lane-header {
            background-color: #495057;
            color: white;
            padding: 8px 12px;
            margin: -15px -15px 10px -15px;
            border-radius: 8px 8px 0 0;
            font-size: 14px;
            font-weight: bold;
        }
        
        .stats-row {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-item {
            text-align: center;
            padding: 5px;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
        
        .search-panel {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .btn-group-mobile {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .btn-group-mobile .btn {
            flex: 1;
            font-size: 12px;
            padding: 8px 4px;
        }
        
        .modal-body {
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .clothing-info {
            background-color: #e3f2fd;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-grid-3x3"></i> 格架管理
            </a>
            <div class="d-flex">
                <select id="storeSelect" class="form-select form-select-sm me-2" style="width: 120px; display: none; font-size: 12px;">
                    <option value="">所有门店</option>
                </select>
                <button class="btn btn-outline-light btn-sm" onclick="refreshSlots()">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid px-3">
        <!-- 统计信息 -->
        <div class="stats-row">
            <div class="row">
                <div class="col-3">
                    <div class="stat-item">
                        <span class="stat-number text-primary" id="totalSlots">-</span>
                        <small class="stat-label">总数</small>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stat-item">
                        <span class="stat-number text-success" id="freeSlots">-</span>
                        <small class="stat-label">空闲</small>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stat-item">
                        <span class="stat-number text-danger" id="occupiedSlots">-</span>
                        <small class="stat-label">占用</small>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stat-item">
                        <span class="stat-number text-info" id="usageRate">-</span>
                        <small class="stat-label">使用率</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="btn-group-mobile">
            <button class="btn btn-primary" onclick="showSearchModal()">
                <i class="bi bi-search"></i> 搜索
            </button>
            <button class="btn btn-success" onclick="showAssignModal()">
                <i class="bi bi-plus-circle"></i> 分配
            </button>
            <button class="btn btn-warning" onclick="showMoveModal()">
                <i class="bi bi-arrow-left-right"></i> 移动
            </button>
            <button class="btn btn-info" onclick="toggleView()">
                <i class="bi bi-eye"></i> <span id="viewToggleText">简化</span>
            </button>
        </div>

        <!-- A侧格架 -->
        <div class="lane-section">
            <div class="lane-header">
                <i class="bi bi-arrow-left"></i> A侧输送线
            </div>
            <div class="slot-grid" id="slotsA"></div>
        </div>

        <!-- B侧格架 -->
        <div class="lane-section">
            <div class="lane-header">
                <i class="bi bi-arrow-right"></i> B侧输送线
            </div>
            <div class="slot-grid" id="slotsB"></div>
        </div>
    </div>

    <!-- 搜索模态框 -->
    <div class="modal fade" id="searchModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">搜索格架</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">扫码/条码</label>
                        <input type="text" class="form-control" id="searchBarcode" placeholder="扫码或输入条码">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">订单号</label>
                        <input type="text" class="form-control" id="searchOrderNumber" placeholder="输入订单号">
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">格架号</label>
                            <input type="number" class="form-control" id="searchSlotNo" placeholder="1-700" min="1" max="700">
                        </div>
                        <div class="col-6">
                            <label class="form-label">侧别</label>
                            <select class="form-select" id="searchLane">
                                <option value="">全部</option>
                                <option value="A">A侧</option>
                                <option value="B">B侧</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="searchSlots()">搜索</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 分配格架模态框 -->
    <div class="modal fade" id="assignModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">分配格架</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">衣物条码</label>
                        <input type="text" class="form-control" id="assignBarcode" placeholder="扫码或输入条码">
                    </div>
                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label">格架号</label>
                            <input type="number" class="form-control" id="assignSlotNo" placeholder="1-700" min="1" max="700">
                        </div>
                        <div class="col-6">
                            <label class="form-label">侧别</label>
                            <select class="form-select" id="assignLane">
                                <option value="A">A侧</option>
                                <option value="B">B侧</option>
                            </select>
                        </div>
                    </div>
                    <div id="clothingInfo" style="display: none;">
                        <div class="clothing-info">
                            <strong>衣物信息：</strong><br>
                            <span id="clothingDetails"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="assignSlot()">确认分配</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 手动移动模态框 -->
    <div class="modal fade" id="moveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">手动移动</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        此操作将直接控制输送线移动
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">格架号</label>
                            <input type="number" class="form-control" id="moveSlotNo" placeholder="1-700" min="1" max="700">
                        </div>
                        <div class="col-6">
                            <label class="form-label">侧别</label>
                            <select class="form-select" id="moveLane">
                                <option value="A">A侧</option>
                                <option value="B">B侧</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="moveConveyor()">确认移动</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 格架详情模态框 -->
    <div class="modal fade" id="slotDetailModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="slotDetailTitle">格架详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="slotDetailBody">
                </div>
                <div class="modal-footer" id="slotDetailFooter">
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端操作提示 -->
    <div class="alert alert-light mt-3" style="text-align: center; font-size: 12px;">
        <strong>📱 操作提示：</strong><br>
        双击屏幕刷新 | 长按格架查看详情 | 支持扫码枪输入
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let slotsData = [];
        let selectedSlot = null;
        let showOnlyOccupied = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否是管理员
            checkManagerPermissions();
            
            refreshSlots();
            
            // 门店选择change事件
            document.getElementById('storeSelect').addEventListener('change', function() {
                const selectedStore = this.value;
                refreshSlots(selectedStore);
            });
            
            // 绑定条码输入框自动查询
            document.getElementById('assignBarcode').addEventListener('input', function() {
                const barcode = this.value.trim();
                if (barcode.length >= 6) {
                    checkClothingByBarcode(barcode);
                }
            });
            
            // 移动端条码输入优化
            const barcodeInputs = document.querySelectorAll('#assignBarcode, #searchBarcode');
            barcodeInputs.forEach(input => {
                // 移动端自动打开数字键盘
                input.setAttribute('inputmode', 'numeric');
                
                // 扫码枪输入时的优化处理
                let scanBuffer = '';
                let scanTimeout;
                
                input.addEventListener('input', function(e) {
                    // 清除之前的超时
                    clearTimeout(scanTimeout);
                    
                    // 如果输入很快（扫码枪特征），缓存输入
                    scanBuffer += e.data || '';
                    
                    // 设置短暂延时，判断是否为扫码输入完成
                    scanTimeout = setTimeout(() => {
                        if (scanBuffer && scanBuffer.length >= 6) {
                            this.value = scanBuffer;
                            scanBuffer = '';
                            
                            // 自动触发查询（如果是分配页面的条码框）
                            if (this.id === 'assignBarcode') {
                                checkClothingByBarcode(this.value.trim());
                            }
                        }
                        scanBuffer = '';
                    }, 100);
                });
                
                // Enter键处理
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const barcode = this.value.trim();
                        
                        if (this.id === 'assignBarcode' && barcode && document.getElementById('assignSlotNo').value) {
                            assignSlot();
                        } else if (this.id === 'searchBarcode' && barcode) {
                            searchSlots();
                        }
                    }
                });
            });
            
            // 添加双击刷新功能
            let lastTap = 0;
            document.addEventListener('touchend', function(e) {
                const currentTime = new Date().getTime();
                const tapLength = currentTime - lastTap;
                
                if (tapLength < 500 && tapLength > 0) {
                    // 双击刷新
                    refreshSlots();
                    
                    // 提供触觉反馈
                    if (navigator.vibrate) {
                        navigator.vibrate(50);
                    }
                    
                    // 显示刷新提示
                    const refreshHint = document.createElement('div');
                    refreshHint.textContent = '🔄 正在刷新...';
                    refreshHint.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: rgba(0,0,0,0.8);
                        color: white;
                        padding: 10px 20px;
                        border-radius: 20px;
                        z-index: 9999;
                        font-size: 14px;
                    `;
                    document.body.appendChild(refreshHint);
                    
                    setTimeout(() => {
                        refreshHint.remove();
                    }, 1000);
                    
                    e.preventDefault();
                }
                lastTap = currentTime;
            });
        });

        // 刷新格架状态
        function refreshSlots(storeName = '') {
            let url = '/api/slots';
            if (storeName) {
                url += `?store=${encodeURIComponent(storeName)}`;
            }
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        slotsData = data.slots;
                        updateSlotsDisplay();
                        updateStats(data.summary);
                    } else {
                        alert('获取格架状态失败: ' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
        }

        // 更新格架显示
        function updateSlotsDisplay() {
            const slotsA = document.getElementById('slotsA');
            const slotsB = document.getElementById('slotsB');
            
            slotsA.innerHTML = '';
            slotsB.innerHTML = '';
            
            slotsData.forEach(slot => {
                // 简化模式下只显示已占用的格架
                if (showOnlyOccupied && slot.status === 'free') {
                    return;
                }
                
                const slotElement = createSlotElement(slot);
                if (slot.lane === 'A') {
                    slotsA.appendChild(slotElement);
                } else {
                    slotsB.appendChild(slotElement);
                }
            });
        }

        // 创建格架元素
        function createSlotElement(slot) {
            const div = document.createElement('div');
            div.className = `slot-item slot-${slot.status}`;
            div.textContent = slot.slot_no;
            div.dataset.lane = slot.lane;
            div.dataset.slotNo = slot.slot_no;
            
            // 点击事件
            div.addEventListener('click', function() {
                showSlotDetail(slot);
            });
            
            return div;
        }

        // 显示格架详情
        function showSlotDetail(slot) {
            const modal = new bootstrap.Modal(document.getElementById('slotDetailModal'));
            const title = document.getElementById('slotDetailTitle');
            const body = document.getElementById('slotDetailBody');
            const footer = document.getElementById('slotDetailFooter');
            
            title.textContent = `格架 ${slot.lane}-${slot.slot_no}`;
            
            if (slot.status === 'occupied') {
                body.innerHTML = `
                    <div class="clothing-info">
                        <p><strong>条码：</strong>${slot.barcode}</p>
                        <p><strong>衣物：</strong>${slot.clothing_name} (${slot.clothing_color})</p>
                        <p><strong>订单：</strong>${slot.order_number}</p>
                        <p><strong>客户：</strong>${slot.customer_name}</p>
                        <p><strong>电话：</strong>${slot.customer_phone}</p>
                        <p><strong>入架时间：</strong>${slot.slot_time}</p>
                        <p><strong>操作员：</strong>${slot.operator}</p>
                    </div>
                `;
                
                footer.innerHTML = `
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-danger" onclick="releaseSlotById(${slot.clothing_id})">释放格架</button>
                `;
            } else {
                body.innerHTML = `
                    <div class="text-center">
                        <i class="bi bi-box text-success" style="font-size: 3rem;"></i>
                        <h5 class="mt-2 text-success">空闲格架</h5>
                        <p class="text-muted">此格架当前无衣物存放</p>
                    </div>
                `;
                
                footer.innerHTML = `
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="assignToSlot('${slot.lane}', ${slot.slot_no})">分配衣物</button>
                `;
            }
            
            modal.show();
        }

        // 更新统计信息
        function updateStats(summary) {
            document.getElementById('totalSlots').textContent = summary.total_slots;
            document.getElementById('freeSlots').textContent = summary.free_count;
            document.getElementById('occupiedSlots').textContent = summary.occupied_count;
            document.getElementById('usageRate').textContent = summary.usage_rate + '%';
        }

        // 切换显示模式
        function toggleView() {
            showOnlyOccupied = !showOnlyOccupied;
            const toggleText = document.getElementById('viewToggleText');
            toggleText.textContent = showOnlyOccupied ? '全部' : '简化';
            updateSlotsDisplay();
        }

        // 显示搜索模态框
        function showSearchModal() {
            new bootstrap.Modal(document.getElementById('searchModal')).show();
        }

        // 显示分配模态框
        function showAssignModal() {
            new bootstrap.Modal(document.getElementById('assignModal')).show();
        }

        // 显示移动模态框
        function showMoveModal() {
            new bootstrap.Modal(document.getElementById('moveModal')).show();
        }

        // 分配到指定格架
        function assignToSlot(lane, slotNo) {
            bootstrap.Modal.getInstance(document.getElementById('slotDetailModal')).hide();
            
            document.getElementById('assignLane').value = lane;
            document.getElementById('assignSlotNo').value = slotNo;
            
            setTimeout(() => {
                showAssignModal();
            }, 300);
        }

        // 根据条码检查衣物
        function checkClothingByBarcode(barcode) {
            fetch(`/api/slots/search?barcode=${encodeURIComponent(barcode)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.results.length > 0) {
                        const clothing = data.results[0];
                        document.getElementById('clothingDetails').innerHTML = `
                            衣物: ${clothing.clothing_name} (${clothing.clothing_color})<br>
                            订单: ${clothing.order_number}<br>
                            客户: ${clothing.customer_name}
                        `;
                        document.getElementById('clothingInfo').style.display = 'block';
                    } else {
                        document.getElementById('clothingInfo').style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('clothingInfo').style.display = 'none';
                });
        }

        // 分配格架
        function assignSlot() {
            const barcode = document.getElementById('assignBarcode').value.trim();
            const slotNo = document.getElementById('assignSlotNo').value;
            const lane = document.getElementById('assignLane').value;
            
            if (!barcode || !slotNo) {
                alert('请填写完整信息');
                return;
            }
            
            // 先查找衣物ID
            fetch(`/api/slots/search?barcode=${encodeURIComponent(barcode)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.results.length > 0) {
                        const clothingId = data.results[0].clothing_id;
                        
                        // 发送分配请求
                        return fetch('/api/slots/assign', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                clothing_id: clothingId,
                                slot_no: parseInt(slotNo),
                                lane: lane
                            })
                        });
                    } else {
                        throw new Error('找不到对应的衣物');
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('格架分配成功！');
                        bootstrap.Modal.getInstance(document.getElementById('assignModal')).hide();
                        refreshSlots();
                        
                        // 清空表单
                        document.getElementById('assignBarcode').value = '';
                        document.getElementById('assignSlotNo').value = '';
                        document.getElementById('clothingInfo').style.display = 'none';
                    } else {
                        alert('分配失败: ' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败: ' + error.message);
                });
        }

        // 释放格架（通过衣物ID）
        function releaseSlotById(clothingId) {
            if (!confirm('确认释放此格架吗？')) {
                return;
            }
            
            fetch('/api/slots/release', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    clothing_id: clothingId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('格架释放成功！');
                    bootstrap.Modal.getInstance(document.getElementById('slotDetailModal')).hide();
                    refreshSlots();
                } else {
                    alert('释放失败: ' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 手动移动输送线
        function moveConveyor() {
            const slotNo = document.getElementById('moveSlotNo').value;
            const lane = document.getElementById('moveLane').value;
            
            if (!slotNo) {
                alert('请输入目标格架号');
                return;
            }
            
            fetch('/api/slots/move', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    slot_no: parseInt(slotNo),
                    lane: lane
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('输送线移动成功！');
                    bootstrap.Modal.getInstance(document.getElementById('moveModal')).hide();
                    document.getElementById('moveSlotNo').value = '';
                } else {
                    alert('移动失败: ' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 搜索格架
        function searchSlots() {
            const params = new URLSearchParams();
            
            const barcode = document.getElementById('searchBarcode').value.trim();
            const orderNumber = document.getElementById('searchOrderNumber').value.trim();
            const slotNo = document.getElementById('searchSlotNo').value.trim();
            const lane = document.getElementById('searchLane').value.trim();
            
            if (barcode) params.append('barcode', barcode);
            if (orderNumber) params.append('order_number', orderNumber);
            if (slotNo) params.append('slot_no', slotNo);
            if (lane) params.append('lane', lane);
            
            if (params.toString() === '') {
                alert('请输入搜索条件');
                return;
            }
            
            fetch(`/api/slots/search?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.results.length > 0) {
                            alert(`找到 ${data.results.length} 个结果`);
                            bootstrap.Modal.getInstance(document.getElementById('searchModal')).hide();
                            
                            // 如果只有一个结果，直接显示详情
                            if (data.results.length === 1) {
                                const result = data.results[0];
                                const slot = slotsData.find(s => 
                                    s.lane === result.lane && s.slot_no === result.slot_no
                                );
                                if (slot) {
                                    showSlotDetail(slot);
                                }
                            }
                        } else {
                            alert('未找到匹配的结果');
                        }
                    } else {
                        alert('搜索失败: ' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
        }
        
        // 检查管理员权限
        function checkManagerPermissions() {
            fetch('/api/admin/stores')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 是管理员，显示门店选择功能
                        document.getElementById('storeSelect').style.display = 'block';
                        
                        // 填充门店选项
                        const select = document.getElementById('storeSelect');
                        select.innerHTML = '<option value="">所有门店</option>';
                        data.stores.forEach(store => {
                            select.innerHTML += `<option value="${store.store_name}">${store.store_name}</option>`;
                        });
                    }
                })
                .catch(error => {
                    // 不是管理员或网络错误，隐藏管理员功能
                    console.log('非管理员用户或网络错误');
                });
        }
    </script>
</body>
</html> 