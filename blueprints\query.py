from flask import Blueprint, request, jsonify, session, render_template, current_app
from functools import wraps
import datetime
import json
import base64
from io import BytesIO
from sqlalchemy.orm import joinedload, selectinload

from models import db, Order, Customer, Clothing, ClothingPhoto, OrderStatusLog, Staff, RechargeRecord
from utils import generate_barcode_base64
from blueprints.auth import login_required

bp = Blueprint('query', __name__)

@bp.route('/customer_history')
@login_required
def customer_history():
    """查询客户历史订单

    根据各种条件查询客户的历史订单，包括电话、姓名、日期、订单号、状态等

    参数:
        phone (str, 可选): 客户电话号码
        name (str, 可选): 客户姓名
        date (str, 可选): 订单日期，格式为YYYY-MM-DD
        order_number (str, 可选): 订单号
        status (str, 可选): 订单状态
        operator (str, 可选): 营业员姓名
        payment_method (str, 可选): 支付方式
        all (str, 可选): 是否查询所有订单，值为'true'或'false'
        page (int, 可选): 页码，默认为1
        per_page (int, 可选): 每页记录数，默认为5

    返回:
        JSON: 包含查询结果的JSON对象
    """
    try:
        # 记录开始时间
        start_time = datetime.datetime.now()
        print(f"开始查询客户历史订单: {start_time}")

        phone = request.args.get('phone', '')
        name = request.args.get('name', '')
        # 日期范围筛选参数（新）
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        # 支付时间筛选参数（新增）
        payment_start_date = request.args.get('payment_start_date', '')
        payment_end_date = request.args.get('payment_end_date', '')
        # 支付状态筛选参数（新增）
        payment_status = request.args.get('payment_status', '')
        # 兼容旧版的单日期参数，可选
        date = request.args.get('date', '')
        order_number = request.args.get('order_number', '')
        status = request.args.get('status', '')
        operator = request.args.get('operator', '')
        payment_method = request.args.get('payment_method', '')
        all_records = request.args.get('all', 'false') == 'true'
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 5))

        # 构建查询
        orders_query = Order.query

        # 获取当前登录用户的角色和ID
        staff_role = session.get('staff_role', '')
        staff_name = session.get('staff_name', '')
        staff_area = session.get('staff_area', '')

        print(
            f"用户角色: {staff_role}, 区域: {staff_area}, 筛选条件: 手机={phone}, 姓名={name}, "
            f"开始日期={start_date}, 结束日期={end_date}, 支付开始日期={payment_start_date}, "
            f"支付结束日期={payment_end_date}, 单日日期={date}, 订单号={order_number}, 状态={status}, "
            f"支付状态={payment_status}, 营业员={operator}, 支付方式={payment_method}"
        )

        # 如果用户不是管理员，只能查看自己操作的订单
        if staff_role != 'manager':
            orders_query = orders_query.filter_by(operator=staff_name)
        # 如果用户是区域管理员，只能查看自己区域的订单
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            # 查找该区域所有用户
            area_staff = Staff.query.filter_by(area=staff_area).all()
            area_staff_names = [user.name for user in area_staff]
            # 筛选该区域用户创建的订单
            orders_query = orders_query.filter(Order.operator.in_(area_staff_names))

        customer_info = None
        matched_customers = []  # 存储匹配的客户列表（用于后四位查询）
        has_filter = False

        # 应用筛选条件 - 支持多条件组合查询
        if order_number:
            orders_query = orders_query.filter_by(order_number=order_number)
            has_filter = True

        if status:
            orders_query = orders_query.filter_by(status=status)
            has_filter = True

        if operator:
            orders_query = orders_query.filter_by(operator=operator)
            has_filter = True

        if payment_method:
            orders_query = orders_query.filter_by(payment_method=payment_method)
            has_filter = True

        if payment_status:
            orders_query = orders_query.filter_by(payment_status=payment_status)
            has_filter = True

        if phone:
            # 根据手机号查询客户 - 支持完整手机号和后四位模糊查询
            phone = phone.strip()

            # 判断是否为后四位查询（4位数字）
            if len(phone) == 4 and phone.isdigit():
                # 后四位模糊查询
                customers = Customer.query.filter(Customer.phone.like(f'%{phone}')).all()
                print(f"使用后四位 {phone} 进行模糊查询，找到 {len(customers)} 个匹配客户")

                if not customers:
                    print(f"未找到手机号后四位为 {phone} 的客户")
                    end_time = datetime.datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                    return jsonify({'found': False})

                # 如果找到多个客户，查询所有相关订单
                customer_ids = [customer.id for customer in customers]
                orders_query = orders_query.filter(Order.customer_id.in_(customer_ids))

                # 存储所有匹配的客户信息
                matched_customers = customers
                # 使用第一个客户作为主要显示信息
                customer_info = customers[0]

            elif len(phone) >= 7:  # 完整手机号或较长的号码
                # 精确匹配查询
                customer = Customer.query.filter_by(phone=phone).first()
                if not customer:
                    print(f"未找到电话号码为 {phone} 的客户")
                    end_time = datetime.datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                    return jsonify({'found': False})
                orders_query = orders_query.filter_by(customer_id=customer.id)
                customer_info = customer
                matched_customers = [customer]  # 精确匹配只有一个客户

            else:
                # 输入长度不符合要求
                print(f"手机号输入格式错误: {phone}，请输入完整手机号或后四位数字")
                end_time = datetime.datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                print(f"查询完成，耗时: {execution_time}秒，结果: 格式错误")
                return jsonify({'found': False, 'error': '请输入完整手机号或后四位数字'})

            has_filter = True

        if name:
            # 根据客户名查询
            if phone:
                # 如果已经有手机号筛选，进一步筛选客户姓名
                if customer_info and name not in customer_info.name:
                    print(f"客户 {customer_info.name} 不匹配姓名条件 {name}")
                    end_time = datetime.datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                    return jsonify({'found': False})
            else:
                # 没有手机号筛选，直接根据姓名查询
                customer = Customer.query.filter(Customer.name.like(f'%{name}%')).first()
                if not customer:
                    print(f"未找到姓名包含 {name} 的客户")
                    end_time = datetime.datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                    return jsonify({'found': False})
                orders_query = orders_query.filter_by(customer_id=customer.id)
                customer_info = customer
            has_filter = True

        # 日期范围过滤：优先使用 start_date / end_date，其次兼容单一 date 参数
        if start_date or end_date or date:
            try:
                if start_date:
                    start_datetime = datetime.datetime.strptime(start_date, '%Y-%m-%d')
                elif date:  # 如果仅提供单日日期，视为开始日期
                    start_datetime = datetime.datetime.strptime(date, '%Y-%m-%d')
                else:
                    # 未提供开始日期，使用最小时间
                    start_datetime = datetime.datetime.min

                if end_date:
                    # 结束日期取当天 23:59:59
                    end_datetime = datetime.datetime.strptime(end_date, '%Y-%m-%d') + datetime.timedelta(days=1)
                elif date:
                    # 如果仅提供单日日期，则结束日期为次日 00:00:00
                    end_datetime = datetime.datetime.strptime(date, '%Y-%m-%d') + datetime.timedelta(days=1)
                else:
                    # 未提供结束日期，使用最大时间
                    end_datetime = datetime.datetime.max

                orders_query = orders_query.filter(
                    Order.created_at >= start_datetime,
                    Order.created_at < end_datetime
                )
                has_filter = True
            except Exception as e:
                print(f"日期格式错误: {str(e)}")
                end_time = datetime.datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                print(f"查询出错，耗时: {execution_time}秒，错误: {str(e)}")
                return jsonify({'error': f'日期格式错误: {str(e)}'}), 400

        # 支付时间范围过滤（新增）
        if payment_start_date or payment_end_date:
            try:
                if payment_start_date:
                    payment_start_datetime = datetime.datetime.strptime(payment_start_date, '%Y-%m-%d')
                else:
                    # 未提供支付开始日期，使用最小时间
                    payment_start_datetime = datetime.datetime.min

                if payment_end_date:
                    # 支付结束日期取当天 23:59:59
                    payment_end_datetime = datetime.datetime.strptime(payment_end_date, '%Y-%m-%d') + datetime.timedelta(days=1)
                else:
                    # 未提供支付结束日期，使用最大时间
                    payment_end_datetime = datetime.datetime.max

                orders_query = orders_query.filter(
                    Order.payment_time >= payment_start_datetime,
                    Order.payment_time < payment_end_datetime
                )
                has_filter = True
            except Exception as e:
                print(f"支付日期格式错误: {str(e)}")
                end_time = datetime.datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                print(f"查询出错，耗时: {execution_time}秒，错误: {str(e)}")
                return jsonify({'error': f'支付日期格式错误: {str(e)}'}), 400

        # 检查是否有筛选条件或者是查询所有记录
        if not has_filter and not all_records:
            # 如果没有任何筛选条件，请求也不是获取全部，则返回错误
            print("未提供查询条件")
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            print(f"查询完成，耗时: {execution_time}秒，结果: 参数错误")
            return jsonify({'error': '请提供查询条件或设置all=true参数'}), 400

        # 按创建时间倒序排序
        orders_query = orders_query.order_by(Order.created_at.desc())

        # 计算总数
        total_orders = orders_query.count()
        print(f"符合条件的订单总数: {total_orders}")

        # 如果没有订单，则提前返回未找到
        if total_orders == 0:
            print("未找到符合条件的订单")
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
            return jsonify({'found': False})

        # 如果没有客户信息，使用第一个订单的客户作为显示信息
        if not customer_info:
            first_order = orders_query.first()
            if first_order:
                customer_info = Customer.query.get(first_order.customer_id)

        # 应用分页
        orders = orders_query.offset((page - 1) * per_page).limit(per_page).all()

        # 批量获取所有订单ID，用于后续查询
        order_ids = [order.id for order in orders]
        customer_ids = [order.customer_id for order in orders]

        # 批量查询所有客户信息
        customers_dict = {}
        customers = Customer.query.filter(Customer.id.in_(customer_ids)).all()
        for customer in customers:
            customers_dict[customer.id] = customer

        # 批量查询所有订单的衣物信息
        all_clothes = {}
        clothes_items = Clothing.query.filter(Clothing.order_id.in_(order_ids)).all()
        for item in clothes_items:
            if item.order_id not in all_clothes:
                all_clothes[item.order_id] = []
            all_clothes[item.order_id].append(item)

        # 获取所有衣物ID
        clothing_ids = [item.id for item in clothes_items]

        # 批量查询所有衣物的照片
        all_photos = {}
        if clothing_ids:
            photos = ClothingPhoto.query.filter(ClothingPhoto.clothing_id.in_(clothing_ids)).all()
            for photo in photos:
                if photo.clothing_id not in all_photos:
                    all_photos[photo.clothing_id] = []
                all_photos[photo.clothing_id].append(photo)

        # 构建订单列表
        order_list = []
        for order in orders:
            customer = customers_dict.get(order.customer_id)
            clothes_list = []

            # 计算该订单的退款总金额
            refund_amount = sum(r.refund_amount for r in order.refund_records)
            # 计算实际金额（原金额 - 退款金额）
            actual_amount = order.total_amount - refund_amount

            # 获取该订单的衣物
            clothes = all_clothes.get(order.id, [])

            for item in clothes:
                # 获取衣物照片
                photos = all_photos.get(item.id, [])
                photo_paths = [f"/static/{photo.image_path}" for photo in photos]

                # 解析服务特殊要求
                requirements = {}
                if item.special_requirements:
                    try:
                        requirements = json.loads(item.special_requirements)
                    except:
                        requirements = {}

                # 解析服务类型
                services = []
                if item.services:
                    try:
                        services = json.loads(item.services)
                    except:
                        services = []

                clothes_list.append({
                    'id': item.id,
                    'name': item.name,
                    'color': item.color,
                    'quantity': item.quantity or 1,  # 添加数量字段
                    'services': services,
                    'requirements': requirements,
                    'price': item.price,
                    'remarks': item.remarks,
                    'flaw': item.flaw or '',  # 添加瑕疵字段
                    'photos': photo_paths,
                    'date': item.created_at.strftime('%Y-%m-%d %H:%M')
                })

            order_list.append({
                'id': order.id,
                'order_number': order.order_number,
                'date': order.created_at.strftime('%Y-%m-%d %H:%M'),
                'total_amount': order.total_amount,  # 原始订单金额
                'refund_amount': refund_amount,  # 退款金额
                'actual_amount': actual_amount,  # 实际金额（原金额-退款金额）
                'discount_amount': order.discount_amount,
                'payment_method': order.payment_method,
                'payment_status': order.payment_status,
                'payment_time': order.payment_time.strftime('%Y-%m-%d %H:%M') if order.payment_time else '',
                'address': order.address,
                'status': order.status,
                'operator': order.operator or '未知',
                'clothes': clothes_list,
                'customer_name': customer.name if customer else '未知',
                'customer_phone': customer.phone if customer else '未知',
                # 添加订单修改标记信息
                'is_modified': order.is_modified or False,
                'last_modified_at': order.last_modified_at.strftime('%Y-%m-%d %H:%M') if order.last_modified_at else '',
                'last_modified_by': order.last_modified_by or '',
                'modification_count': order.modification_count or 0
            })

        # 计算全量汇总统计（基于所有符合条件的订单，不受分页影响）
        summary_stats = {
            'total_orders': total_orders,
            'total_amount': 0.0,
            'total_items': 0,
            'wash_count': 0,      # 精洗/奢洗件数
            'alter_count': 0,     # 改衣件数
            'darn_count': 0       # 织补件数
        }

        # 重新构建查询来获取所有符合条件的订单（不分页）用于汇总统计
        # 直接复用原始查询条件，确保筛选条件完全一致
        summary_query = Order.query.options(
            joinedload(Order.customer),
            selectinload(Order.clothes)
        )

        # 应用权限过滤（与主查询保持一致）
        if staff_role != 'manager':
            summary_query = summary_query.filter_by(operator=staff_name)
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            area_staff = Staff.query.filter_by(area=staff_area).all()
            area_staff_names = [user.name for user in area_staff]
            summary_query = summary_query.filter(Order.operator.in_(area_staff_names))

        # 应用所有筛选条件（与主查询保持完全一致）
        if order_number:
            summary_query = summary_query.filter_by(order_number=order_number)

        if status:
            summary_query = summary_query.filter_by(status=status)

        if operator:
            summary_query = summary_query.filter_by(operator=operator)

        if payment_method:
            summary_query = summary_query.filter_by(payment_method=payment_method)

        if payment_status:
            summary_query = summary_query.filter_by(payment_status=payment_status)

        if phone:
            customer = Customer.query.filter_by(phone=phone).first()
            if customer:
                summary_query = summary_query.filter_by(customer_id=customer.id)
            else:
                # 如果没有找到客户，返回空查询
                summary_query = summary_query.filter_by(id=-1)

        if name:
            if phone:
                # 如果已经有手机号筛选，进一步筛选客户姓名
                if customer_info and name not in customer_info.name:
                    summary_query = summary_query.filter_by(id=-1)
            else:
                # 没有手机号筛选，直接根据姓名查询
                customer = Customer.query.filter(Customer.name.like(f'%{name}%')).first()
                if customer:
                    summary_query = summary_query.filter_by(customer_id=customer.id)
                else:
                    summary_query = summary_query.filter_by(id=-1)

        # 日期范围过滤
        if start_date or end_date or date:
            try:
                if start_date:
                    start_datetime = datetime.datetime.strptime(start_date, '%Y-%m-%d')
                elif date:
                    start_datetime = datetime.datetime.strptime(date, '%Y-%m-%d')
                else:
                    start_datetime = datetime.datetime.min

                if end_date:
                    end_datetime = datetime.datetime.strptime(end_date, '%Y-%m-%d') + datetime.timedelta(days=1)
                elif date:
                    end_datetime = datetime.datetime.strptime(date, '%Y-%m-%d') + datetime.timedelta(days=1)
                else:
                    end_datetime = datetime.datetime.max

                summary_query = summary_query.filter(
                    Order.created_at >= start_datetime,
                    Order.created_at < end_datetime
                )
            except:
                pass

        # 支付时间范围过滤
        if payment_start_date or payment_end_date:
            try:
                if payment_start_date:
                    payment_start_datetime = datetime.datetime.strptime(payment_start_date, '%Y-%m-%d')
                else:
                    payment_start_datetime = datetime.datetime.min

                if payment_end_date:
                    payment_end_datetime = datetime.datetime.strptime(payment_end_date, '%Y-%m-%d') + datetime.timedelta(days=1)
                else:
                    payment_end_datetime = datetime.datetime.max

                summary_query = summary_query.filter(
                    Order.payment_time >= payment_start_datetime,
                    Order.payment_time < payment_end_datetime
                )
            except:
                pass

        # 获取所有符合条件的订单
        all_orders = summary_query.all()
        print(f"汇总统计 - 查询到的订单数量: {len(all_orders)}, 应该等于total_orders: {total_orders}")
        
        for order in all_orders:
            summary_stats['total_amount'] += order.total_amount

            # 统计衣物和服务类型
            for item in order.clothes:
                item_quantity = item.quantity or 1
                summary_stats['total_items'] += item_quantity

                # 解析服务类型
                try:
                    services = json.loads(item.services) if item.services else []
                    print(f"订单 {order.order_number} 衣物 {item.name} 服务: {services}, 数量: {item_quantity}")
                    for service in services:
                        if service in ['精洗', '奢洗', '洗衣']:
                            summary_stats['wash_count'] += item_quantity
                        elif service == '改衣':
                            summary_stats['alter_count'] += item_quantity
                        elif service == '织补':
                            summary_stats['darn_count'] += item_quantity
                except Exception as e:
                    print(f"解析服务类型出错: {item.services}, 错误: {e}")
                    pass

        # 计算执行时间
        end_time = datetime.datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        print(f"查询完成，耗时: {execution_time}秒，返回 {len(order_list)} 条订单记录，汇总统计: {summary_stats}")

        response_data = {
            'found': True,
            'customer': {
                'name': customer_info.name,
                'phone': customer_info.phone,
                'balance': customer_info.balance
            },
            'orders': order_list,
            'pagination': {
                'total': total_orders,
                'page': page,
                'per_page': per_page,
                'pages': (total_orders + per_page - 1) // per_page
            },
            'summary': summary_stats,  # 添加汇总统计数据
            'execution_time': execution_time,  # 添加执行时间到响应中
            'matched_customers': [  # 添加匹配的客户列表（用于后四位查询）
                {
                    'id': customer.id,
                    'name': customer.name,
                    'phone': customer.phone,
                    'balance': customer.balance
                } for customer in matched_customers
            ] if matched_customers else []
        }

        return jsonify(response_data)
    except Exception as e:
        # 记录异常发生时的时间，计算总执行时间
        end_time = datetime.datetime.now()
        execution_time = (end_time - start_time if 'start_time' in locals() else datetime.datetime.now()).total_seconds()

        print(f"客户历史查询出错: {str(e)}, 耗时: {execution_time}秒")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'查询失败: {str(e)}'}), 500

@bp.route('/order_details')
@login_required
def order_details():
    """获取指定订单的详细信息，用于打印功能

    根据订单ID获取订单详细信息，包括客户信息、衣物信息和状态变更日志

    参数:
        id (str): 订单ID

    返回:
        JSON: 包含订单详细信息的JSON对象
    """
    try:
        order_id = request.args.get('id')
        if not order_id:
            return jsonify({'error': '未提供订单ID'}), 400

        # 获取订单信息
        order = Order.query.get(order_id)
        if not order:
            return jsonify({'error': '订单不存在'}), 404

        # 获取客户信息
        customer = Customer.query.get(order.customer_id)
        if not customer:
            return jsonify({'error': '客户信息不存在'}), 404

        # 获取衣物信息
        clothes = Clothing.query.filter_by(order_id=order.id).all()
        clothes_list = []

        for item in clothes:
            # 获取衣物照片
            photos = ClothingPhoto.query.filter_by(clothing_id=item.id).all()
            photo_paths = [f"/static/{photo.image_path}" for photo in photos]

            # 解析服务特殊要求
            requirements = {}
            if item.special_requirements:
                try:
                    requirements = json.loads(item.special_requirements)
                except:
                    requirements = {}

            # 解析服务类型
            services = []
            if item.services:
                try:
                    services = json.loads(item.services)
                except:
                    services = []

            clothes_list.append({
                'id': item.id,
                'name': item.name,
                'color': item.color,
                'quantity': item.quantity or 1,  # 添加数量字段
                'services': services,
                'requirements': requirements,
                'price': item.price,
                'remarks': item.remarks,
                'flaw': item.flaw or '',  # 添加瑕疵字段
                'photos': photo_paths,
                # 新增：格架信息
                'slot_no': item.slot_no,
                'lane': item.lane,
                'slot_time': item.slot_time.isoformat() if item.slot_time else '',
                'barcode': item.barcode or '',
                'date': item.created_at.strftime('%Y-%m-%d %H:%M')
            })

        # 获取状态变更日志
        status_logs = OrderStatusLog.query.filter_by(order_id=order.id).order_by(OrderStatusLog.created_at.desc()).all()
        status_logs_list = []
        for log in status_logs:
            status_logs_list.append({
                'id': log.id,
                'old_status': log.old_status,
                'new_status': log.new_status,
                'changed_by': log.changed_by,
                'remarks': log.remarks,
                'created_at': log.created_at.strftime('%Y-%m-%d %H:%M')
            })

        # 计算客户余额信息（用于小票打印）
        customer_balance_info = None
        if customer:
            # 计算总余额（充值余额 + 赠送余额）
            total_balance = (customer.balance or 0) + (customer.gift_balance or 0)

            # 计算订单前后余额
            if order.payment_method == '余额' and order.payment_status == '已付款':
                # 如果是余额支付，订单前余额 = 当前余额 + 订单金额
                balance_before_order = total_balance + order.total_amount
                balance_after_order = total_balance
                balance_used = order.total_amount
            else:
                # 如果不是余额支付，余额没有变化
                balance_before_order = total_balance
                balance_after_order = total_balance
                balance_used = 0

            customer_balance_info = {
                'has_balance_account': True,
                'total_balance': total_balance,
                'balance': customer.balance or 0,
                'gift_balance': customer.gift_balance or 0,
                'balance_before_order': balance_before_order,
                'balance_after_order': balance_after_order,
                'balance_used': balance_used,
                'is_balance_payment': order.payment_method == '余额'
            }

        # 构建响应数据
        order_data = {
            'id': order.id,
            'order_number': order.order_number,
            'date': order.created_at.strftime('%Y-%m-%d %H:%M'),
            'total_amount': order.total_amount,  # 实际应付金额（折扣后）
            'discount_amount': order.discount_amount or 0,  # 折扣金额
            'original_amount': (order.total_amount or 0) + (order.discount_amount or 0),  # 原始金额（折扣前）
            'actual_amount': order.total_amount,  # 实付金额等于total_amount（折扣后的金额）
            'payment_method': order.payment_method,
            'payment_status': order.payment_status,
            'payment_time': order.payment_time.strftime('%Y-%m-%d %H:%M') if order.payment_time else '',
            'address': order.address,
            'status': order.status,
            'operator': order.operator or '未知',
            'customer_name': customer.name,
            'customer_phone': customer.phone,
            'customer_balance_info': customer_balance_info,  # 添加客户余额信息
            'clothes': clothes_list,
            'status_logs': status_logs_list,
            'remarks': '',  # 添加备注字段，初始为空
            # 添加订单修改标记信息
            'is_modified': order.is_modified or False,
            'last_modified_at': order.last_modified_at.strftime('%Y-%m-%d %H:%M') if order.last_modified_at else '',
            'last_modified_by': order.last_modified_by or '',
            'modification_count': order.modification_count or 0
        }

        # 收集衣物备注作为订单备注
        clothing_remarks = []
        for item in clothes_list:
            if item.get('remarks') and item['remarks'].strip():
                clothing_remarks.append(f"{item['name']}: {item['remarks']}")

        # 如果有衣物备注，则合并为订单备注
        if clothing_remarks:
            order_data['remarks'] = '; '.join(clothing_remarks)

        return jsonify(order_data)
    except Exception as e:
        print(f"获取订单详情出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'获取订单详情失败: {str(e)}'}), 500

@bp.route('/history')
@login_required
def history():
    """订单历史页面

    返回订单历史查询页面

    返回:
        渲染后的历史页面模板
    """
    template_name = 'history_mobile.html' if request.args.get('m') == '1' else 'history.html'
    return render_template(template_name)

@bp.route('/barcode/<order_number>/<int:index>')
def generate_barcode(order_number, index):
    """生成订单条码/二维码

    根据订单号和衣物索引生成条形码图像

    参数:
        order_number (str): 订单号
        index (int): 衣物索引

    返回:
        图像: PNG格式的条形码图像
    """
    try:
        # 使用已有的工具函数生成条形码
        barcode_base64 = generate_barcode_base64(
            order_number=order_number,
            item_index=index,
            # 以下参数可选，可以根据需要传递
            operator_name="",
            phone="",
            remarks="",
            defects=""
        )

        if not barcode_base64:
            current_app.logger.error("生成条形码失败")
            return "条形码生成错误", 500

        # 从base64字符串中提取图像数据
        if barcode_base64.startswith('data:image'):
            # 分离metadata和实际数据
            barcode_base64 = barcode_base64.split(',')[1]

        # 解码base64数据
        img_data = base64.b64decode(barcode_base64)

        # 创建字节流
        img_io = BytesIO(img_data)
        img_io.seek(0)

        # 返回图像
        return current_app.response_class(img_io, mimetype='image/png')
    except Exception as e:
        current_app.logger.error(f"生成条形码错误: {str(e)}")
        return "条形码生成错误", 500

@bp.route('/api/export_orders', methods=['POST'])
@login_required
def export_orders():
    """导出订单到Excel
    
    根据筛选条件导出订单数据到Excel文件
    
    请求体:
        JSON对象，包含筛选条件
    
    返回:
        Excel文件的二进制流
    """
    try:
        # 获取筛选条件
        filters = request.json if request.json else {}
        
        print(f"导出订单请求 - 筛选条件: {filters}")
        
        # 获取当前登录用户的角色和信息
        staff_role = session.get('staff_role', '')
        staff_name = session.get('staff_name', '')
        staff_area = session.get('staff_area', '')
        
        # 构建查询
        orders_query = Order.query
        
        # 权限控制
        if staff_role != 'manager':
            orders_query = orders_query.filter_by(operator=staff_name)
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            area_staff = Staff.query.filter_by(area=staff_area).all()
            area_staff_names = [user.name for user in area_staff]
            orders_query = orders_query.filter(Order.operator.in_(area_staff_names))
        
        # 应用筛选条件
        if filters.get('phone'):
            customer = Customer.query.filter_by(phone=filters['phone']).first()
            if customer:
                orders_query = orders_query.filter_by(customer_id=customer.id)
            else:
                # 如果没有找到客户，返回空结果
                orders_query = orders_query.filter_by(id=-1)
        
        if filters.get('name'):
            if not filters.get('phone'):
                customer = Customer.query.filter(Customer.name.like(f"%{filters['name']}%")).first()
                if customer:
                    orders_query = orders_query.filter_by(customer_id=customer.id)
                else:
                    orders_query = orders_query.filter_by(id=-1)
        
        if filters.get('order_number'):
            orders_query = orders_query.filter_by(order_number=filters['order_number'])
        
        if filters.get('status'):
            orders_query = orders_query.filter_by(status=filters['status'])
        
        if filters.get('payment_status'):
            orders_query = orders_query.filter_by(payment_status=filters['payment_status'])
        
        if filters.get('operator'):
            orders_query = orders_query.filter_by(operator=filters['operator'])
        
        if filters.get('payment_method'):
            orders_query = orders_query.filter_by(payment_method=filters['payment_method'])
        
        # 日期筛选
        if filters.get('start_date'):
            start_datetime = datetime.datetime.strptime(filters['start_date'], '%Y-%m-%d')
            orders_query = orders_query.filter(Order.created_at >= start_datetime)
        
        if filters.get('end_date'):
            end_datetime = datetime.datetime.strptime(filters['end_date'], '%Y-%m-%d') + datetime.timedelta(days=1)
            orders_query = orders_query.filter(Order.created_at < end_datetime)
        
        # 支付时间筛选
        if filters.get('payment_start_date'):
            payment_start_datetime = datetime.datetime.strptime(filters['payment_start_date'], '%Y-%m-%d')
            orders_query = orders_query.filter(Order.payment_time >= payment_start_datetime)
        
        if filters.get('payment_end_date'):
            payment_end_datetime = datetime.datetime.strptime(filters['payment_end_date'], '%Y-%m-%d') + datetime.timedelta(days=1)
            orders_query = orders_query.filter(Order.payment_time < payment_end_datetime)
        
        # 获取查询结果
        orders = orders_query.order_by(Order.created_at.desc()).limit(1000).all()  # 限制导出数量
        
        print(f"导出订单数量: {len(orders)}")
        
        if not orders:
            return jsonify({'error': '没有找到符合条件的订单'}), 404
        
        # 导入Excel库
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment
        except ImportError:
            return jsonify({'error': '服务器缺少openpyxl库，无法导出Excel'}), 500
        
        # 创建工作簿
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "订单导出"
        
        # 设置列标题
        headers = [
            '订单号', '客户姓名', '客户电话', '下单时间', '支付时间', 
            '订单状态', '支付状态', '支付方式', '订单金额', '折扣金额', 
            '实付金额', '衣物数量', '营业员', '客户地址', '衣物详情'
        ]
        
        # 写入标题行
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # 获取客户信息
        customer_ids = [order.customer_id for order in orders]
        customers_dict = {}
        customers = Customer.query.filter(Customer.id.in_(customer_ids)).all()
        for customer in customers:
            customers_dict[customer.id] = customer
        
        # 获取衣物信息
        order_ids = [order.id for order in orders]
        all_clothes = {}
        clothes_items = Clothing.query.filter(Clothing.order_id.in_(order_ids)).all()
        for item in clothes_items:
            if item.order_id not in all_clothes:
                all_clothes[item.order_id] = []
            all_clothes[item.order_id].append(item)
        
        # 写入数据行
        for row, order in enumerate(orders, 2):
            customer = customers_dict.get(order.customer_id)
            clothes = all_clothes.get(order.id, [])
            
            # 构建衣物详情和计算数量
            clothes_details = []
            total_quantity = 0
            for item in clothes:
                services = []
                if item.services:
                    try:
                        services = json.loads(item.services)
                    except:
                        services = []
                
                item_quantity = item.quantity or 1
                total_quantity += item_quantity
                
                detail = f"{item.name}({item.color or '无色'})"
                if item_quantity > 1:
                    detail += f" x{item_quantity}"
                if services:
                    detail += f" - {', '.join(services)}"
                if item.price:
                    detail += f" - ¥{item.price}"
                if item.remarks:
                    detail += f" - 备注:{item.remarks}"
                clothes_details.append(detail)
            
            # 写入数据
            data = [
                order.order_number,
                customer.name if customer else '未知',
                customer.phone if customer else '未知',
                order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                order.payment_time.strftime('%Y-%m-%d %H:%M:%S') if order.payment_time else '',
                order.status,
                order.payment_status,
                order.payment_method,
                order.total_amount,
                order.discount_amount or 0,
                order.total_amount - (order.discount_amount or 0),
                total_quantity,
                order.operator or '未知',
                order.address or '',
                '; '.join(clothes_details)
            ]
            
            for col, value in enumerate(data, 1):
                worksheet.cell(row=row, column=col, value=value)
        
        # 调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # 保存到内存
        output = BytesIO()
        workbook.save(output)
        output.seek(0)
        
        # 返回Excel文件
        from flask import make_response
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = 'attachment; filename=orders_export.xlsx'
        
        return response
        
    except Exception as e:
        print(f"导出订单失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'导出失败: {str(e)}'}), 500