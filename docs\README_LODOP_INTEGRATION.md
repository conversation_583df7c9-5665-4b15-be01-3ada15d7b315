# Lodop打印集成使用指南

## 概述

本系统已成功集成Lodop专业打印控件，支持水洗唛、不干胶标签、小票的专业打印。Lodop是一款专业的Web打印解决方案，提供比浏览器打印更精确、更稳定的打印效果。

## 系统特性

### 🎯 双模式打印
- **Lodop专业打印**：精确布局、高质量输出、专业打印效果
- **浏览器打印**：备用方案，保持向后兼容

### 📋 支持的打印类型

#### 1. 水洗唛标签 (101mm × 16mm)
- **左侧条形码区域** (0-63mm)
  - 使用CODE128条码格式
  - 条码内容：订单号-衣物序号 (如: 240315001-01)
  - 条码尺寸：61mm×14mm
  - 不显示条码文字

- **右侧信息区域** (63-101mm)
  - 第一行：衣物名称、价格、件数统计
  - 第二行：客户信息（脱敏电话号码、客户姓名）
  - 第三行：营业员、下单日期
  - 第四行：服务标签、备注信息

#### 2. 不干胶标签 (72mm × 50mm)
- **标头**：店铺名称（Soulweave改衣坊）
- **基本信息**：订单号、日期、客户电话、总件数
- **服务标记**：配饰[配]、加急[急]标识
- **衣物列表**：显示所有衣物名称
- **底部条形码**：订单号条码，支持快速识别

#### 3. 收银小票 (80mm 热敏纸)
- **小票头部**：店铺名称、小票标题
- **订单信息**：订单号、客户、电话、日期、收银员
- **商品明细表格**：品名、数量、服务、单价
- **金额统计**：原价、折扣、实付金额、支付方式
- **余额信息**：显示会员卡余额变化（如适用）
- **小票底部**：感谢语

## 安装配置

### 1. Lodop控件安装
```bash
# Windows 32位系统
下载并安装：install_lodop32.exe

# Windows 64位系统  
下载并安装：install_lodop64.exe

# 或使用CLodop云打印服务
下载并安装：CLodop_Setup_for_Win32NT.exe
```

### 2. 文件结构
```
static/js/
├── LodopFuncs.js          # Lodop基础函数库
├── lodop-print.js         # 水洗唛、不干胶、小票打印实现
└── print-functions.js     # 增强的打印统一接口

templates/
├── index.html             # 主页面（已集成）
└── sticky_label_print.html # 不干胶标签页面（已集成）
```

### 3. 脚本引用
```html
<!-- Lodop打印控件脚本 -->
<script language="javascript" src="https://www.lodop.net/download/CLodop_Setup_For_Win32NT.exe" id="lodop32"></script>
<script language="javascript" src="https://www.lodop.net/download/CLodop_Setup_For_Win32NT.exe" id="lodop64"></script>
<script language="javascript" src="/static/js/LodopFuncs.js"></script>
<script src="/static/js/lodop-print.js"></script>
<script src="/static/js/print-functions.js"></script>
```

## 使用方法

### 1. 创建订单后打印
1. 在订单确认页面，点击"打印小票"或"打印水洗唛"
2. 系统自动弹出打印方式选择对话框
3. 选择"Lodop专业打印"或"浏览器打印"
4. Lodop打印将显示专业打印预览界面

### 2. 不干胶标签打印
1. 访问不干胶标签打印页面
2. 输入订单号或扫描水洗唛条码
3. 点击"打印"按钮
4. 选择打印方式进行输出

### 3. 批量打印
- **水洗唛**：支持打印全部衣物或指定单个衣物
- **小票**：一个订单对应一张小票
- **不干胶**：一个订单对应一张标签

## 打印参数配置

### 水洗唛打印参数
```javascript
// 纸张设置
LODOP.SET_PRINT_PAGESIZE(1, "101mm", "16mm", "");

// 条形码参数
LODOP.ADD_PRINT_BARCODE("1mm", "1mm", "61mm", "14mm", "128A", barcodeContent);

// 字体设置
LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
LODOP.SET_PRINT_STYLEA(0, "FontSize", 8);
LODOP.SET_PRINT_STYLEA(0, "Bold", 1);
```

### 不干胶标签参数
```javascript
// 纸张设置
LODOP.SET_PRINT_PAGESIZE(1, "72mm", "50mm", "");

// 边框
LODOP.ADD_PRINT_RECT("1mm", "1mm", "70mm", "48mm", 0, 1);

// 条形码
LODOP.ADD_PRINT_BARCODE("32mm", "16mm", "40mm", "15mm", "128A", orderNumber);
```

### 小票打印参数
```javascript
// 纸张设置（高度自适应）
LODOP.SET_PRINT_PAGESIZE(1, "80mm", "200mm", "");

// 文字样式
LODOP.SET_PRINT_STYLEA(0, "FontName", "微软雅黑");
LODOP.SET_PRINT_STYLEA(0, "FontSize", 12);
LODOP.SET_PRINT_STYLEA(0, "Alignment", 2); // 居中对齐
```

## API接口

### 核心打印函数
```javascript
// 水洗唛打印
lodopPrintWashLabels(orderId, selectedOption = 'all')

// 小票打印  
lodopPrintReceipt(orderId)

// 不干胶标签打印
lodopPrintStickyLabel(orderNumber)
```

### 统一打印接口
```javascript
// 带模式选择的打印接口
printReceiptWithModeSelection(orderId)
printWashLabelWithModeSelection(orderId, selectedOption)
printStickyLabelWithModeSelection(orderNumber)
```

## 故障排除

### 常见问题

#### 1. "打印控件未安装"
**解决方案**：
- 下载并安装对应系统版本的Lodop控件
- 安装完成后刷新浏览器页面
- 检查浏览器是否允许运行ActiveX控件

#### 2. "CLodop云打印服务未启动"
**解决方案**：
- 安装CLodop云打印服务
- 确保服务在后台运行
- 检查8000或18000端口是否被占用

#### 3. 打印布局错误
**解决方案**：
- 检查纸张尺寸设置是否正确
- 调整LODOP.SET_PRINT_PAGESIZE参数
- 验证字体大小和位置参数

#### 4. 条形码无法识别
**解决方案**：
- 确认条码类型设置正确（CODE128A）
- 检查条码内容格式
- 调整条码尺寸参数

### 调试方法

#### 1. 控制台日志
```javascript
// 开启调试模式
console.log('Lodop版本:', LODOP.VERSION);
console.log('打印数据:', orderData);
```

#### 2. 打印预览
- 所有Lodop打印都会先显示预览界面
- 可以在预览中检查布局和内容
- 支持缩放和页面导航

#### 3. 错误处理
```javascript
try {
    // 打印代码
} catch (error) {
    console.error('打印失败:', error);
    alert('打印失败: ' + error.message);
}
```

## 性能优化

### 1. 脚本加载优化
- LodopFuncs.js 在页面加载完成后自动检查控件状态
- 支持CLodop云打印双端口检测
- 自动降级到浏览器打印模式

### 2. 打印速度优化
- 预先初始化Lodop对象
- 缓存常用打印模板
- 批量处理多页面打印

### 3. 内存管理
- 打印完成后自动清理临时对象
- 避免重复创建打印实例
- 合理使用LODOP.NEWPAGE()

## 版本兼容性

### Lodop版本要求
- **最低版本**：*******
- **推荐版本**：最新稳定版
- **CLodop**：支持C-Lodop V3.8.9.7及以上版本

### 浏览器支持
- **Internet Explorer**：8.0及以上
- **Chrome**：41及以上
- **Firefox**：41及以上  
- **Edge**：全版本支持
- **Safari**：通过CLodop云打印支持

### 操作系统支持
- **Windows**：XP/Vista/7/8/10/11
- **Linux**：通过CLodop云打印支持
- **macOS**：通过CLodop云打印支持

## 技术支持

如遇到问题，请按以下步骤排查：

1. **检查控件安装**：确认Lodop或CLodop已正确安装
2. **验证网络连接**：确保可以访问Lodop相关资源
3. **查看控制台日志**：检查JavaScript错误信息
4. **测试打印预览**：使用预览功能验证布局
5. **联系技术支持**：提供详细的错误信息和系统环境

---

**注意**：首次使用Lodop打印时，浏览器可能会提示安全警告，请点击"允许"以启用打印功能。 