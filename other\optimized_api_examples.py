# 性能优化后的API实现示例
# 这些是建议的优化实现，需要替换app.py中对应的函数

from flask import jsonify, request, session
from sqlalchemy import func, case, text
from models import db, MallCustomer, MallProductDiscount, MallMonthlyBill, Customer, Order
import datetime

def get_mall_customers_optimized():
    """优化后的商场客户列表查询 - 解决N+1查询问题"""
    try:
        # 记录开始时间
        start_time = datetime.datetime.now()
        print(f"[PERF] 开始优化后的商场客户查询: {start_time}")

        # 获取查询参数
        search_term = request.args.get('search', '')
        status = request.args.get('status', '')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # 权限控制
        staff_area = session.get('staff_area')
        staff_role = session.get('staff_role')

        # 使用联表查询一次性获取所有需要的数据，解决N+1问题
        query = db.session.query(
            MallCustomer.id,
            MallCustomer.mall_name,
            MallCustomer.address,
            MallCustomer.phone,
            MallCustomer.contract_start_date,
            MallCustomer.contract_end_date,
            MallCustomer.billing_cycle,
            MallCustomer.contact_name,
            MallCustomer.contact_phone,
            MallCustomer.contact_position,
            MallCustomer.status,
            MallCustomer.remarks,
            MallCustomer.overall_discount_rate,
            MallCustomer.area,
            MallCustomer.created_at,
            MallCustomer.updated_at,
            # 使用聚合函数一次性计算折扣数量
            func.count(func.distinct(MallProductDiscount.id)).label('discount_count'),
            # 使用条件聚合计算未付款账单数量
            func.sum(
                case([(MallMonthlyBill.payment_status == '未付款', 1)], else_=0)
            ).label('unpaid_bills_count')
        ).outerjoin(
            MallProductDiscount, 
            MallCustomer.id == MallProductDiscount.mall_customer_id
        ).outerjoin(
            MallMonthlyBill, 
            MallCustomer.id == MallMonthlyBill.mall_customer_id
        ).group_by(MallCustomer.id)

        # 应用权限过滤 - 使用索引优化
        if staff_role != 'admin' and staff_area != '总部' and staff_area:
            query = query.filter(MallCustomer.area == staff_area)
            print(f"[PERF] 应用区域过滤: {staff_area}")

        # 应用搜索过滤 - 使用索引优化
        if search_term:
            # 使用全文索引进行搜索（如果可用）
            query = query.filter(MallCustomer.mall_name.like(f'%{search_term}%'))
            print(f"[PERF] 应用搜索过滤: {search_term}")

        # 应用状态过滤 - 使用索引优化
        if status:
            query = query.filter(MallCustomer.status == status)
            print(f"[PERF] 应用状态过滤: {status}")

        # 排序优化 - 使用索引
        query = query.order_by(MallCustomer.mall_name)

        # 执行分页查询
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        print(f"[PERF] 查询执行完成，总记录数: {pagination.total}")

        # 构建返回结果
        result = []
        for row in pagination.items:
            # 计算付款状态
            payment_status = '正常' if row.unpaid_bills_count == 0 else '有欠款'
            
            result.append({
                'id': row.id,
                'mall_name': row.mall_name,
                'address': row.address or '',
                'phone': row.phone or '',
                'contract_start_date': row.contract_start_date.isoformat() if row.contract_start_date else None,
                'contract_end_date': row.contract_end_date.isoformat() if row.contract_end_date else None,
                'billing_cycle': row.billing_cycle,
                'contact_name': row.contact_name or '',
                'contact_phone': row.contact_phone or '',
                'contact_position': row.contact_position or '',
                'status': row.status,
                'remarks': row.remarks or '',
                'overall_discount_rate': row.overall_discount_rate,
                'area': row.area or '',
                'created_at': row.created_at.isoformat(),
                'updated_at': row.updated_at.isoformat(),
                'discount_count': row.discount_count,
                'payment_status': payment_status,
                'unpaid_bills_count': row.unpaid_bills_count
            })

        # 计算执行时间
        end_time = datetime.datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        print(f"[PERF] 优化后商场客户查询完成，耗时: {execution_time}秒，返回 {len(result)} 条记录")

        return jsonify({
            'customers': result,
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page,
            'per_page': per_page,
            'execution_time': execution_time,
            'optimization': 'enabled'  # 标识使用了优化版本
        })

    except Exception as e:
        import traceback
        print(f"[ERROR] 优化后商场客户查询出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500


def get_members_optimized():
    """优化后的会员列表查询 - 优化排序和筛选"""
    try:
        # 记录开始时间
        start_time = datetime.datetime.now()
        print(f"[PERF] 开始优化后的会员查询: {start_time}")

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '')
        status = request.args.get('status', 'all')

        # 预计算活跃状态阈值，避免每次查询时计算
        active_cutoff_date = datetime.datetime.now() - datetime.timedelta(days=180)

        # 构建基础查询
        query = Customer.query

        # 应用搜索过滤 - 使用索引优化
        if search:
            # 使用OR查询，确保两个字段都有索引
            query = query.filter(db.or_(
                Customer.name.like(f'%{search}%'),
                Customer.phone.like(f'%{search}%')
            ))

        # 应用状态过滤 - 使用索引优化的日期比较
        if status == 'active':
            query = query.filter(Customer.updated_at >= active_cutoff_date)
        elif status == 'inactive':
            query = query.filter(Customer.updated_at < active_cutoff_date)

        # 获取总数
        total = query.count()
        print(f"[PERF] 符合条件的会员总数: {total}")

        # 如果没有记录，提前返回
        if total == 0:
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            print(f"[PERF] 会员查询完成，耗时: {execution_time}秒，无匹配记录")
            
            return jsonify({
                'members': [],
                'total': 0,
                'pages': 0,
                'current_page': page,
                'total_pages': 0,
                'execution_time': execution_time,
                'optimization': 'enabled'
            })

        # 执行分页查询 - 使用索引优化的排序
        pagination = query.order_by(Customer.updated_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # 构建返回结果，预计算活跃状态
        result = []
        for member in pagination.items:
            is_active = member.updated_at >= active_cutoff_date
            
            result.append({
                'id': member.id,
                'name': member.name,
                'phone': member.phone,
                'balance': member.balance or 0.0,
                'gift_balance': member.gift_balance or 0.0,
                'total_balance': member.total_balance,
                'discount_rate': member.discount_rate or 1.0,
                'discount_expiry': member.discount_expiry.isoformat() if member.discount_expiry else None,
                'created_at': member.created_at.isoformat(),
                'updated_at': member.updated_at.isoformat(),
                'is_active': is_active,
                'is_mall_customer': member.is_mall_customer or False
            })

        # 计算执行时间
        end_time = datetime.datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        print(f"[PERF] 优化后会员查询完成，耗时: {execution_time}秒，返回 {len(result)} 条记录")

        return jsonify({
            'members': result,
            'total': total,
            'pages': pagination.pages,
            'current_page': page,
            'total_pages': pagination.pages,
            'execution_time': execution_time,
            'optimization': 'enabled'
        })

    except Exception as e:
        import traceback
        print(f"[ERROR] 优化后会员查询出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500


def customer_history_optimized():
    """优化后的历史订单查询 - 优化复合查询条件"""
    try:
        # 记录开始时间
        start_time = datetime.datetime.now()
        print(f"[PERF] 开始优化后的历史订单查询: {start_time}")

        # 获取查询参数
        phone = request.args.get('phone', '').strip()
        name = request.args.get('name', '').strip()
        date = request.args.get('date', '').strip()
        order_number = request.args.get('order_number', '').strip()
        status = request.args.get('status', '').strip()
        operator = request.args.get('operator', '').strip()
        payment_method = request.args.get('payment_method', '').strip()
        all_records = request.args.get('all', '').lower() == 'true'
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 5, type=int)

        # 权限控制
        staff_name = session.get('staff_name')
        staff_role = session.get('staff_role')
        staff_area = session.get('staff_area')

        # 使用联表查询优化客户信息获取
        orders_query = db.session.query(Order, Customer).join(
            Customer, Order.customer_id == Customer.id
        )

        has_filter = False

        # 应用各种筛选条件 - 使用索引优化
        if phone:
            orders_query = orders_query.filter(Customer.phone.like(f'%{phone}%'))
            has_filter = True

        if name:
            orders_query = orders_query.filter(Customer.name.like(f'%{name}%'))
            has_filter = True

        if order_number:
            orders_query = orders_query.filter(Order.order_number.like(f'%{order_number}%'))
            has_filter = True

        if status:
            orders_query = orders_query.filter(Order.status == status)
            has_filter = True

        if operator:
            orders_query = orders_query.filter(Order.operator.like(f'%{operator}%'))
            has_filter = True

        if payment_method:
            orders_query = orders_query.filter(Order.payment_method == payment_method)
            has_filter = True

        if date:
            try:
                search_date = datetime.datetime.strptime(date, '%Y-%m-%d')
                next_day = search_date + datetime.timedelta(days=1)
                # 使用索引优化的日期范围查询
                orders_query = orders_query.filter(
                    Order.created_at >= search_date,
                    Order.created_at < next_day
                )
                has_filter = True
            except Exception as e:
                print(f"[ERROR] 日期格式错误: {str(e)}")
                return jsonify({'error': f'日期格式错误: {str(e)}'}), 400

        # 权限过滤优化
        if staff_role != 'manager':
            orders_query = orders_query.filter(Order.operator == staff_name)
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            # 使用子查询优化区域权限过滤
            area_staff_subquery = db.session.query(Staff.name).filter(Staff.area == staff_area).subquery()
            orders_query = orders_query.filter(Order.operator.in_(area_staff_subquery))

        # 检查筛选条件
        if not has_filter and not all_records:
            return jsonify({'error': '请提供查询条件或设置all=true参数'}), 400

        # 使用索引优化的排序
        orders_query = orders_query.order_by(Order.created_at.desc())

        # 计算总数
        total_orders = orders_query.count()
        print(f"[PERF] 符合条件的订单总数: {total_orders}")

        if total_orders == 0:
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            print(f"[PERF] 历史订单查询完成，耗时: {execution_time}秒，未找到记录")
            return jsonify({'found': False})

        # 分页查询
        pagination = orders_query.paginate(page=page, per_page=per_page, error_out=False)

        # 构建返回结果
        orders_data = []
        for order, customer in pagination.items:
            orders_data.append({
                'id': order.id,
                'order_number': order.order_number,
                'customer_name': customer.name,
                'customer_phone': customer.phone,
                'total_amount': order.total_amount,
                'payment_method': order.payment_method,
                'payment_status': order.payment_status,
                'status': order.status,
                'operator': order.operator,
                'created_at': order.created_at.isoformat(),
                'address': order.address or '',
                'is_mall_order': order.is_mall_order or False
            })

        # 计算执行时间
        end_time = datetime.datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        print(f"[PERF] 优化后历史订单查询完成，耗时: {execution_time}秒，返回 {len(orders_data)} 条记录")

        return jsonify({
            'found': True,
            'orders': orders_data,
            'total': total_orders,
            'pages': pagination.pages,
            'current_page': page,
            'per_page': per_page,
            'execution_time': execution_time,
            'optimization': 'enabled'
        })

    except Exception as e:
        import traceback
        print(f"[ERROR] 优化后历史订单查询出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': f'查询失败: {str(e)}'}), 500
