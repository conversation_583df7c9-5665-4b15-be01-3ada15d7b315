"""
业务相关验证器模块
包含特定业务逻辑的验证功能
"""
from typing import Dict, Any, List
from common.validators import DataValidator, ValidationError
from models import MallCustomer, MallProductDiscount, Customer, Order
import datetime


class MallCustomerValidator:
    """商场客户验证器"""
    
    @staticmethod
    def validate_create_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证创建商场客户的数据
        
        Args:
            data: 客户数据
            
        Returns:
            Dict[str, Any]: 验证后的数据
            
        Raises:
            ValidationError: 验证失败时
        """
        validated_data = {}
        
        # 验证商场名称
        validated_data['mall_name'] = DataValidator.validate_mall_name(data.get('mall_name', ''))
        
        # 验证联系电话（可选）
        if data.get('phone'):
            if not DataValidator.validate_phone(data['phone']):
                raise ValidationError("请输入有效的手机号码")
            validated_data['phone'] = data['phone']
        
        # 验证合同日期
        if data.get('contract_start_date') and data.get('contract_end_date'):
            start_date, end_date = DataValidator.validate_date_range(
                data['contract_start_date'],
                data['contract_end_date']
            )
            validated_data['contract_start_date'] = start_date
            validated_data['contract_end_date'] = end_date
        
        # 验证折扣率
        if data.get('overall_discount_rate'):
            rate = float(data['overall_discount_rate'])
            DataValidator.validate_discount_rate(rate)
            validated_data['overall_discount_rate'] = rate
        
        # 其他字段直接传递
        for field in ['address', 'billing_cycle', 'contact_name', 'contact_phone', 
                     'contact_position', 'status', 'remarks', 'area']:
            if field in data:
                validated_data[field] = data[field]
        
        return validated_data
    
    @staticmethod
    def validate_name_uniqueness(mall_name: str, area: str, exclude_id: int = None) -> None:
        """
        验证商场名称在区域内的唯一性
        
        Args:
            mall_name: 商场名称
            area: 区域
            exclude_id: 排除的ID（用于更新时）
            
        Raises:
            ValidationError: 名称重复时
        """
        query = MallCustomer.query.filter_by(mall_name=mall_name, area=area)
        if exclude_id:
            query = query.filter(MallCustomer.id != exclude_id)
        
        if query.first():
            raise ValidationError(f'商场品牌名称"{mall_name}"在区域"{area}"已存在，请使用不同的名称')


class DiscountValidator:
    """折扣验证器"""
    
    @staticmethod
    def validate_product_discount_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证产品折扣数据
        
        Args:
            data: 折扣数据
            
        Returns:
            Dict[str, Any]: 验证后的数据
        """
        # 验证必填字段
        required_fields = ['product_name', 'product_type', 'discount_rate', 'effective_date']
        DataValidator.validate_required_fields(data, required_fields)
        
        # 验证折扣率
        discount_rate = float(data['discount_rate'])
        DataValidator.validate_discount_rate(discount_rate)
        
        # 验证产品名称长度
        product_name = data['product_name'].strip()
        if len(product_name) > 100:
            raise ValidationError("产品名称不能超过100个字符")
        
        # 验证日期
        effective_date = datetime.datetime.strptime(data['effective_date'], '%Y-%m-%d').date()
        expiry_date = None
        
        if data.get('expiry_date'):
            expiry_date = datetime.datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
            if expiry_date <= effective_date:
                raise ValidationError("失效日期必须晚于生效日期")
        
        return {
            'product_name': product_name,
            'product_type': data['product_type'],
            'discount_rate': discount_rate,
            'effective_date': effective_date,
            'expiry_date': expiry_date,
            'change_reason': data.get('change_reason', '定期调整')
        }
    
    @staticmethod
    def validate_service_discount_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证会员服务折扣数据
        
        Args:
            data: 折扣数据
            
        Returns:
            Dict[str, Any]: 验证后的数据
        """
        # 验证必填字段
        required_fields = ['service_type', 'discount_rate', 'valid_from', 'valid_to']
        DataValidator.validate_required_fields(data, required_fields)
        
        # 验证服务类型
        DataValidator.validate_service_type(data['service_type'])
        
        # 验证折扣率
        discount_rate = float(data['discount_rate'])
        DataValidator.validate_discount_rate(discount_rate)
        
        # 验证日期范围
        valid_from, valid_to = DataValidator.validate_date_range(
            data['valid_from'],
            data['valid_to']
        )
        
        return {
            'service_type': data['service_type'],
            'discount_rate': discount_rate,
            'valid_from': valid_from,
            'valid_to': valid_to,
            'is_active': data.get('is_active', True)
        }


class OrderValidator:
    """订单验证器"""
    
    @staticmethod
    def validate_order_status_change(old_status: str, new_status: str, user_role: str) -> None:
        """订单状态流转校验已取消，直接通过"""
        return
    
    @staticmethod
    def validate_refund_amount(order_amount: float, refund_amount: float) -> None:
        """
        验证退款金额
        
        Args:
            order_amount: 订单金额
            refund_amount: 退款金额
            
        Raises:
            ValidationError: 退款金额无效时
        """
        if refund_amount <= 0:
            raise ValidationError("退款金额必须大于0")
        
        if refund_amount > order_amount:
            raise ValidationError("退款金额不能超过订单金额")


class UserValidator:
    """用户验证器"""
    
    @staticmethod
    def validate_user_data(data: Dict[str, Any], is_new_user: bool = False) -> Dict[str, Any]:
        """
        验证用户数据
        
        Args:
            data: 用户数据
            is_new_user: 是否为新用户
            
        Returns:
            Dict[str, Any]: 验证后的数据
        """
        # 验证必填字段
        required_fields = ['username', 'name', 'role']
        DataValidator.validate_required_fields(data, required_fields)
        
        # 验证用户名长度
        username = data['username'].strip()
        if len(username) < 3 or len(username) > 50:
            raise ValidationError("用户名长度必须在3-50个字符之间")
        
        # 验证真实姓名
        name = data['name'].strip()
        if len(name) < 2 or len(name) > 50:
            raise ValidationError("姓名长度必须在2-50个字符之间")
        
        # 验证角色
        valid_roles = ['staff', 'manager', 'admin']
        if data['role'] not in valid_roles:
            raise ValidationError(f"无效的角色，支持的角色: {', '.join(valid_roles)}")
        
        # 验证手机号（可选）
        if data.get('phone'):
            if not DataValidator.validate_phone(data['phone']):
                raise ValidationError("请输入有效的手机号码")
        
        # 验证密码
        if data.get('password'):
            DataValidator.validate_password(data['password'], is_new_user)
        
        validated_data = {
            'username': username,
            'name': name,
            'role': data['role'],
            'area': data.get('area', ''),
            'is_active': data.get('is_active', True)
        }
        
        if data.get('phone'):
            validated_data['phone'] = data['phone']
        
        if data.get('password'):
            validated_data['password'] = data['password']
        
        return validated_data 