"""
权限验证工具模块
提供统一的权限检查功能，避免重复代码
"""
from typing import Optional, List, Dict, Any, Callable
from functools import wraps
from flask import session, jsonify, redirect, url_for, flash, request
import functools


class PermissionError(Exception):
    """权限异常"""
    def __init__(self, message: str):
        self.message = message
        super().__init__(message)


class PermissionChecker:
    """
    权限检查器，提供统一的权限验证方法
    """
    
    @staticmethod
    def get_current_user_info() -> Dict[str, Any]:
        """获取当前用户信息"""
        return {
            'staff_role': session.get('staff_role', ''),
            'staff_name': session.get('staff_name', ''),
            'staff_area': session.get('staff_area', ''),
            'staff_id': session.get('staff_id', '')
        }
    
    @staticmethod
    def is_logged_in() -> bool:
        """检查用户是否登录"""
        return 'staff_id' in session and session.get('staff_id') is not None
    
    @staticmethod
    def is_admin() -> bool:
        """
        检查用户是否为超级管理员
        
        返回:
            bool: 如果是admin角色则返回True
        """
        return session.get('staff_role') == 'admin'
    
    @staticmethod
    def is_manager() -> bool:
        """
        检查用户是否为管理员（manager角色）
        
        返回:
            bool: 如果是manager角色则返回True
        """
        return session.get('staff_role') == 'manager'
    
    @staticmethod
    def is_admin_or_manager() -> bool:
        """
        检查用户是否为管理员（admin或manager角色）
        
        返回:
            bool: 如果是admin或manager角色则返回True
        """
        staff_role = session.get('staff_role')
        return staff_role in ['admin', 'manager']
    
    @staticmethod
    def has_headquarters_access() -> bool:
        """检查用户是否有总部权限"""
        staff_role = session.get('staff_role', '')
        staff_area = session.get('staff_area', '')
        return staff_role == 'admin' or staff_area == '总部'
    
    @staticmethod
    def can_access_area_data(target_area: str) -> bool:
        """
        检查用户是否可以访问特定区域的数据
        
        参数:
            target_area: 目标区域名称
            
        返回:
            bool: 是否有权限访问
        """
        staff_role = session.get('staff_role', '')
        staff_area = session.get('staff_area', '')
        
        # admin和总部用户可以访问所有区域
        if staff_role == 'admin' or staff_area == '总部':
            return True
            
        # manager只能访问自己区域的数据
        if staff_role == 'manager':
            return staff_area == target_area
            
        # 普通员工只能访问自己区域的数据
        return staff_area == target_area
    
    @staticmethod
    def get_accessible_areas() -> List[str]:
        """获取用户可访问的区域列表"""
        staff_role = session.get('staff_role', '')
        staff_area = session.get('staff_area', '')
        
        if staff_role == 'admin' or staff_area == '总部':
            # admin和总部用户可以访问所有区域
            from models import Staff
            areas = Staff.query.with_entities(Staff.area).distinct().all()
            return [area[0] for area in areas if area[0]]
        else:
            # 其他用户只能访问自己的区域
            return [staff_area] if staff_area else []


def require_admin_permission(f: Callable) -> Callable:
    """
    要求admin权限的装饰器
    """
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        if not PermissionChecker.is_logged_in():
            if request.is_json:
                return jsonify({'error': '请先登录'}), 401
            flash('请先登录', 'error')
            return redirect(url_for('auth.login'))
        
        if not PermissionChecker.is_admin():
            if request.is_json:
                return jsonify({'error': '权限不足，需要超级管理员权限'}), 403
            flash('权限不足，需要超级管理员权限', 'error')
            return redirect(url_for('index'))
        
        return f(*args, **kwargs)
    return wrapper


def require_manager_permission(f: Callable) -> Callable:
    """
    要求manager权限的装饰器
    """
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        if not PermissionChecker.is_logged_in():
            if request.is_json:
                return jsonify({'error': '请先登录'}), 401
            flash('请先登录', 'error')
            return redirect(url_for('auth.login'))
        
        if not PermissionChecker.is_manager():
            if request.is_json:
                return jsonify({'error': '权限不足，需要管理员权限'}), 403
            flash('权限不足，需要管理员权限', 'error')
            return redirect(url_for('index'))
        
        return f(*args, **kwargs)
    return wrapper


def require_admin_or_manager_permission(f: Callable) -> Callable:
    """
    要求admin或manager权限的装饰器
    """
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        if not PermissionChecker.is_logged_in():
            if request.is_json:
                return jsonify({'error': '请先登录'}), 401
            flash('请先登录', 'error')
            return redirect(url_for('auth.login'))
        
        if not PermissionChecker.is_admin_or_manager():
            if request.is_json:
                return jsonify({'error': '权限不足，需要管理员权限'}), 403
            flash('权限不足，需要管理员权限', 'error')
            return redirect(url_for('index'))
        
        return f(*args, **kwargs)
    return wrapper


def require_area_permission(f: Callable) -> Callable:
    """
    要求区域权限的装饰器
    """
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        if not PermissionChecker.is_logged_in():
            if request.is_json:
                return jsonify({'error': '请先登录'}), 401
            flash('请先登录', 'error')
            return redirect(url_for('auth.login'))
        
        # 检查是否有总部权限
        if not PermissionChecker.has_headquarters_access():
            if request.is_json:
                return jsonify({'error': '权限不足，需要总部权限'}), 403
            flash('权限不足，需要总部权限', 'error')
            return redirect(url_for('index'))
        
        return f(*args, **kwargs)
    return wrapper 