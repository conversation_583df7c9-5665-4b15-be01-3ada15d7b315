from flask import Blueprint, request, jsonify, session
import datetime
import json

from blueprints.auth import login_required
from models import db, Order, Customer, Clothing, ClothingPhoto, MallCustomer, MallMonthlyBill, Staff
from utils import update_customer_balance, save_base64_image, generate_order_number

bp = Blueprint('order', __name__)


# 从会员管理模块导入折扣计算函数
from blueprints.member import calculate_member_discount


@bp.route('/submit_order', methods=['POST'], endpoint='submit_order')
def submit_order():
    """提交订单

    处理新订单的创建，包括普通客户订单和商场客户订单

    请求体:
        JSON对象，包含订单信息、客户信息和衣物信息

    返回:
        JSON: 包含订单创建结果
    """
    # 检查用户是否登录，如果未登录，返回JSON格式错误
    if 'staff_id' not in session:
        return jsonify({'error': '未登录或会话已过期', 'redirect': '/login'}), 401

    try:
        print("接收到订单提交请求")
        # 获取请求数据
        if not request.is_json:
            print("请求不是JSON格式")
            return jsonify({'error': '请求必须是JSON格式'}), 400

        data = request.json
        print(f"接收到的数据: {data}")

        # 检查是否是商场客户订单
        is_mall_customer = data.get('is_mall_customer', False)

        # 获取客户信息 - 修改为直接从data中获取，而不是从customer对象中获取
        payment_method = data.get('payment_method')
        clothing_items = data.get('items', [])  # 修正字段名称，前端发送的是items而不是clothing_items

        # 初始化mall_customer_id变量
        mall_customer_id = None

        if is_mall_customer:
            # 商场客户订单处理
            mall_customer_id = data.get('mall_customer_id')
            customer_name = data.get('mall_customer_name') or data.get('customer_name')
            customer_phone = data.get('mall_customer_phone') or data.get('customer_phone', '')
            address = data.get('address', '')  # 商场客户通常不需要地址信息，但仍然获取

            # 修改：完善商场客户信息的验证，确保姓名和电话都有提供
            if not mall_customer_id:
                print("缺少商场客户ID")
                return jsonify({'error': '缺少商场客户ID'}), 400

            if not customer_name:
                print("缺少商场客户姓名")
                return jsonify({'error': '请提供商场客户姓名'}), 400

            if not customer_phone:
                print("缺少商场客户电话")
                return jsonify({'error': '请提供商场客户电话'}), 400

            # 获取商场客户信息
            mall_customer = MallCustomer.query.get(mall_customer_id)
            if not mall_customer:
                print(f"找不到商场客户: ID {mall_customer_id}")
                return jsonify({'error': '找不到指定的商场客户'}), 404

            print(f"商场客户订单: {mall_customer.mall_name} (ID: {mall_customer.id})")

            # 为了兼容性，创建或更新普通客户记录
            customer = Customer.query.filter_by(phone=customer_phone).first()
            if not customer:
                print(f"为商场客户创建关联的普通客户记录: {customer_name}")
                customer = Customer(
                    name=customer_name,
                    phone=customer_phone,
                    balance=0.0,
                    is_mall_customer=True,
                    mall_customer_id=mall_customer_id
                )
                db.session.add(customer)
                db.session.flush()  # 获取新客户ID

                is_new_customer = True
            else:
                print(f"找到商场客户关联的普通客户记录: {customer.name}, ID: {customer.id}")
                # 更新mall_customer_id和is_mall_customer字段
                customer.is_mall_customer = True
                customer.mall_customer_id = mall_customer_id
                is_new_customer = False

        else:
            # 普通客户订单处理
            customer_name = data.get('customer_name')
            customer_phone = data.get('customer_phone')
            address = data.get('address', '')

            if not customer_name or not customer_phone:
                print("缺少必要的客户信息")
                return jsonify({'error': '缺少必要的客户信息'}), 400

            # 检查客户是否已存在
            customer = Customer.query.filter_by(phone=customer_phone).first()
            if not customer:
                print(f"创建新客户: {customer_name}")
                customer = Customer(name=customer_name, phone=customer_phone, balance=0.0)
                db.session.add(customer)
                db.session.flush()  # 获取新客户ID
            else:
                print(f"找到现有客户: {customer.name}, ID: {customer.id}")

        # 计算原始订单总金额
        original_total_amount = sum(float(item.get('price', 0)) for item in clothing_items)

        # 计算会员折扣（如果是普通客户且不是商场订单）
        member_discount_info = {'total_discount': 0, 'discounted_items': []}
        if not is_mall_customer and customer:
            member_discount_info = calculate_member_discount(customer.id, clothing_items)
            print(f"会员折扣信息: {member_discount_info}")

        # 应用会员折扣到衣物价格
        for i, item in enumerate(clothing_items):
            if i < len(member_discount_info['discounted_items']):
                discount_item = member_discount_info['discounted_items'][i]
                # 保存原始价格
                item['original_price'] = discount_item['original_price']
                # 更新为折扣后价格
                item['price'] = discount_item['discounted_price']
                item['discount_amount'] = discount_item['discount_amount']
                item['discount_source'] = discount_item['discount_source']

        # 计算最终订单总金额
        total_amount = float(data.get('total_amount', 0))
        if total_amount <= 0:
            # 使用折扣后的价格计算总金额
            total_amount = sum(float(item.get('price', 0)) for item in clothing_items)

        # 记录折扣金额
        discount_amount = member_discount_info['total_discount'] + data.get('discount_amount', 0)

        print(f"原始总金额: {original_total_amount}, 订单总金额: {total_amount}, 折扣金额: {discount_amount}")

        # 如果是余额支付，检查余额是否足够
        if payment_method == '余额' and not is_mall_customer:  # 商场客户通常不使用余额支付
            total_balance = customer.total_balance
            if total_balance < total_amount:
                print(f"余额不足: {total_balance} < {total_amount}")
                return jsonify({'error': '账户余额不足'}), 400

            # 更新客户余额（优先使用赠送余额）
            new_balance = update_customer_balance(customer, total_amount, is_recharge=False)
            print(f"更新后的总余额: {new_balance}")

        # 创建订单
        order_number = generate_order_number()
        print(f"生成订单号: {order_number}")

        # 商场客户订单通常是月结
        payment_status = "已付款"
        if is_mall_customer and payment_method == "月结":
            payment_status = "未付款"  # 月结的商场客户订单初始状态为未付款
        elif payment_method == "未付款":
            payment_status = "未付款"

        # 如果订单状态是已付款，设置支付时间
        payment_time = None
        if payment_status == "已付款":
            payment_time = datetime.datetime.now()

        order = Order(
            order_number=order_number,
            customer_id=customer.id,
            total_amount=total_amount,
            payment_method=payment_method,
            payment_status=payment_status,
            payment_time=payment_time,
            address=address,
            operator=session.get('staff_name', '未知'),  # 添加操作员信息
            is_mall_order=is_mall_customer,
            mall_customer_id=customer.mall_customer_id if is_mall_customer else None,
            discount_amount=discount_amount,
            status='门店已分拣'  # 设置初始状态为"门店已分拣"
        )
        db.session.add(order)
        db.session.flush()  # 获取订单ID
        print(f"创建订单成功, ID: {order.id}")

        # 如果是商场客户订单，更新或创建月度账单
        if is_mall_customer:
            # 获取当前年月
            current_date = datetime.datetime.now()
            bill_year_month = current_date.strftime('%Y-%m')

            # 计算当月的开始和结束日期
            import calendar
            _, last_day = calendar.monthrange(current_date.year, current_date.month)
            bill_start_date = datetime.date(current_date.year, current_date.month, 1)
            bill_end_date = datetime.date(current_date.year, current_date.month, last_day)

            # 查找当月账单
            monthly_bill = MallMonthlyBill.query.filter_by(
                mall_customer_id=customer.mall_customer_id,
                bill_year_month=bill_year_month
            ).first()

            # 计算订单的原始金额（折扣前）
            original_total_amount = 0
            for item_data in clothing_items:
                item_original_price = item_data.get('original_price', item_data.get('price', 0))
                item_quantity = item_data.get('quantity', 1)
                original_total_amount += float(item_original_price) * item_quantity

            # 如果没有原始价格信息，使用订单总金额作为原始金额
            if original_total_amount == 0:
                original_total_amount = total_amount

            if monthly_bill:
                # 更新现有账单
                monthly_bill.order_count += 1
                monthly_bill.original_amount += original_total_amount
                monthly_bill.total_amount += total_amount
                monthly_bill.discount_amount += discount_amount
                monthly_bill.actual_amount = monthly_bill.total_amount
                print(f"更新商场客户月度账单: ID {monthly_bill.id}, 原始金额: {monthly_bill.original_amount}, 实际金额: {monthly_bill.actual_amount}")
            else:
                # 创建新账单
                monthly_bill = MallMonthlyBill(
                    mall_customer_id=customer.mall_customer_id,
                    bill_year_month=bill_year_month,
                    bill_start_date=bill_start_date,
                    bill_end_date=bill_end_date,
                    order_count=1,
                    original_amount=original_total_amount,
                    total_amount=total_amount,
                    discount_amount=discount_amount,
                    actual_amount=total_amount,
                    payment_status="未付款"
                )
                db.session.add(monthly_bill)
                print(f"创建商场客户月度账单: {bill_year_month}, 原始金额: {original_total_amount}, 实际金额: {total_amount}")

        # 处理每件衣物
        for i, item_data in enumerate(clothing_items):
            # 提取衣物折扣信息（如果有）
            original_price = item_data.get('original_price')
            discount_rate = item_data.get('discount_rate')

            # 创建衣物记录
            print(f"处理衣物 {i+1}: {item_data.get('name', '')}")

            # 解析服务和特殊需求
            services = item_data.get('serviceTypes', ['洗衣'])
            special_requirements = item_data.get('specialRequirements', {})

            clothing = Clothing(
                name=item_data.get('name', ''),
                color=item_data.get('color', ''),
                services=json.dumps(services),
                special_requirements=json.dumps(special_requirements),
                price=float(item_data.get('price', 0)),
                quantity=int(item_data.get('quantity', 1)),  # 添加数量字段支持
                remarks=item_data.get('remarks', ''),
                flaw=item_data.get('flaw', ''),  # 添加瑕疵字段支持
                barcode=f"{order_number}-{str(i+1).zfill(2)}",  # 订单号-序号
                customer_id=customer.id,
                order_id=order.id,
                original_price=original_price,
                discount_rate=discount_rate,
                is_mall_order=is_mall_customer
            )
            db.session.add(clothing)
            db.session.flush()  # 获取衣物ID
            print(f"衣物记录创建成功, ID: {clothing.id}")

            # 处理衣物照片
            photos = item_data.get('photos', [])
            print(f"衣物照片数量: {len(photos)}")
            for j, photo_data in enumerate(photos):
                # 保存图片
                image_path = save_base64_image(photo_data, customer_phone, j)
                if image_path:
                    # 创建照片记录
                    photo = ClothingPhoto(
                        clothing_id=clothing.id,
                        image_path=image_path
                    )
                    db.session.add(photo)
                    print(f"添加照片 {j+1} 成功")

        print("提交数据库事务...")
        db.session.commit()
        print("数据库事务提交成功")

        # 构建响应数据
        response_data = {
            'success': True,
            'order_id': order.id,
            'order_number': order.order_number,
            'total_amount': order.total_amount
        }

        # 如果是余额支付，返回新余额
        if payment_method == '余额' and not is_mall_customer:
            response_data['newBalance'] = new_balance

        print(f"返回响应: {response_data}")
        return jsonify(response_data)
    except Exception as e:
        print(f"订单提交出错: {str(e)}")
        import traceback
        traceback.print_exc()
        db.session.rollback()
        # 确保返回JSON格式的错误信息
        return jsonify({'error': f'订单提交出错: {str(e)}'}), 500 